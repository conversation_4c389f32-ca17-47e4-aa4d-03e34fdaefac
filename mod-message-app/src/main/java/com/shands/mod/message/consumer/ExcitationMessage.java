package com.shands.mod.message.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.mapper.excitation.ModMessageExcitationDao;
import com.shands.mod.dao.mapper.hs.ModMessageLogMapper;
import com.shands.mod.dao.mapper.syncuc.ModUserDao;
import com.shands.mod.dao.mapper.workorder.NwWorkOrderMapper;
import com.shands.mod.dao.mapper.workorder.NwWorkOrderScheduleMapper;
import com.shands.mod.dao.model.excitation.ModMessageExcitation;
import com.shands.mod.dao.model.hs.enums.MobileTypeEnum;
import com.shands.mod.dao.model.req.ExcitationH5Req;
import com.shands.mod.dao.model.req.ExcitationMobielH5Req;
import com.shands.mod.dao.model.req.ExcitationNomoReq;
import com.shands.mod.dao.model.req.ExcitationRemindReq;
import com.shands.mod.dao.model.req.UmMessageReq;
import com.shands.mod.dao.model.syncuc.ModUser;
import com.shands.mod.dao.model.v0701.pojo.ModMessageLog;
import com.shands.mod.dao.model.workorder.bo.SendWorkTaskBo;
import com.shands.mod.dao.model.workorder.po.NwWorkOrder;
import com.shands.mod.dao.model.workorder.po.NwWorkOrderSchedule;
import com.shands.mod.message.enums.MessageStateEnum;
import com.shands.mod.message.enums.MessageTypeEnum;
import com.shands.mod.message.service.ISmsService;
import com.shands.mod.message.service.UmMessagePushService;
import com.shands.mod.rabbitmq.constant.ExcitationMqConstant;
import com.shands.mod.rabbitmq.constant.QualityMqConstant;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import com.shands.mod.rabbitmq.sender.RabbitMqSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/7/21
 * @desc 会员激励信息发送消费者
 */

@Slf4j
@Component
public class ExcitationMessage {

  @Resource
  private ModUserDao modUserDao;

  @Resource
  private ModMessageLogMapper modMessageLogMapper;

  @Resource
  private ModMessageExcitationDao modMessageExcitationDao;

  @Resource
  private ISmsService smsService;

  @Resource
  private NwWorkOrderMapper nwWorkOrderMapper;

  @Resource
  private NwWorkOrderScheduleMapper nwWorkOrderScheduleMapper;

  @Resource
  private UmMessagePushService umMessagePushService;

  @RabbitListener(queues = ExcitationMqConstant.QUEUE_DIRECT_MESSAGE_YM_EXCITATION)
  @RabbitHandler
  public void sendExcitationWithRabbit(Message message) {
    log.info("[会员激励信息-奖励提醒][消息提醒][RabbitMq][上送参数:{}]", message);
    try {
      String messageContent = new String(message.getBody(), StandardCharsets.UTF_8.name());
      this.sendExcitation(messageContent);
    } catch (Exception e) {
      log.error("[会员激励信息-奖励提醒][解析参数异常]", e);
    }
  }

  public void sendExcitation(String messageContent) {

    try {
      if (messageContent == null) {
        return;
      }

      log.info("[会员激励信息-奖励提醒][消息提醒][上送参数:{}]", messageContent);
      JSONObject jsonObject = JSONObject.parseObject(messageContent);
      if (jsonObject == null) {
        return;
      }

      String data = jsonObject.getString("remindReq");
      ExcitationRemindReq remindReq = JSONObject.parseObject(data, ExcitationRemindReq.class);

      if (remindReq == null) {
        return;
      }

      //保存奖励提醒详情内容
      String expendNo = String.valueOf(this.saveMessage(remindReq));
      String mobile = remindReq.getMoible();

      ModUser modUser = modUserDao.queryByYmMessage(mobile);

      if (modUser == null) {
        log.info("[消息中心][APP消息发送失败][信息接收人查询为空]");
        return;
      }

      //保存消息发送记录
      int messageId = this.saveMessageLog(MessageTypeEnum.MESSAGE_TYPE_APP, remindReq.getTitle(),
          remindReq.getText(),
          MessageTypeEnum.MESSAGE_TYPE_EXCITATION_REMIND.getTypeCode(), expendNo,
          MessageTypeEnum.REJT_TYPE_STAFF, String.valueOf(modUser.getId()), remindReq.getTopName());

      //组装友盟推送消息体
      UmMessageReq umMessageReq = this
          .getSendObject(modUser.getDeviceId(), modUser.getDeviceType(), remindReq.getText(),
              remindReq.getTitle(),
              MessageTypeEnum.MESSAGE_TYPE_EXCITATION_REMIND.getTypeCode(),
              String.valueOf(messageId), expendNo, null);

      //调用友盟推送
      umMessagePushService.sendMessage(umMessageReq);
    } catch (Exception e) {
      log.error("sendExcitation消息消费失败，失败原因 ", e);
    }
  }

  @RabbitListener(queues = ExcitationMqConstant.QUEUE_DIRECT_MESSAGE_YM_USERH5)
  @RabbitHandler
  public void sendUserH5(Message message) throws UnsupportedEncodingException {

    try {
      String messageContent = new String(message.getBody(), "UTF-8");
      if (messageContent == null) {
        return;
      }

      log.info("[会员激励信息-礼包售卖][消息提醒][RabbitMq][上送参数:{}]", messageContent);
      JSONObject jsonObject = JSONObject.parseObject(messageContent);
      if (jsonObject == null) {
        return;
      }

      String data = jsonObject.getString("excitationH5Req");
      ExcitationMobielH5Req excitationH5Req = JSONObject.parseObject(data,
          ExcitationMobielH5Req.class);

      if (excitationH5Req == null) {
        return;
      }

      ModUser modUser = modUserDao.queryByYmMessage(excitationH5Req.getMobile());

      if (modUser == null) {
        log.info("[消息中心][APP消息发送失败][信息接收人查询为空]");
        return;
      }

      String url = excitationH5Req.getUrl();

      //保存消息发送记录
      int messageId = this.saveMessageLog(MessageTypeEnum.MESSAGE_TYPE_APP,
          excitationH5Req.getTitle(), excitationH5Req.getText(),
          MessageTypeEnum.MESSAGE_TYPE_EXCITATION_H5.getTypeCode(), null,
          MessageTypeEnum.REJT_TYPE_STAFF, String.valueOf(modUser.getId()), url);

      //组装友盟推送消息体
      UmMessageReq umMessageReq = this
          .getSendObject(modUser.getDeviceId(), modUser.getDeviceType(), excitationH5Req.getText(),
              excitationH5Req.getTitle(),
              MessageTypeEnum.MESSAGE_TYPE_EXCITATION_H5.getTypeCode(), String.valueOf(messageId),
              null, url);

      //调用友盟推送
      umMessagePushService.sendMessage(umMessageReq);
    } catch (Exception e) {
      log.error("sendUserH5消息消费失败，失败原因 ", e);
    }
  }

  @RabbitListener(queues = ExcitationMqConstant.QUEUE_DIRECT_MESSAGE_YM_H5)
  @RabbitHandler
  public void sendH5WithRabbitmq(Message message) {
    log.info("[会员激励信息-礼包售卖][消息提醒][RabbitMq][上送参数:{}]", message);
    try {
      String messageContent = new String(message.getBody(), StandardCharsets.UTF_8.name());
      this.sendH5(messageContent);
    } catch (Exception e) {
      log.error("会员激励信息-礼包售卖解析参数失败", e);
    }
  }

  public void sendH5(String messageContent) {

    try {

      log.info("[会员激励信息-礼包售卖][消息提醒][上送参数:{}]", messageContent);
      JSONObject jsonObject = JSONObject.parseObject(messageContent);
      if (jsonObject == null) {
        return;
      }

      String data = jsonObject.getString("excitationH5Req");
      ExcitationH5Req excitationH5Req = JSONObject.parseObject(data, ExcitationH5Req.class);

      if (excitationH5Req == null) {
        return;
      }

      List<ModUser> users = modUserDao.queryExcitationAll();

      if (users == null && !users.isEmpty()) {
        log.info("[消息中心][APP消息发送失败][信息接收人查询为空]");
        return;
      }

      String url = excitationH5Req.getUrl();

      users.forEach(x -> {

        //保存消息发送记录
        int messageId = this.saveMessageLog(MessageTypeEnum.MESSAGE_TYPE_APP,
            excitationH5Req.getTitle(), excitationH5Req.getText(),
            MessageTypeEnum.MESSAGE_TYPE_EXCITATION.getTypeCode(), null,
            MessageTypeEnum.REJT_TYPE_STAFF, String.valueOf(x.getId()), url);

        //组装友盟推送消息体
        UmMessageReq umMessageReq = this
            .getSendObject(x.getDeviceId(), x.getDeviceType(), excitationH5Req.getText(),
                excitationH5Req.getTitle(),
                MessageTypeEnum.MESSAGE_TYPE_EXCITATION.getTypeCode(), String.valueOf(messageId),
                null, url);

        //调用友盟推送
        umMessagePushService.sendMessage(umMessageReq);
      });
    } catch (Exception e) {
      log.error("sendH5消息消费失败，失败原因 ", e);
    }
  }


  @RabbitListener(queues = ExcitationMqConstant.QUEUE_DIRECT_MESSAGE_YM_NOMO)
  @RabbitHandler
  public void sendNomoWithRabbit(Message message) {
    log.info("[会员激励信息-提醒][消息提醒][RabbitMq][上送参数:{}]", message);
    try {
      String messageContent = new String(message.getBody(), StandardCharsets.UTF_8.name());
      this.sendNomo(messageContent);
    } catch (Exception e) {
      log.error("会员激励信息-提醒解析参数失败", e);
    }

  }

  public void sendNomo(String messageContent) {

    try {
      if (messageContent == null) {
        return;
      }
      log.info("[会员激励信息-提醒][消息提醒][上送参数:{}]", messageContent);
      JSONObject jsonObject = JSONObject.parseObject(messageContent);
      if (jsonObject == null) {
        return;
      }

      String data = jsonObject.getString("excitationNomoReq");
      ExcitationNomoReq excitationNomoReq = JSONObject.parseObject(data, ExcitationNomoReq.class);

      if (excitationNomoReq == null) {
        return;
      }

      ModUser modUser = modUserDao.queryByYmMessage(excitationNomoReq.getMobile());

      if (modUser == null) {
        log.info("[消息中心][APP消息发送失败][信息接收人查询为空]");
        return;
      }

      //保存消息发送记录
      int messageId = this.saveMessageLog(MessageTypeEnum.MESSAGE_TYPE_APP,
          excitationNomoReq.getTitle(), excitationNomoReq.getText(),
          MessageTypeEnum.MESSAGE_TYPE_EXCITATION.getTypeCode(), null,
          MessageTypeEnum.REJT_TYPE_STAFF, String.valueOf(modUser.getId()), null);

      //组装友盟推送消息体
      UmMessageReq umMessageReq = this
          .getSendObject(modUser.getDeviceId(), modUser.getDeviceType(),
              excitationNomoReq.getText(), excitationNomoReq.getTitle(),
              MessageTypeEnum.MESSAGE_TYPE_EXCITATION.getTypeCode(), String.valueOf(messageId),
              null, null);

      //调用友盟推送
      umMessagePushService.sendMessage(umMessageReq);
    } catch (Exception e) {
      log.error("sendNomo消息消费失败，失败原因 ", e);
    }
  }

  @RabbitListener(queues = ExcitationMqConstant.QUEUE_DIRECT_MESSAGE_YM_SET)
  @RabbitHandler
  public void sendSet(Message message) {

    try {
      String messageContent = new String(message.getBody(), StandardCharsets.UTF_8.name());
      if (messageContent == null) {
        return;
      }

      log.info("[会员激励信息-账户设置][消息提醒][RabbitMq][上送参数:{}]", messageContent);
      JSONObject jsonObject = JSONObject.parseObject(messageContent);
      if (jsonObject == null) {
        return;
      }

      String data = jsonObject.getString("excitationNomoReq");
      ExcitationNomoReq excitationNomoReq = JSONObject.parseObject(data, ExcitationNomoReq.class);

      if (excitationNomoReq == null) {
        return;
      }

      ModUser modUser = modUserDao.queryByYmMessage(excitationNomoReq.getMobile());

      if (modUser == null) {
        log.info("[消息中心][APP消息发送失败][信息接收人查询为空]");
        return;
      }

      //保存消息发送记录
      int messageId = this.saveMessageLog(MessageTypeEnum.MESSAGE_TYPE_APP,
          excitationNomoReq.getTitle(), excitationNomoReq.getText(),
          MessageTypeEnum.MESSAGE_TYPE_EXCITATION_SET.getTypeCode(), null,
          MessageTypeEnum.REJT_TYPE_STAFF, String.valueOf(modUser.getId()), null);

      //组装友盟推送消息体
      UmMessageReq umMessageReq = this
          .getSendObject(modUser.getDeviceId(), modUser.getDeviceType(),
              excitationNomoReq.getText(), excitationNomoReq.getTitle(),
              MessageTypeEnum.MESSAGE_TYPE_EXCITATION_SET.getTypeCode(), String.valueOf(messageId),
              null, null);

      //调用友盟推送
      umMessagePushService.sendMessage(umMessageReq);
    } catch (Exception e) {
      log.error("sendSet消息消费失败，失败原因 ", e);
    }
  }

  @RabbitListener(queues = ExcitationMqConstant.QUEUE_DIRECT_MESSAGE_YM_WORK)
  @RabbitHandler
  public void sendWorkQueueWithRabbit(Message message) {
    log.info("[工单推送][消息提醒][RabbitMq][上送参数:{}]", message);
    try {
      String messageContent = new String(message.getBody(), StandardCharsets.UTF_8.name());
      this.sendWorkQueue(messageContent);
    } catch (Exception e) {
      log.error("[工单推送][消息提醒参数解析失败", e);
    }
  }

  public void sendWorkQueue(String messageContent) {
    try {
      if (messageContent == null) {
        return;
      }

      log.info("[工单推送][消息提醒][上送参数:{}]", messageContent);
      JSONObject jsonObject = JSONObject.parseObject(messageContent);
      if (jsonObject == null) {
        return;
      }

      String data = jsonObject.getString("sendWorkTask");
      SendWorkTaskBo sendWorkTaskBo = JSONObject.parseObject(data, SendWorkTaskBo.class);
      log.info("[工单推送]sendWorkTaskBo------>" + JSON.toJSONString(sendWorkTaskBo));
      if (sendWorkTaskBo == null) {
        return;
      }

      ModUser modUser = modUserDao.queryById(sendWorkTaskBo.getUserId());

      if (modUser == null) {
        log.info("[消息中心][APP消息发送失败][信息接收人查询为空]");
        return;
      }
      String type;
      String task;
      if ("MESSAGE".equals(sendWorkTaskBo.getMessageType())) {
        type = "YM_SMS";
        task = "810";
      } else {
        type = "YM_APP";
        task = "809";
      }
      if ("WORD_STATUS_12".equals(sendWorkTaskBo.getType())) {
        type = "YM_APP";
        task = "811";
      }
      //保存消息发送记录
      int messageId = this.saveMessageLog(MessageTypeEnum.getMessageTypeEnum(type),
          sendWorkTaskBo.getTitle(), sendWorkTaskBo.getContent(),
          task, String.valueOf(sendWorkTaskBo.getWorkMessageId()),
          MessageTypeEnum.REJT_TYPE_STAFF, String.valueOf(modUser.getId()), null);

      if ("MESSAGE".equals(sendWorkTaskBo.getMessageType())) {
        smsService.sendMessage(modUser.getMobile(), sendWorkTaskBo.getContent());
      } else if ("APP".equals(sendWorkTaskBo.getMessageType())) {

        Map<String, String> customMap = new HashMap<>();
        customMap.put("orderId", String.valueOf(sendWorkTaskBo.getWorkMessageId()));
        customMap.put("code", MessageTypeEnum.MESSAGE_TYPE_WORK_TASK.getTypeCode());
        customMap.put("messageId", String.valueOf(messageId));
        UmMessageReq umMessageReq = this
            .getSendObject(modUser.getDeviceId(), modUser.getDeviceType(),
                sendWorkTaskBo.getContent(), sendWorkTaskBo.getTitle(), customMap);
        //调用友盟推送
        umMessagePushService.sendMessage(umMessageReq);
      }
    } catch (Exception e) {
      log.error("[工单推送][消息异常][RabbitMq][上送参数] ", e);
    }
  }

  @RabbitListener(queues = QualityMqConstant.QUEUE_WORK_PUSH)
  @RabbitHandler
  public void sendOutWorkQueueWithRabbit(Message message) {
    log.info("[工单超时推送][消息提醒][RabbitMq][上送参数:{}]", message);
    try {
      String messageContent = new String(message.getBody(), StandardCharsets.UTF_8.name());
      this.sendOutWorkQueue("[工单超时推送][消息提醒]");
    } catch (Exception e) {
      log.error("[工单超时推送][消息提醒][参数解析失败]", e);
    }
  }

  public void sendOutWorkQueue(String messageContent) {
    String type;
    String task;
    try {
      if (StringUtils.isEmpty(messageContent)) {
        return;
      }
      log.info("[工单超时推送][消息提醒][上送参数:{}]", messageContent);
      JSONObject jsonObject = JSONObject.parseObject(messageContent);
      if (jsonObject == null) {
        return;
      }
      String data = jsonObject.getString("sendWorkTask");
      SendWorkTaskBo sendWorkTaskBo = JSONObject.parseObject(data, SendWorkTaskBo.class);
      log.info("[工单超时推送]sendWorkTaskBo------>" + JSON.toJSONString(sendWorkTaskBo));
      if (sendWorkTaskBo == null) {
        return;
      }
      boolean boo = false;
      if ("WORD_STATUS_6".equals(sendWorkTaskBo.getType())) {
        NwWorkOrderSchedule schedule = nwWorkOrderScheduleMapper.queryById(
            sendWorkTaskBo.getWorkId());
        if (schedule != null && schedule.getScheduleStatus() >= 3
            && schedule.getScheduleStatus() != 7) {
          boo = true;
        }
      }
      if ("WORD_STATUS_7".equals(sendWorkTaskBo.getType())) {
        NwWorkOrder nwWorkOrder = nwWorkOrderMapper.queryById(sendWorkTaskBo.getWorkId());
        if (nwWorkOrder != null && nwWorkOrder.getStatus() == 5) {
          boo = true;
        }
      }
      if ("WORD_STATUS_8".equals(sendWorkTaskBo.getType())) {
        NwWorkOrder nwWorkOrder = nwWorkOrderMapper.queryById(sendWorkTaskBo.getWorkId());
        if (nwWorkOrder != null && nwWorkOrder.getStatus() == 3) {
          boo = true;
        }
      }
      if ("WORD_STATUS_14".equals(sendWorkTaskBo.getType())) {
        NwWorkOrder nwWorkOrder = nwWorkOrderMapper.queryById(sendWorkTaskBo.getWorkId());
        if (nwWorkOrder != null && nwWorkOrder.getStatus() == 2) {
          boo = true;
        }
      }
      if (boo) {
        log.info("[消息中心][数据已处理]{}", sendWorkTaskBo);
        return;
      }
      ModUser modUser = modUserDao.queryById(sendWorkTaskBo.getUserId());

      if (modUser == null) {
        log.info("[消息中心][APP消息发送失败][信息接收人查询为空]");
        return;
      }
      if ("MESSAGE".equals(sendWorkTaskBo.getMessageType())) {
        type = "YM_SMS";
        task = "810";
      } else {
        type = "YM_APP";
        task = "809";
      }
      //保存消息发送记录
      int messageId = this.saveMessageLog(MessageTypeEnum.getMessageTypeEnum(type),
          sendWorkTaskBo.getTitle(), sendWorkTaskBo.getContent(),
          task, String.valueOf(sendWorkTaskBo.getWorkMessageId()),
          MessageTypeEnum.REJT_TYPE_STAFF, String.valueOf(modUser.getId()), null);

      NwWorkOrder nwWorkOrder = nwWorkOrderMapper.queryById(sendWorkTaskBo.getWorkMessageId());
      nwWorkOrder.setIfTimeOut(1);
      nwWorkOrder.setUpdateTime(new Date());
      nwWorkOrderMapper.update(nwWorkOrder);

      if ("MESSAGE".equals(sendWorkTaskBo.getMessageType())) {
        smsService.sendMessage(modUser.getMobile(), sendWorkTaskBo.getContent());
      } else if ("APP".equals(sendWorkTaskBo.getMessageType())) {
        Map<String, String> customMap = new HashMap<>();
        customMap.put("orderId", String.valueOf(sendWorkTaskBo.getWorkMessageId()));
        customMap.put("code", MessageTypeEnum.MESSAGE_TYPE_WORK_TASK.getTypeCode());
        customMap.put("messageId", String.valueOf(messageId));
        UmMessageReq umMessageReq = this
            .getSendObject(modUser.getDeviceId(), modUser.getDeviceType(),
                sendWorkTaskBo.getContent(), sendWorkTaskBo.getTitle(), customMap);
        //调用友盟推送
        umMessagePushService.sendMessage(umMessageReq);
      }
    } catch (Exception e) {
      log.error("[工单推送][超时消息异常][RabbitMq][上送参数] ", e);
    }
  }

  /**
   * 方法没有被使用
   *
   * @param sendWorkTaskBo
   * @param time
   */
  @Deprecated
  public void sendOutWorkQueue(SendWorkTaskBo sendWorkTaskBo, Integer time) {
    try {
      Map<String, Object> sendMap = new HashMap<>();
      sendMap.put("sendWorkTask", JSONObject.toJSONString(sendWorkTaskBo));
      //加入延迟队列
      String delayE = QualityMqConstant.EXCHANGE_WORK_PUSH;
      String delayR = QualityMqConstant.WORK_PUSH_RULE;
      //rabbitMqSender.sendDelayMessage(delayE, delayR, sendMap, new Long(time).intValue());
    } catch (Exception e) {
      log.error("sendOutWorkQueue_消息推送失败 " + JSON.toJSONString(sendWorkTaskBo), e);
    }
  }


  /**
   * 保存消息内容
   *
   * @param remindReq
   * @return
   */
  private int saveMessage(ExcitationRemindReq remindReq) {

    ModMessageExcitation modo = new ModMessageExcitation();
    modo.setBilleType(remindReq.getBillType());
    modo.setSn(remindReq.getSn());
    modo.setExcitationAmount(remindReq.getExcitationAmount());
    modo.setAmount(remindReq.getAmount());
    modo.setDescribeStr(remindReq.getDesc());
    modo.setOrderTime(remindReq.getCreateTime());
    modo.setCreateTime(new Date());
    modo.setRemark(remindReq.getTopName());

    modMessageExcitationDao.insert(modo);

    return modo.getId();
  }

  /**
   * 保存消息发送记录
   *
   * @param typeEnum       消息发送类型
   * @param title          消息标题
   * @param messageContent 消息内容
   * @param extendNo       第三方关联单号
   * @param extendType     第三方关联类型
   * @param rejtTypeEnum   接受人类型
   * @param rejtId         接受人ID
   * @param remark         备注信息
   */
  public int saveMessageLog(MessageTypeEnum typeEnum, String title, String messageContent,
      String extendType, String extendNo,
      MessageTypeEnum rejtTypeEnum, String rejtId, String remark) {

    ModMessageLog modMessageLog = new ModMessageLog();

    //消息发送类型
    modMessageLog.setMessageType(typeEnum.getTypeCode());
    //消息内容
    modMessageLog.setMessageContent(messageContent);
    //第三方关联单号
    modMessageLog.setExtendNo(extendNo);
    //消息发送状态
    modMessageLog.setSendState(MessageStateEnum.SEND_SUCCESS.getStateCode());
    //消息读取状态
    modMessageLog.setReadState(MessageStateEnum.READ_UN.getStateCode());
    //消息接收者类型
    modMessageLog.setRecipientType(rejtTypeEnum.getTypeCode());
    //消息接收者ID
    modMessageLog.setRecipientId(rejtId);

    modMessageLog.setMessageTitle(title);
    modMessageLog.setExtendType(extendType);
    modMessageLog.setCreateTime(new Date());
    modMessageLog.setUpdateTime(new Date());
    modMessageLog.setGroupId(2);
    modMessageLog.setCompanyId(2);
    modMessageLog.setRemark(remark);

    int messageId = modMessageLogMapper.insert(modMessageLog);

    log.info("[消息发送][日志记录]{}", messageId > 0 ? "成功" : "失败");

    if (messageId < 1) {
      throw new RuntimeException("消息发送日志记录保存失败");
    }

    return modMessageLog.getId();
  }


  public int saveMessageLogForBill(MessageTypeEnum typeEnum, String title, String messageContent,
                            String extendType, String extendNo,
                            MessageTypeEnum rejtTypeEnum, String rejtId, String remark, Integer hotelId) {

    ModMessageLog modMessageLog = new ModMessageLog();

    //消息发送类型
    modMessageLog.setMessageType(typeEnum.getTypeCode());
    //消息内容
    modMessageLog.setMessageContent(messageContent);
    //第三方关联单号
    modMessageLog.setExtendNo(extendNo);
    //消息发送状态
    modMessageLog.setSendState(MessageStateEnum.SEND_SUCCESS.getStateCode());
    //消息读取状态
    modMessageLog.setReadState(MessageStateEnum.READ_UN.getStateCode());
    //消息接收者类型
    modMessageLog.setRecipientType(rejtTypeEnum.getTypeCode());
    //消息接收者ID
    modMessageLog.setRecipientId(rejtId);

    modMessageLog.setMessageTitle(title);
    modMessageLog.setExtendType(extendType);
    modMessageLog.setCreateTime(new Date());
    modMessageLog.setUpdateTime(new Date());
    modMessageLog.setGroupId(2);
    modMessageLog.setCompanyId(hotelId);
    modMessageLog.setRemark(remark);

    int messageId = modMessageLogMapper.insert(modMessageLog);

    log.info("[消息发送][日志记录]{}", messageId > 0 ? "成功" : "失败");

    if (messageId < 1) {
      throw new RuntimeException("消息发送日志记录保存失败");
    }

    return modMessageLog.getId();
  }


  /**
   * 组装消息发送对象
   *
   * @param deviceId    设备号
   * @param deviceType  设备类型
   * @param text        消息内容
   * @param title       消息标题
   * @param messageCode 消息code
   * @param messageId   消息ID
   * @param extendNo    关联单号
   * @return
   */
  public UmMessageReq getSendObject(String deviceId, String deviceType, String text, String title,
      String messageCode, String messageId, String extendNo, String url) {

    UmMessageReq umMessageReq = new UmMessageReq();

    //设置消息发送类型 1：单播
    umMessageReq.setType(1);
    umMessageReq.setDeviceToken(deviceId);
    umMessageReq.setDisplayType(1);
    umMessageReq.setTitle(title);
    umMessageReq.setText(text);
    //设置接受消息自定义解析
    umMessageReq.setAfterOpen("go_custom");

    //自定义解析参数
    Map<String, String> customMap = new HashMap<>();
    customMap.put("code", messageCode);
    //应用根据messageID查询消息读取状态
    customMap.put("messageId", messageId);
    customMap.put("extendNo", extendNo);
    customMap.put("jumpUrl", url);
    umMessageReq.setSound("push_sound.caf");
    umMessageReq.setCustomizedField(customMap);

    //判断用户设备标识是否为空
    if (!StringUtils.isEmpty(deviceType)) {

      umMessageReq.setMobileType(MobileTypeEnum.getCodeByDesc(deviceType));
    } else {

      if (!StringUtils.isEmpty(deviceType) && deviceId.length() > 44) {
        //IOS
        umMessageReq.setMobileType(MobileTypeEnum.IOS.getCode());
      } else {
        //安卓
        umMessageReq.setMobileType(MobileTypeEnum.ANDROID.getCode());
      }
    }
    return umMessageReq;
  }

  /**
   * 组装消息发送对象
   *
   * @param deviceId   设备号
   * @param deviceType 设备类型
   * @param text       消息内容
   * @param title      消息标题
   * @param customMap  自定义参数
   * @return {@link UmMessageReq}
   */
  private UmMessageReq getSendObject(String deviceId, String deviceType, String text, String title,
      Map<String, String> customMap) {

    UmMessageReq umMessageReq = new UmMessageReq();

    //设置消息发送类型 1：单播
    umMessageReq.setType(1);
    umMessageReq.setDeviceToken(deviceId);
    umMessageReq.setDisplayType(1);
    umMessageReq.setTitle(title);
    umMessageReq.setText(text);
    //设置接受消息自定义解析
    umMessageReq.setAfterOpen("go_custom");
    umMessageReq.setSound("push_sound.caf");

    umMessageReq.setCustomizedField(customMap);

    //判断用户设备标识是否为空
    if (!StringUtils.isEmpty(deviceType)) {

      umMessageReq.setMobileType(MobileTypeEnum.getCodeByDesc(deviceType));
    } else {

      if (deviceId.length() > 44) {
        //IOS
        umMessageReq.setMobileType(MobileTypeEnum.IOS.getCode());
      } else {
        //安卓
        umMessageReq.setMobileType(MobileTypeEnum.ANDROID.getCode());
      }
    }
    return umMessageReq;
  }
}
