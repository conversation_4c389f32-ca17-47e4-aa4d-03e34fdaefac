package com.shands.mod.message.controller;

import com.shands.mod.message.annotation.SignSafety;
import com.shands.mod.message.service.ElsExcitationService;
import com.shands.mod.message.service.IHotelInfoService;
import com.shands.mod.vo.ResultVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-18
 * @description els调用服务
 */

@RestController
@RequestMapping(value = "/elsMessage")
public class ElsMessageController {

  @Resource
  private ElsExcitationService elsExcitationService;
  @Resource
  private IHotelInfoService hotelInfoService;

  @SignSafety
  @PostMapping(value = "/sendH5")
  public ResultVO sendH5(@RequestBody Map<String, Object> map){
    return elsExcitationService.sendH5(map);
  }

  @SignSafety
  @PostMapping(value = "/sendUserH5")
  public ResultVO sendUserH5(@RequestBody Map<String, Object> map){
    return elsExcitationService.sendUserH5(map);
  }

  @SignSafety
  @PostMapping(value = "/sendNomo")
  public ResultVO sendNomo(@RequestBody Map<String, Object> map){
    return elsExcitationService.sendNomo(map);
  }

  @SignSafety
  @PostMapping(value = "/sendSet")
  public ResultVO sendSet(@RequestBody Map<String, Object> map){
    return elsExcitationService.sendSet(map);
  }

  @SignSafety
  @PostMapping(value = "/sendExcitation")
  public ResultVO sendExcitation(@RequestBody Map<String, Object> map){
    return elsExcitationService.sendExcitation(map);
  }

}
