package com.shands.mod.message.job;

import com.shands.mod.dao.mapper.mp.AdsTradeHotelPerformanceMDao;
import com.shands.mod.dao.mapper.mp.AdsTradeHotelPerformanceQDao;
import com.shands.mod.dao.mapper.mp.MpAppraisalRecordDao;
import com.shands.mod.dao.mapper.mp.MpAssessTaskDao;
import com.shands.mod.dao.mapper.mp.MpInspectionRecordsDao;
import com.shands.mod.dao.mapper.mp.MpManagerDao;
import com.shands.mod.dao.mapper.mp.MpManagerScoreDao;
import com.shands.mod.dao.model.enums.mp.TargetEnum;
import com.shands.mod.dao.model.mp.po.MpAssessTask;
import com.shands.mod.dao.model.mp.po.MpManagerScore;
import com.shands.mod.dao.model.mp.vo.TradeVo;
import com.shands.mod.message.util.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/11/12
 * @desc 总经理绩效计算定时任务
 */
@Component
@Slf4j
public class ManagerScoreJob {

  @Autowired
  private MpAssessTaskDao mpAssessTaskDao;

  @Autowired
  private MpManagerScoreDao mpManagerScoreDao;

  @Autowired
  private MpAppraisalRecordDao mpAppraisalRecordDao;

  @Autowired
  private MpInspectionRecordsDao mpInspectionRecordsDao;

  @Autowired
  private MpManagerDao mpManagerDao;

  @Autowired
  private AdsTradeHotelPerformanceMDao adsTradeHotelPerformanceMDao;

  @Autowired
  private AdsTradeHotelPerformanceQDao adsTradeHotelPerformanceQDao;

  /**
   * 酒店考核分明细落地
   * @param param
   * @return
   * @throws Exception
   */
  @XxlJob("calHotelScore")
  public ReturnT<String> calHotelScore(String param) throws Exception {

    Date dataDate = Date.from(LocalDate.now().plusDays(-1).atStartOfDay(ZoneId.systemDefault()).toInstant());
    if(StringUtils.hasText(param)){
      dataDate = DateUtils.strToDate(param);
    }

    //查询所有正在进行中的绩效方案 季度考核
    List<MpAssessTask> targetQVos = mpAssessTaskDao.selectTargets(dataDate, "quarter","running");
    if(targetQVos != null && !targetQVos.isEmpty()){

      //查询大数据指标数据是否落地
      List<TradeVo> tradeQVos = adsTradeHotelPerformanceQDao.selectByUser(dataDate.toString());
      if(tradeQVos != null && !tradeQVos.isEmpty()){

        //计算酒店考核分
        this.calHotelScore(tradeQVos,targetQVos,dataDate);
      }
    }

    //查询所有正在进行中的绩效方案 月度考核
    List<MpAssessTask> targetMVos = mpAssessTaskDao.selectTargets(dataDate, "month","running");
    if(targetMVos != null && !targetMVos.isEmpty()){

      //查询大数据指标数据是否落地
      List<TradeVo> tradeMonthVos = adsTradeHotelPerformanceMDao.selectByUser(dataDate.toString());
      if(tradeMonthVos != null && !tradeMonthVos.isEmpty()){

        //计算酒店考核分
        this.calHotelScore(tradeMonthVos,targetMVos,dataDate);
      }
    }

    return ReturnT.SUCCESS;
  }

  /**
   * 总经理综合分落地
   * @param param
   * @return
   * @throws Exception
   */
  @XxlJob("calManagerScore")
  public ReturnT<String> calManagerScore(String param) throws Exception {

    //查询落地考核分明细
    List<MpManagerScore> scores = mpManagerScoreDao.selectSourcesToNow();

    if(scores != null && !scores.isEmpty()){

      //根据总经理进行数据分组 过滤总经理为空的情况
      Map<Integer,List<MpManagerScore>> managerScores = scores.stream().filter(a -> a.getManagerId() != null)
          .collect(Collectors.groupingBy(a -> a.getManagerId()));

      //对总经理进行数据迭代
      managerScores.forEach((k1,v1) -> {

        List<String> taskCodes = new ArrayList<>();

        //综合分 =（a酒店累计得分*a累计天数 + b酒店累计得分*b累计天数） / （a任职天数+b任职天数）
        AtomicReference<Double> managerSocre = new AtomicReference<Double>(0.00);
        AtomicReference<Integer> dayCount = new AtomicReference<>(0);
        AtomicReference<Integer> inspectionCount = new AtomicReference<>(0);
        v1.forEach(x -> {

          taskCodes.add(x.getTaskCode());
          Integer days = x.getManagerCumDay();
          dayCount.updateAndGet(v2 -> v2 + days);
          managerSocre.updateAndGet(v2 -> v2 + x.getManagerCumSocre() * days);

          //判断总经理是否存在一票否决情况
          int flag = mpInspectionRecordsDao.qurInfoByUser(x.getTaskCode(),x.getHotelCode(),k1);
          if(flag > 0){
            inspectionCount.updateAndGet(v2 -> v2 + 1);
          }
        });

        //数据落地
        double comprScore = managerSocre.get() / dayCount.get();

        if(inspectionCount.get() > 0){
          mpManagerScoreDao.updateByTcdUserId(k1,taskCodes,0.00);
        }else{
          mpManagerScoreDao.updateByTcdUserId(k1,taskCodes,comprScore);
        }
      });
    }

    return ReturnT.SUCCESS;
  }



  /**
   * 计算酒店考核分
   * @param tradeVos
   * @param targetVos
   * @param dataDate
   */
  private void calHotelScore(List<TradeVo> tradeVos,List<MpAssessTask> targetVos,Date dataDate){

    //定位方案（酒店合同状态+事业部+管理方式）
    Map<String,List<MpAssessTask>> tragetMap = targetVos.stream()
        .collect(Collectors.groupingBy(a -> a.getHotelStatus() + "-" + a.getBrand() + "-" + a.getManage()));

    //查询历史评分数据
    List<MpManagerScore> oldScores = mpManagerScoreDao.selectSourcesToNow();

    tradeVos.forEach(x -> {

      //筛选命中考核方案的酒店信息 定位方案(定位的方式要根据方案的配置内容来确定)
      String taskMapKey = x.getHotelStatus() + "-" + x.getDepartmentCode() + "-" + x.getManageCode();

      //未命中则不参与执行(不在考核范围之内)
      if(!tragetMap.containsKey(taskMapKey)){
        return;
      }

      //计算总考核分（酒店分）
      BigDecimal hotelScore = this.calScore(x,targetVos);

      //保存酒店总考核分
      //查询本方案所有人的评分信息
      MpAssessTask task = tragetMap.get(taskMapKey).get(0);

      if(oldScores != null && !oldScores.isEmpty()){

        //定位 用户+酒店+酒店状态（只有变更了酒店状态才会影响重新评分）
        Map<String,List<MpManagerScore>> stringListMap = oldScores.stream()
            .collect(Collectors.groupingBy(a ->
                a.getTaskCode()
                    + "-" + a.getManagerId()
                    + "-" + a.getHotelCode()
                    + "-" + a.getHotelStatus()));

        String findKey = task.getTaskCode() + "-" + x.getUserId() + "-" + x.getHotelCode() + "-" + x.getHotelStatus();
        if(stringListMap.containsKey(findKey)){
          //如果之前有记录，则进行update操作，反之进行insert操作
          MpManagerScore updObject = stringListMap.get(findKey).get(0);

          //总经理累计得分 = 酒店当日考核分 - （酒店历史考核分 - 酒店总经理历史累计得分）
          BigDecimal differ = new BigDecimal(updObject.getHotelScore()).subtract(new BigDecimal(updObject.getManagerCumSocre()));
          BigDecimal managerCumScore = hotelScore.subtract(differ);

          //更新数据

          //判断总经理是否存在一票否决情况
          int flag = mpInspectionRecordsDao.qurInfoByUser(task.getTaskCode(),x.getHotelCode(),updObject.getManagerId());
          if(flag > 0){
            this.updScore(updObject,hotelScore,new BigDecimal(0),dataDate);
          }else{
            this.updScore(updObject,hotelScore,managerCumScore,dataDate);
          }
        }else{

          //历史得分
          BigDecimal contrastSocre = new BigDecimal(0);

          //判断该酒店是否已存在考核分(主要考虑（1）酒店在考核期中总经理临时加入的情况与（2）酒店总经理变更情况)
          //对比指
          Map<String,List<MpManagerScore>> contrastMap = oldScores.stream().collect(Collectors.groupingBy(a -> a.getTaskCode() + "-" + a.getHotelCode() + "-" + a.getHotelStatus()));
          String mapKey = task.getTaskCode() + "-" + x.getHotelCode() + "-" + x.getHotelStatus();
          if(contrastMap.containsKey(mapKey)){
            contrastSocre = BigDecimal.valueOf(contrastMap.get(mapKey).get(0).getHotelScore());
          }

          //总经理累计得分 = 酒店当日考核分 - 历史得分
          BigDecimal managerCumScore = hotelScore.subtract(contrastSocre);

          //判断总经理是否存在一票否决情况
          int flag = mpInspectionRecordsDao.qurInfoByUser(task.getTaskCode(),x.getHotelCode(),x.getUserId());
          if(flag > 0){
            this.inrScore(x,task,hotelScore,new BigDecimal(0),dataDate);
          }else{
            this.inrScore(x,task,hotelScore,managerCumScore,dataDate);
          }
        }
      }else{

        //判断总经理是否存在一票否决情况
        int flag = mpInspectionRecordsDao.qurInfoByUser(task.getTaskCode(),x.getHotelCode(),x.getUserId());
        if(flag > 0){
          this.inrScore(x,task,hotelScore,new BigDecimal(0),dataDate);
        }else{
          this.inrScore(x,task,hotelScore,hotelScore,dataDate);
        }
      }
    });
  }

  /**
   * 计算考核分
   * @param x
   * @param targetVos
   * @return
   */
  private BigDecimal calScore(TradeVo x,List<MpAssessTask> targetVos){

    //将所有方案进行分组（酒店合同状态+事业部+管理方式+指标）
    Map<String,List<MpAssessTask>> tragetMap = targetVos.stream()
        .collect(Collectors.groupingBy(a -> a.getBrand() + "-" + a.getManage() + "-" + a.getHotelStatus() + "-" + a.getTargetCode()));

    BigDecimal sumScore = new BigDecimal(0);
    //会员发展
    String memKey = x.getDepartmentCode() + "-" + x.getManageCode() + "-" + x.getHotelStatus() + "-" + TargetEnum.MEM_RATE.name();
    if(tragetMap.containsKey(memKey)){

      //获取方案指标配置
      List<MpAssessTask> sourceData = tragetMap.get(memKey);
      if(sourceData != null || sourceData.isEmpty()){

        //计算得分
        Integer score = new BigDecimal(x.getMemScore())
            .multiply(new BigDecimal(sourceData.get(0).getProportion()))
            .divide(new BigDecimal(100)).intValue();
        sumScore = sumScore.add(new BigDecimal(score));
      }
    }
    //官渠占比
    String roomKey = x.getDepartmentCode() + "-" + x.getManageCode() + "-" + x.getHotelStatus() + "-" + TargetEnum.ROOM_BOOKER_RATE.name();
    if(tragetMap.containsKey(roomKey)){

      //获取方案指标配置
      List<MpAssessTask> sourceData = tragetMap.get(roomKey);
      if(sourceData != null || sourceData.isEmpty()){

        //计算得分
        Integer score = new BigDecimal(x.getRoomsBookerScore())
            .multiply(new BigDecimal(sourceData.get(0).getProportion()))
            .divide(new BigDecimal(100)).intValue();
        sumScore = sumScore.add(new BigDecimal(score));
      }
    }
    //网评
    String commentKey = x.getDepartmentCode() + "-" + x.getManageCode() + "-" + x.getHotelStatus() + "-" + TargetEnum.COMMENT_SCORE.name();
    if(tragetMap.containsKey(commentKey)){

      //获取方案指标配置
      List<MpAssessTask> sourceData = tragetMap.get(commentKey);
      if(sourceData != null || sourceData.isEmpty()){

        //计算得分
        Integer score = new BigDecimal(x.getCompeteScore())
            .multiply(new BigDecimal(sourceData.get(0).getProportion()))
            .divide(new BigDecimal(100)).intValue();
        sumScore = sumScore.add(new BigDecimal(score));
      }
    }
    //营业收入
    String incomeKey = x.getDepartmentCode() + "-" + x.getManageCode() + "-" + x.getHotelStatus() + "-" + TargetEnum.INCOME_RATE.name();
    if(tragetMap.containsKey(incomeKey)){

      //获取方案指标配置
      List<MpAssessTask> sourceData = tragetMap.get(incomeKey);
      if(sourceData != null || sourceData.isEmpty()){

        //计算得分
        Integer score = new BigDecimal(x.getIncomeScore())
            .multiply(new BigDecimal(sourceData.get(0).getProportion()))
            .divide(new BigDecimal(100)).intValue();
        sumScore = sumScore.add(new BigDecimal(score));
      }
    }
    //月度统采率
    String uniformKey = x.getDepartmentCode() + "-" + x.getManageCode() + "-" + x.getHotelStatus() + "-" + TargetEnum.UNIFORM_PURCHASE_RATE.name();
    if(tragetMap.containsKey(uniformKey)){

      //获取方案指标配置
      List<MpAssessTask> sourceData = tragetMap.get(uniformKey);
      if(sourceData != null || sourceData.isEmpty()){

        //计算得分
        Integer score = new BigDecimal(x.getUniformPurchaseScore())
            .multiply(new BigDecimal(sourceData.get(0).getProportion()))
            .divide(new BigDecimal(100)).intValue();
        sumScore = sumScore.add(new BigDecimal(score));
      }
    }
    //筹备期统采率
    String preUniformKey = x.getDepartmentCode() + "-" + x.getManageCode() + "-" + x.getHotelStatus() + "-" + TargetEnum.PREPARE_UNIFORM_PURCHASE_RATE.name();
    if(tragetMap.containsKey(preUniformKey)){

      //获取方案指标配置
      List<MpAssessTask> sourceData = tragetMap.get(preUniformKey);
      if(sourceData != null || sourceData.isEmpty()){

        //计算得分
        Integer score = new BigDecimal(x.getUniformPurchaseScore())
            .multiply(new BigDecimal(sourceData.get(0).getProportion()))
            .divide(new BigDecimal(100)).intValue();
        sumScore = sumScore.add(new BigDecimal(score));
      }
    }
    //筹备期会员发展
    String preMemKey = x.getDepartmentCode() + "-" + x.getManageCode() + "-" + x.getHotelStatus() + "-" + TargetEnum.PREPARE_MEM_RATE.name();
    if(tragetMap.containsKey(preMemKey)){

      //获取方案指标配置
      List<MpAssessTask> sourceData = tragetMap.get(preMemKey);
      if(sourceData != null || sourceData.isEmpty()){

        //计算得分
        Integer score = new BigDecimal(x.getMemScore())
            .multiply(new BigDecimal(sourceData.get(0).getProportion()))
            .divide(new BigDecimal(100)).intValue();
        sumScore = sumScore.add(new BigDecimal(score));
      }
    }
    //筹备期完成情况
    String prepareKey = x.getDepartmentCode() + "-" + x.getManageCode() + "-" + x.getHotelStatus() + "-" + TargetEnum.PREPARE_RATE.name();
    if(tragetMap.containsKey(prepareKey)){

      //获取方案指标配置
      List<MpAssessTask> sourceData = tragetMap.get(prepareKey);
      if(sourceData != null || sourceData.isEmpty()){

        //计算得分
        Integer score = new BigDecimal(x.getPreparationScore())
            .multiply(new BigDecimal(sourceData.get(0).getProportion()))
            .divide(new BigDecimal(100)).intValue();
        sumScore = sumScore.add(new BigDecimal(score));
      }
    }

    return sumScore;
  }

  /**
   * 插入数据
   * @param x
   * @param task
   */
  private void inrScore(TradeVo x,MpAssessTask task,BigDecimal hotelScore,BigDecimal managerCumScore,Date dataDate){
    MpManagerScore inrObject = new MpManagerScore();
    inrObject.setTaskCode(task.getTaskCode());
    inrObject.setTaskName(task.getName());
    inrObject.setHotelCode(x.getHotelCode());
    inrObject.setHotelName(x.getHotelName());
    inrObject.setManagerId(x.getUserId());
    inrObject.setManagerName(x.getUserName());
    inrObject.setHotelScore(hotelScore.doubleValue());
    inrObject.setManagerCumSocre(managerCumScore.doubleValue() < 0 ? 0 : managerCumScore.doubleValue());
    inrObject.setManagerCumDay(1);
    inrObject.setDepartmentCode(x.getDepartmentCode());
    inrObject.setDepartmentName(x.getDeptName());
    inrObject.setBrandCode(x.getBrandCode());
    inrObject.setBrandName(x.getBrandName());
    inrObject.setManageCode(x.getManageCode());
    inrObject.setManageName(x.getManageMode());
    inrObject.setHotelStatus(x.getHotelStatus());
    inrObject.setCreateTime(new Date());
    inrObject.setUpdateTime(new Date());
    inrObject.setStartDate(dataDate);
    inrObject.setEndDate(dataDate);
    inrObject.setBatchCode(task.getBatchCode());
    mpManagerScoreDao.insert(inrObject);
    System.out.println("insert 执行中");
  }

  /**
   * 修改数据
   * @param mpManagerScore
   * @param hotelScore
   * @param hotelScore
   */
  private void updScore(MpManagerScore mpManagerScore, BigDecimal hotelScore,BigDecimal managerCumScore,Date dataDate){
    MpManagerScore UpdateObject = new MpManagerScore();
    //避免原数据被修改
    BeanUtils.copyProperties(mpManagerScore,UpdateObject);
    UpdateObject.setHotelScore(hotelScore.doubleValue());
    UpdateObject.setManagerCumSocre(managerCumScore.doubleValue() < 0 ? 0 : managerCumScore.doubleValue());
    UpdateObject.setManagerCumDay(UpdateObject.getManagerCumDay() + 1);
    UpdateObject.setUpdateTime(new Date());
    UpdateObject.setEndDate(dataDate);
    mpManagerScoreDao.updateByPrimaryKey(UpdateObject);
    System.out.println("update 执行中");
  }
}
