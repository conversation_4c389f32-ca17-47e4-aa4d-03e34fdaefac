package com.shands.mod.websocket.controller;

import com.shands.mod.vo.ResultVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @createDate 2021/02/25 上午9:26 <br>
 * @see com.shands.mod.websocket.controller <br>
 */
@RestController
@RequestMapping(value = "/webSocket/public")
public class WebsocketHealthController {

  @Value("${spring.application.name}")
  private String name;


  /**
   * 健康检查
   *
   * @return
   */
  @RequestMapping(value = "/health", method = RequestMethod.GET)
  public ResultVO<String> health() {
    return ResultVO.success(this.name + "#" + System.currentTimeMillis());
  }
}
