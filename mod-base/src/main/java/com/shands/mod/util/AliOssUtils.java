package com.shands.mod.util;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.CreateBucketRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import java.io.File;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AliOssUtils {

  @Value("${oss.bucketname:delonix-sxe}")
  private String bucketName;

  @Resource
  private OSSClient ossClient;

  /**
   * 阿里云文件上传
   * @param file 文件
   * @param fileUrl 地址
   * @return 地址
   */
  public String upLoad(File file, String fileUrl){
    log.info("[阿里云Oss文件开始上传，文件名：{}]", file.getName());

    //判断文件是否为空
    if(file == null){
      throw new RuntimeException("上传文件不能为空");
    }

    //判断oss容器是否存在是否存在，不存在则创建
    if(!ossClient.doesBucketExist(bucketName)){
      ossClient.createBucket(bucketName);
      CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucketName);
      createBucketRequest.setCannedACL(CannedAccessControlList.PublicRead);
      ossClient.createBucket(createBucketRequest);
      // 设置权限(公开读)
      ossClient.setBucketAcl(bucketName, CannedAccessControlList.PublicRead);
    }

    //上传文件
    PutObjectResult result = ossClient.putObject(new PutObjectRequest(bucketName,fileUrl,file));
    if(result != null){
      log.info("[阿里云Oss文件上传成功，地址{}]", fileUrl);
      return fileUrl;
    }

    return null;
  }

  /**
   * 文件读取
   * @param fileUrl 文件地址
   * @return
   */
  public OSSObject getObject(String fileUrl){
    return ossClient.getObject(bucketName, fileUrl);
  }
}
