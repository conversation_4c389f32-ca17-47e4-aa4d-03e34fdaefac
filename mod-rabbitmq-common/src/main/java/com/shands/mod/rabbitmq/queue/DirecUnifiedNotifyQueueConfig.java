package com.shands.mod.rabbitmq.queue;

import com.shands.mod.rabbitmq.constant.RabbitMqConstant;
import javax.annotation.Resource;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/9/18
 * @desc 直连交换机 支付统一下单回调 配置类
 */
@Configuration
public class DirecUnifiedNotifyQueueConfig {

  @Resource
  private RabbitAdmin rabbitAdmin;

  @Bean
  public void directUnifiedRabbitConfigInit() {

    rabbitAdmin.declareQueue(wxUnifiedNotifyQueue());
    rabbitAdmin.declareExchange(wxUnifiedNotifyDirectExchange());
  }

  /**
   * 微信统一下单通知定义队列
   */
  @Bean("wxUnifiedNotifyQueue")
  public Queue wxUnifiedNotifyQueue() {
    return new Queue(RabbitMqConstant.QUEUE_DIRECT_WX_UNIFIED_NOTIFY, true);
  }

  /**
   * 微信统一下单通知定义交换机
   *
   * @return
   */
  @Bean("wxUnifiedNotifyDirectExchange")
  DirectExchange wxUnifiedNotifyDirectExchange() {
    return new DirectExchange(RabbitMqConstant.EXCHANGE_DIRECT_WX_UNIFIED_NOTIFY, true, false);
  }

  /**
   * 交换机 队列 进行绑定
   *
   * @param queue
   * @param exchange
   * @return
   */
  @Bean
  Binding bindingWxUnifiedNotifyDirect(@Qualifier("wxUnifiedNotifyQueue") Queue queue,
      @Qualifier("wxUnifiedNotifyDirectExchange")
          Exchange exchange) {
    return BindingBuilder.bind(queue).to(exchange).with(RabbitMqConstant.ROUTINGKEY_WX_UNIFIED_NOTIFY_RULE)
        .noargs();
  }
}
