package com.shands.mod.main.service.sys.impl;

import com.shands.mod.dao.model.Region;
import com.shands.mod.main.service.sys.IRegionService;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.Tools;
import com.shands.mod.vo.TreeVO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
public class RegionServiceImpl implements IRegionService {

  @Autowired private RedisTemplate redisTemplate;

  @Override
  public List<Region> listByCn() {
    List<Region> result = null;
    BoundHashOperations<String, String, Region> op =
        this.redisTemplate.boundHashOps(BaseConstants.CACHE_REGION);
    Map<String, Region> map = op.entries();
    if (map != null && !map.isEmpty()) {
      result = new ArrayList<>(map.values());
    }
    return result;
  }

  @Override
  public List<TreeVO> treeByCn() {
    List<TreeVO> result = null;
    List<Region> list = this.listByCn();
    if (list != null && !list.isEmpty()) {
      // 初始化map
      TreeVO tmp;
      Map<Integer, TreeVO> map = new HashMap<>();
      for (Region row : list) {
        Integer id = NumberUtils.toInt(row.getId());
        // 初始化map
        tmp = map.get(row.getId());
        if (tmp == null) {
          tmp = new TreeVO(id, row.getNameCn());
        } else {
          tmp.setName(row.getNameCn());
        }
        // 去除叶节点的children
        if (id % 100 > 0) {
          tmp.setChildren(null);
        }
        map.put(id, tmp);

        // 更新pid的children
        Integer key = NumberUtils.toInt(row.getpId());
        if (key > 0) {
          TreeVO vo = map.get(key);
          if (vo == null) {
            vo = new TreeVO(key, null);
          }
          vo.getChildren().add(tmp);
          map.put(key, vo);
        }
      }

      // 重构数组
      result = new ArrayList<>();
      for (Region row : list) {
        Integer pid = NumberUtils.toInt(row.getpId());

        if (pid == 0) {
          Integer key = NumberUtils.toInt(row.getId());
          result.add(map.get(key));
        }
      }
    }
    return result;
  }

  @Override
  public List<TreeVO> treeByPid(String pid) {
    List<TreeVO> result = null;

    List<Region> list = this.listByCn();
    if (list != null && !list.isEmpty()) {
      result = new ArrayList<>();
      for (Region row : list) {
        if (StringUtils.defaultString(pid).equals(StringUtils.defaultString(row.getpId()))) {
          result.add(new TreeVO(NumberUtils.toInt(row.getId()), row.getNameCn(), null, null));
        }
      }
    }
    return result;
  }

  @Override
  public Region getById(String id) {
    Region result = Tools.getDataByRedisHash(this.redisTemplate, BaseConstants.CACHE_REGION, id);
    return result;
  }
}
