package com.shands.mod.main.service.crm.impl;

import com.shands.mod.dao.mapper.crm.HsSksCardPayLogMapper;
import com.shands.mod.dao.mapper.hs.HsHotelServiceFoodConfigMapper;
import com.shands.mod.dao.model.crm.HsSksCardPayLog;
import com.shands.mod.dao.model.enums.SksUriEnum;
import com.shands.mod.dao.model.hs.HsHotelServiceFoodConfig;
import com.shands.mod.dao.model.hs.enums.PayTypeEnum;
import com.shands.mod.dao.model.res.hs.crm.PracticeRes;
import com.shands.mod.dao.model.v0701.dto.DiscountInfoDto;
import com.shands.mod.dao.model.v0701.dto.MemberInfoDto;
import com.shands.mod.dao.model.v0701.dto.MemberPayDto;
import com.shands.mod.dao.model.v0701.dto.PayInfoDto;
import com.shands.mod.dao.model.v0701.dto.PosPluDto;
import com.shands.mod.dao.model.v0701.vo.CateringOrderInfoVo;
import com.shands.mod.dao.model.v0701.vo.PosPluVo;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.external.model.vo.MemberCardDiscountVo;
import com.shands.mod.external.model.vo.MemberInfoVo;
import com.shands.mod.external.service.SksMemberService;
import com.shands.mod.main.remote.message.MessageService;
import com.shands.mod.main.service.crm.IMemberService;
import com.shands.mod.main.service.order.OrderCommonService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.main.vo.feign.MessageVO;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.Tools;
import com.shands.mod.vo.ResultVO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-05-21
 * @description 会员支付
 */

@Slf4j
@Service
public class MemberServiceImpl implements IMemberService {

  @Value("${sks.url:http://test.server.rest.sks.com}")
  private String url;

  /**
   * 验证码有效时间
   */
  @Value("${sks.volidTimes:10}")
  private long volidTimes;

  private static final String SMS_CONTEXT = "验证码 %s，用于重置密码，%d分钟内有效。为保障信息安全，请勿告知他人。";

  @Resource
  private SksMemberService sksMemberService;

  @Resource
  private MessageService messageService;

  @Resource
  private HsHotelServiceFoodConfigMapper foodConfigMapper;

  @Resource
  private OrderCommonService orderCommonService;

  @Resource
  private HsSksCardPayLogMapper sksCardPayLogMapper;

  @Resource
  private RedisTemplate redisTemplate;

  @Override
  public List<MemberInfoVo> queryMemberCards(String mobile) {
    Assert.hasText(mobile, "手机号不能为空");
    try {
      return sksMemberService
          .qurMemberInfoByMobile(url + SksUriEnum.FINDCARDS.getUri(), mobile);
    }catch (Exception e){
      log.error("[获取会员卡列表异常]：{}",e.getMessage());
      throw new RuntimeException(e.getMessage());
    }

  }

  @Override
  public String sendCode(String mobile) {
    Assert.hasText(mobile, "手机号不能为空");

    String code = RandomStringUtils.randomNumeric(6);
    String uuid = Tools.createUUID(false);

    MessageVO message = new MessageVO();
    message.setCompanyId(1);
    message.setGroupId(BaseConstants.PLATFORM_COMPANY);
    message.setMessageType(BaseConstants.MESSAGE_TYPE_LOGIN_CODE);
    message.setReceivers(mobile);
    message.setContent(String.format(SMS_CONTEXT, code, volidTimes));
    message.setReceiverType(BaseConstants.RECEIVE_TYPE_CUSTOMER);
    message.setCreateUser(BaseConstants.SYSTEM_USER);

    ResultVO<Integer> sms = messageService.sms(message);

    if (sms != null
        && sms.getCode() != null
        && BaseConstants.RESULT_CODE_SUCCESS == sms.getCode()
        && sms.getData() != null
        && sms.getData() > 0) {
      String key = Tools.buildKey(BaseConstants.CACHE_RESETPWD, mobile, uuid);
      // 短信验证码10分钟有效
      this.redisTemplate.opsForValue().set(key, code, volidTimes, TimeUnit.MINUTES);
      return uuid;

    } else {
      throw new ServiceException("验证码发送失败");
    }

  }

  @Override
  public ResultVO verifyCode(MemberInfoDto dto) {
    Assert.hasText(dto.getMobile(), "手机号不能为空");
    Assert.hasText(dto.getCode(), "验证码不能为空");
    Assert.hasText(dto.getCardNo(), "会员卡号不能为空");
    Assert.hasText(dto.getNewPwd(), "重置密码不能为空");
    Assert.hasText(dto.getToken(), "token不能为空");

    String mobile = dto.getMobile();
    String key = Tools.buildKey(BaseConstants.CACHE_RESETPWD, mobile, dto.getToken());
    Object o = this.redisTemplate.opsForValue().get(key);

    if (o != null && !"".equals(o.toString())) {
      this.redisTemplate.delete(key);
      if (dto.getCode().equals(o)) {
        try {
          if (sksMemberService
              .resetPassword(url + SksUriEnum.RESETPWD.getUri(), dto.getCardNo(),
                  dto.getNewPwd())) {
            return ResultVO.success();
          }
          log.error("[重置sks会员密码失败]");
          return ResultVO.failed("重置密码失败");
        }catch (Exception e){
          log.error("[重置sks会员密码失败]:{}", e);
          return ResultVO.failed(e.getMessage());
        }

      }
      return ResultVO.failed("验证码输入有误");
    }
    return ResultVO.failed("验证码已失效请重试");
  }

  @Override
  public CateringOrderInfoVo calcFoodsPrice(DiscountInfoDto dto) {
    Assert.hasText(dto.getMobile(), "手机号不能为空");
    Assert.hasText(dto.getPlaceCode(), "折扣地点不能为空");
    Assert.isTrue(dto.getPosPluList().size() > 0, "菜品列表不能为空");

    CateringOrderInfoVo orderInfoVo = new CateringOrderInfoVo();
    orderInfoVo.setRemake("暂无会员卡打折信息");
    List<MemberInfoVo> memberInfoVos = queryMemberCards(dto.getMobile());

    MemberInfoVo memberInfo = new MemberInfoVo();
    if (memberInfoVos != null && memberInfoVos.size() > 0) {
      memberInfo = memberInfoVos.get(0);
    }

    MemberCardDiscountVo cardDiscountVo = sksMemberService
        .qurCardDiscount(url + SksUriEnum.DISCOUNT.getUri(), memberInfo.getCardLevel(),
            dto.getPlaceCode());

    log.info("[会员卡打折信息]：{}", cardDiscountVo);
    if (cardDiscountVo != null){
      String remark = cardDiscountVo.getRemark();
      if (remark.contains("自动折扣")){
        remark = remark.substring(0, remark.indexOf("自动折扣"));
      }
      orderInfoVo.setRemake(remark);
    }

    //计算服务费（当前计算规则：商品原总价乘以服务费率）
    HsHotelServiceFoodConfig foodConfig = new HsHotelServiceFoodConfig();
    foodConfig.setHotelServiceId(dto.getHotelServiceId());
    foodConfig.setNoteCode(dto.getNoteCode());
    foodConfig.setOutletId(dto.getOutletId());
    HsHotelServiceFoodConfig foodConfigMiddleRes = foodConfigMapper.queryBySelective(foodConfig);
    if (foodConfigMiddleRes != null && foodConfigMiddleRes.getMemberDiscount() == 0){
      cardDiscountVo = null;
      orderInfoVo.setRemake(null);
    }

    List<PosPluVo> pluVoList = new ArrayList<>();
    List<PosPluDto> pluList = dto.getPosPluList();
    for (PosPluDto pluDto : pluList) {
      PosPluVo vo = calcSingleFoodPrice(pluDto,cardDiscountVo);
      pluVoList.add(vo);
    }

    //商品原总价
    BigDecimal totalPrice = pluVoList.stream().map(PosPluVo::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
    //商品折后总价
    BigDecimal finalPrice = pluVoList.stream().map(PosPluVo::getDiscountPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
    //会员卡优惠金额
    BigDecimal remitPrice = pluVoList.stream().map(PosPluVo::getRemitPrice).reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal servicePrice;
    if (foodConfigMiddleRes != null) {
      BigDecimal divide = foodConfigMiddleRes.getServiceCharge().divide(new BigDecimal(100));
      servicePrice = totalPrice.multiply(divide).setScale(2, BigDecimal.ROUND_HALF_UP);
      orderInfoVo.setServiceCharge(foodConfigMiddleRes.getServiceCharge());
      orderInfoVo.setServicePrice(servicePrice);
    }else {
      servicePrice = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
      orderInfoVo.setServiceCharge(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP));
      orderInfoVo.setServicePrice(servicePrice);
    }

    orderInfoVo.setTotalDisPrice(finalPrice);
    orderInfoVo.setTotalPrice(totalPrice);
    orderInfoVo.setFinalPrice(finalPrice.add(servicePrice));
    orderInfoVo.setPosPluList(pluVoList);
    orderInfoVo.setRemitPrice(remitPrice);

    return orderInfoVo;

  }

  @Override
  public PosPluVo calcSingleFoodPrice(PosPluDto pluDto, MemberCardDiscountVo cardDiscountVo) {

    //食品折扣
    BigDecimal foodsDisc = BigDecimal.ONE,
        // 酒水折扣
        beveDisc = BigDecimal.ONE,
        // 其他折扣
        miscDisc = BigDecimal.ONE;

    if (cardDiscountVo != null) {

      if (cardDiscountVo.getDiscFood() != null && !"".equals(cardDiscountVo.getDiscFood())){
        foodsDisc = new BigDecimal(cardDiscountVo.getDiscFood());
      }

      if (cardDiscountVo.getDiscBeve() != null && !"".equals(cardDiscountVo.getDiscBeve())){
        beveDisc = new BigDecimal(cardDiscountVo.getDiscBeve());
      }

      if (cardDiscountVo.getDiscMisc() != null && !"".equals(cardDiscountVo.getDiscMisc())){
        miscDisc = new BigDecimal(cardDiscountVo.getDiscMisc());
      }
    }

    //商品单价
    BigDecimal detailPrice = pluDto.getPrice();
    //商品折后单价
    BigDecimal detailDisPrice = detailPrice;
    //商品原总价
    BigDecimal totalPrice = detailPrice.multiply(
        BigDecimal.valueOf(pluDto.getNum()));
    //商品折后总价
    BigDecimal discPrice = totalPrice;
    //会员卡优惠金额
    BigDecimal remitPrice = new BigDecimal(0);

    PosPluVo vo = new PosPluVo();
    BeanUtils.copyProperties(pluDto, vo);
    vo.setDiscountRatio(BigDecimal.ONE.setScale(2, BigDecimal.ROUND_HALF_UP));

    //计算做法加价
    PracticeRes practiceRes = pluDto.getPracticeRes();
    if (practiceRes != null && practiceRes.getAddPrice() != null){
      //加价后总价
      totalPrice = totalPrice.add(practiceRes.getAddPrice().multiply(BigDecimal.valueOf(pluDto.getNum())));
      //加价后折后总价
      discPrice = totalPrice;
      //加价后单价
      detailPrice = detailPrice.add(practiceRes.getAddPrice());
      //加价后折后单价
      detailDisPrice = detailDisPrice.add(practiceRes.getAddPrice());
    }

    //计算打折菜 折后价格
    if ("T".equals(pluDto.getDiscountFlag())) {

      switch (pluDto.getToCode()) {
        case BaseConstants.POSPLU_010:
          discPrice = foodsDisc.multiply(totalPrice)
              .setScale(2, BigDecimal.ROUND_HALF_UP);
          remitPrice = (BigDecimal.ONE.subtract(foodsDisc)).multiply(totalPrice)
              .setScale(2, BigDecimal.ROUND_HALF_UP);
          detailDisPrice = foodsDisc.multiply(detailPrice)
              .setScale(2, BigDecimal.ROUND_HALF_UP);
          vo.setDiscountRatio(foodsDisc);
          break;
        case BaseConstants.POSPLU_020:
          discPrice = beveDisc.multiply(totalPrice)
              .setScale(2, BigDecimal.ROUND_HALF_UP);
          remitPrice = (BigDecimal.ONE.subtract(beveDisc)).multiply(totalPrice)
              .setScale(2, BigDecimal.ROUND_HALF_UP);
          detailDisPrice = beveDisc.multiply(detailPrice)
              .setScale(2, BigDecimal.ROUND_HALF_UP);
          vo.setDiscountRatio(beveDisc);
          break;
        default:
          discPrice = miscDisc.multiply(totalPrice)
              .setScale(2, BigDecimal.ROUND_HALF_UP);
          remitPrice = (BigDecimal.ONE.subtract(miscDisc)).multiply(totalPrice)
              .setScale(2, BigDecimal.ROUND_HALF_UP);
          detailDisPrice = miscDisc.multiply(detailPrice)
              .setScale(2, BigDecimal.ROUND_HALF_UP);
          vo.setDiscountRatio(miscDisc);
          break;
      }
    }

    vo.setDetailDisPrice(detailDisPrice);
    vo.setTotalPrice(totalPrice);
    vo.setDiscountPrice(discPrice);
    vo.setRemitPrice(remitPrice);
    vo.setDetailPrice(detailPrice);

    return vo;
  }

  @Override
  public ResultVO miniPayByPassword(PayInfoDto dto) {
    Assert.hasText(dto.getPassword(), "密码不能为空");
    Assert.hasText(dto.getCardNo(), "会员卡号不能为空");
    Assert.hasText(dto.getOrderNo(), "订单号不能为空");
    Assert.hasText(dto.getCardInfo(), "会员信息不能为空");
    Assert.hasText(dto.getPlaceCode(),"折扣地点code不能为空");

    boolean b = false;
    String payOrderNo = "";
    String message = "";
    HsSksCardPayLog sksCardPayLog = new HsSksCardPayLog();
    try {
      String snNum = orderCommonService.getSnNum();
      payOrderNo = sksMemberService
          .rePay(url + SksUriEnum.PAY.getUri(), dto.getCardNo(), snNum,
              dto.getPassword(), dto.getPrice().multiply(new BigDecimal(100)).intValue(),
              dto.getPlaceCode());

      log.info("[sks订单编号]：{}", payOrderNo);

      if (payOrderNo != null && !"".equals(payOrderNo)) {
        b = orderCommonService
            .syncOrderState(Integer.valueOf(dto.getOrderNo()), PayTypeEnum.MEMBER_CARD,
                dto.getCardNo(), dto.getCardInfo());
      }
      if (b){
        return ResultVO.success();
      }else {
        return ResultVO.failed("支付失败，请重试");
      }

    }catch (Exception e){
      message = e.getMessage();
      log.error("[会员卡支付异常]：{}", message);
      return ResultVO.failed(message);
    }finally {
      sksCardPayLog.setCreateTime(new Date());
      sksCardPayLog.setDeleted(BaseConstants.DATA_UNDELETED);
      sksCardPayLog.setGroupId(2);
      sksCardPayLog.setCompanyId(ThreadLocalHelper.getCompanyId());
      sksCardPayLog.setCardNo(dto.getCardNo());
      sksCardPayLog.setCardInfo(dto.getCardInfo());
      sksCardPayLog.setPlaceCode(dto.getPlaceCode());
      sksCardPayLog.setPrice(dto.getPrice());
      sksCardPayLog.setOrderNo(dto.getOrderNo());
      sksCardPayLog.setSksNo(payOrderNo);
      sksCardPayLog.setMessage(message);
      if (b){
        sksCardPayLog.setResult(1);
      }else {
        sksCardPayLog.setResult(0);
      }
      sksCardPayLogMapper.insert(sksCardPayLog);
    }

  }

  @Override
  public ResultVO miniPayByCode(MemberPayDto dto) {
    //校验验证码并重置密码
    ResultVO resultVO = verifyCode(dto);

    log.info("[重置密码结果]：{}", resultVO);

    if (resultVO.getCode() == 0){
      PayInfoDto payInfoDto = new PayInfoDto();
      payInfoDto.setCardNo(dto.getCardNo());
      payInfoDto.setPassword(dto.getNewPwd());
      payInfoDto.setPlaceCode(dto.getPlaceCode());
      payInfoDto.setPrice(dto.getPrice());
      payInfoDto.setOrderNo(dto.getOrderNo());
      payInfoDto.setCardInfo(dto.getCardInfo());
      return miniPayByPassword(payInfoDto);
    }
    return resultVO;
  }

}
