package com.shands.mod.main.controller.crm;

import com.shands.mod.controller.BaseController;
import com.shands.mod.main.service.gw.IGwRestService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.betterwood.log.core.annotation.ResultLog;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-07-29
 * @description 官网礼遇问卷接口
 */

@RestController
@RequestMapping(value = "/gwRest")
public class GwRestController extends BaseController {

  @Resource
  private IGwRestService gwRestService;

  @Override
    @ResultLog(name = "GwRestController.isPublic", methodType = MethodTypeEnum.HTTP_UP)
  public boolean isPublic() {
    return true;
  }

  @PostMapping(value = "/getUcToken")
  @ApiOperation(value = "获取用户通宝token(拥有官网权限)")
    @ResultLog(name = "GwRestController.getUcTokenForGw", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO getUcTokenForGw(){
    return gwRestService.getUcToken(ThreadLocalHelper.getUser().getId(), "gw-admin");
  }
}
