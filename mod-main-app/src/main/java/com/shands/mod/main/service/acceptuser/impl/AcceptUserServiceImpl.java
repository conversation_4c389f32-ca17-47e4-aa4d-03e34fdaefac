package com.shands.mod.main.service.acceptuser.impl;

import com.shands.mod.dao.mapper.shift.ModShiftUserDao;
import com.shands.mod.dao.mapper.syncuc.ModUserDao;
import com.shands.mod.dao.model.hs.enums.OrderServiceEnum;
import com.shands.mod.dao.model.res.hs.staff.UserServerAreaRes;
import com.shands.mod.dao.model.syncuc.ModUser;
import com.shands.mod.main.service.acceptuser.AcceptUserService;
import com.shands.mod.main.service.crm.IUserExtendService;
import com.shands.mod.main.service.crm.IUserService;
import com.shands.mod.main.service.hotelservice.IHotelServiceExtendService;
import com.shands.mod.main.service.hotelservice.IHsServiceUserService;
import com.shands.mod.main.service.staff.IHsDutifulServeService;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/4/20
 * @desc 工单受理人获取实现类
 */
@Service
@Slf4j
public class AcceptUserServiceImpl implements AcceptUserService {

  private final IHsDutifulServeService hsDutifulServeService;

  private final IHotelServiceExtendService hotelServiceExtendService;

  private final IHsServiceUserService hsServiceUserService;

  private final IUserExtendService userExtendService;

  private final IUserService iUserService;

  @Resource
  private ModShiftUserDao modShiftUserDao;

  @Resource
  private ModUserDao modUserDao;

  public AcceptUserServiceImpl(
      IHsDutifulServeService hsDutifulServeService,
      IHotelServiceExtendService hotelServiceExtendService,
      IHsServiceUserService hsServiceUserService,
      IUserExtendService userExtendService,
      IUserService iUserService) {
    this.hsDutifulServeService = hsDutifulServeService;
    this.hotelServiceExtendService = hotelServiceExtendService;
    this.hsServiceUserService = hsServiceUserService;
    this.userExtendService = userExtendService;
    this.iUserService = iUserService;
  }

  @Override
  public UserServerAreaRes getAcceptUserNew(Integer companyId, String serviceType, Integer serviceExtend,
      String roomNumber){

    //保洁 快送
    if("CLEAN".equals(serviceType) || "DELIVER".equals(serviceType)){

      Integer userId = modShiftUserDao.getAcceptUserOne(companyId,serviceType,roomNumber);
      if(userId == null){
        userId = modShiftUserDao.getAcceptUserTwo(companyId,serviceType,roomNumber);
      }
      if(userId == null){
        userId = modShiftUserDao.getAcceptUserThree(companyId);
      }
      if(userId == null){
        throw new RuntimeException("未查询到服务受理人!");
      }

      UserServerAreaRes userServerAreaRes = modUserDao.getAreRes(userId);
      return userServerAreaRes;

    }else{
      //维修
      Integer userId = modShiftUserDao.getAcceptRepUserOne(companyId,serviceExtend);
      if(userId == null){
        userId = modShiftUserDao.getAcceptRepUserTwo(companyId,serviceExtend);
      }
      if(userId == null){
        userId = modShiftUserDao.getAcceptShiftUserOne(companyId);
      }
      if(userId == null){
        userId = modShiftUserDao.getAcceptShiftUserTwo(companyId);
      }
      if(userId == null){
        userId = modShiftUserDao.getAcceptUserThree(companyId);
      }
      if(userId == null){
        throw new RuntimeException("未查询到服务受理人!");
      }

      UserServerAreaRes userServerAreaRes = modUserDao.getAreRes(userId);
      return userServerAreaRes;
    }
  }

  /**
   * 获取工单受理人
   * @param companyId 酒店编码
   * @param serviceType 服务类型
   * @param serviceExtend 服务子项
   * @param roomNumber 房间号
   * @return 受理人Id
   */
  @Override
  public UserServerAreaRes getAcceptUser(Integer companyId, String serviceType, Integer serviceExtend,
      String roomNumber) {

    List<Integer> userIds = null;

    if(OrderServiceEnum.ROOMDINING.getServiceCode().equals(serviceType)){
      return null;
    }

    //调用区域派单规则接口查询受理人信息
    userIds = hsDutifulServeService.getUser(companyId,serviceType,serviceExtend,roomNumber);
    //过滤未开启接单模式的员工
    userIds = this.receiveFilter(userIds);

    //如果区域派单未查询到数据，则根据服务+服务子项
    if(userIds == null || userIds.isEmpty()){

      //根据服务类型 + 服务子项 + 酒店编码查询受理人信息
      userIds = hotelServiceExtendService.getUser(companyId,serviceExtend,serviceType);
      //过滤未开启接单模式的员工
      userIds = this.receiveFilter(userIds);

      if(userIds == null || userIds.isEmpty()){
        //根据服务类型查询受理人信息
        userIds = hsServiceUserService.getUser(serviceType,companyId);
        //过滤未开启接单模式的员工
        userIds = this.receiveFilter(userIds);
      }
    }

    if(userIds == null || userIds.isEmpty()){

      userIds = hsServiceUserService.getUser(serviceType,companyId);

      if(userIds == null || userIds.isEmpty()){
        log.info("[服务受理人信息查询][查询失败:{}]","服务受理人信息为空");

        if("ROOMDINING".equals(serviceType)){
          return new UserServerAreaRes();
        }else{
          throw new RuntimeException("服务受理人信息为空");
        }
      }
    }

    Random random = new Random();
    int next = random.nextInt(userIds.size());
    int userId = userIds.get(next);

    //根据用户id查询用户信息 + 部门信息
    UserServerAreaRes userServerAreaRes = modUserDao.getAreRes(userId);
    return userServerAreaRes;
  }

  /**
   * 获取工单创建受理人
   * @param companyId 酒店编码
   * @param serviceType 服务类型
   * @return 受理人Id
   */
  @Override
  public UserServerAreaRes getAcceptUser(Integer companyId, String serviceType) {

    List<Integer> userIds = null;

    //根据服务类型查询受理人信息
    userIds = hsServiceUserService.getUser(serviceType,companyId);
    //过滤未开启接单模式的员工
    userIds = this.receiveFilter(userIds);

    if(userIds == null || userIds.isEmpty()){

      userIds = hsServiceUserService.getUser(serviceType,companyId);

    }

    if(userIds != null && !userIds.isEmpty()){

      Random random = new Random();
      int next = random.nextInt(userIds.size());
      int userId = userIds.get(next);

      //根据用户id查询用户信息 + 部门信息
      UserServerAreaRes userServerAreaRes = modUserDao.getAreRes(userId);
      return userServerAreaRes;
    }

    return null;
  }

  /**
   * 酒店员工接单模式过滤
   * @param userIds
   * @return
   */
  @Override
  public List<Integer> receiveFilter(List<Integer> userIds){

    List<Integer> res = new ArrayList<>();

    if(userIds == null || userIds.isEmpty()){
      return res;
    }

    userIds.forEach( x -> {

      //根据用户id查询用户接单模式开关
      ModUser modUser = modUserDao.queryById(x);

      if(modUser != null && modUser.getReceiveOrderMode() == 1){
        res.add(x);
      }
    });

    return res;
  }
}
