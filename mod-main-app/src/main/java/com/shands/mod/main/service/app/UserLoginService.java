package com.shands.mod.main.service.app;

import com.shands.mod.dao.model.v0701.dto.ModUserSecurityVerificationDto;
import com.shands.mod.dao.model.v0701.dto.OutSourceFeiShuLoginDto;
import com.shands.mod.dao.model.v0701.vo.ModUserLoginVo;
import com.shands.mod.main.vo.SendCodeVO;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2021/8/10
 * @desc 用户登录service
*/
public interface UserLoginService {

  /**
   * APP员工端手机号登录
   * @param username 用户名
   * @param password 密码
   * @return
   */
  ModUserLoginVo loginByMobile(String username, String password, HttpServletRequest httpServletRequest);

  /**
   * APP员工端手机号登录(加密)
   * @param param 加密串
   * @return
   */
  ModUserLoginVo loginByMobileEncrypt(String param, HttpServletRequest httpServletRequest) throws Exception;

  /**
   * APP员工端验证码登录
   * @param mobile
   * @param code
   * @return
   */
  ModUserLoginVo loginByCode(String mobile, String code, HttpServletRequest httpServletRequest);

  /**
   * 飞书小程序应用免登
   * @param appId 应用id
   * @param code 小程序应用登录码
   * @return ModUserLoginVo 德胧生态登录用户信息
   */
  ModUserLoginVo loginByFeiShuMiniAppCode(String appId, String code, HttpServletRequest request);

  /**
   * 飞书网页应用免登
   * @param appId 应用id
   * @param code 网页应用登录码
   * @return ModUserLoginVo 德胧生态登录用户信息
   */
  ModUserLoginVo loginByFeiShuWebAppCode(String appId, String code, HttpServletRequest request);

  /**
   * 飞书应用外协人员切换机构免登
   * @return ModUserLoginVo 德胧生态登录用户信息
   */
  ModUserLoginVo changeOrgLogin(OutSourceFeiShuLoginDto outSourceFeiShuLoginDto, HttpServletRequest request);

  /**
   * 校验token是否有效
   * @param token 用户token
   * @return true 有效 false 无效
   */
  Boolean tokenValid(String token);

  /**
   * 校验是否是新登录设备
   * @param mobile
   * @param httpServletRequest
   * @return
   */
  Boolean newDeviceValid(String mobile, HttpServletRequest httpServletRequest);


  /**
   * 登录成功后组装登录信息
   * @param mobile
   * @param httpServletRequest
   * @return
   */
  ModUserLoginVo getModUserLoginVo(String mobile,HttpServletRequest httpServletRequest,boolean changeDevice);

  /**
   * 验证用户状态
   * @param mobile
   */
  void validateUser(String mobile);


  int sendCode(SendCodeVO sendCodeVO);

  /**
   * 安全校验
   * @return
   */
  ModUserLoginVo securityVerification(ModUserSecurityVerificationDto dto, HttpServletRequest httpServletRequest);


  /**
   * 初始化密码
   * @return
   */
  ModUserLoginVo initPassword(ModUserSecurityVerificationDto dto, HttpServletRequest httpServletRequest);



}
