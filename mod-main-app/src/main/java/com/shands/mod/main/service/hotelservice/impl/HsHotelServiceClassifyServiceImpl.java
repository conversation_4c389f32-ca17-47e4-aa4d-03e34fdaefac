package com.shands.mod.main.service.hotelservice.impl;



import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.shands.mod.dao.mapper.hs.HsHotelServiceClassifyMapper;
import com.shands.mod.dao.mapper.hs.HsHotelServiceExtendMapper;
import com.shands.mod.dao.model.enums.StatusEnums;
import com.shands.mod.dao.model.hs.HsHotelServiceClassify;
import com.shands.mod.dao.model.req.hs.BaseIdReq;
import com.shands.mod.dao.model.req.hs.serviceClassify.ServiceClassifyAddReq;
import com.shands.mod.dao.model.req.hs.serviceClassify.ServiceClassifyUpdateReq;
import com.shands.mod.dao.model.req.hs.serviceClassify.ServiceClassifyUpdateStatusReq;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.main.service.hotelservice.HsHotelServiceClassifyService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.vo.ResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【hs_hotel_service_classify(酒店服务分类)】的数据库操作Service实现
* @createDate 2023-08-29 11:06:04
*/
@Service
@RequiredArgsConstructor
@Slf4j
public class HsHotelServiceClassifyServiceImpl implements HsHotelServiceClassifyService {

  private final HsHotelServiceClassifyMapper hotelServiceClassifyMapper;
  private final HsHotelServiceExtendMapper hotelServiceExtendMapper;

  @Override
  public ResultVO<Boolean> add(ServiceClassifyAddReq req) {
    int companyId = ThreadLocalHelper.getCompanyId();
    log.info("酒店服务新增入参：[{}]->[{}]", req, companyId);
    if (hotelServiceClassifyMapper.selectByCompanyIdAndCode(companyId, req.getCode()) != null) {
      throw new ServiceException("存在重复项，请检查代码" + req.getCode());
    }
    if (hotelServiceClassifyMapper.selectByCompanyIdAndName(companyId, req.getName()) != null) {
      throw new ServiceException("存在重复项，请检查服务分类" + req.getName());
    }
    HsHotelServiceClassify classify = new HsHotelServiceClassify();
    classify.setCompanyId(companyId);
    classify.setCode(req.getCode());
    classify.setName(req.getName());
    classify.setStatus(req.getStatus());
    classify.setCreateUser(ThreadLocalHelper.getUser().getName());

    return ResultVO.success(hotelServiceClassifyMapper.insertSelective(classify) > 0);
  }

  @Override
  public ResultVO<Boolean> update(ServiceClassifyUpdateReq req) {
    Long id = req.getId();
    log.info("酒店服务更新入参：[{}]->", req);
    HsHotelServiceClassify classify = hotelServiceClassifyMapper.selectById(id);
    if (classify == null) {
      throw new ServiceException("服务内容不存在");
    }

    Integer companyId = classify.getCompanyId();
    //更新名称
    if (!StringUtils.equals(classify.getName(), req.getName())) {
      hotelServiceExtendMapper.updateNameByServiceClassifyId(companyId, id, req.getName());
    }
    //无效校验
    if (StatusEnums.INVALID.getCode().equals(req.getStatus()) &&
        CollUtil.isNotEmpty(hotelServiceExtendMapper.getByServiceClassifyId(ThreadLocalHelper.getCompanyId(), req.getId()))) {
      throw new ServiceException("已有服务内容关联此服务分类，该项不可设置为无效");
    }
    HsHotelServiceClassify byCode = hotelServiceClassifyMapper.selectByCompanyIdAndCode(companyId, req.getCode());
    if (byCode != null && !byCode.getId().equals(id)) {
      throw new ServiceException("存在重复项，请检查代码" + req.getCode());
    }
    HsHotelServiceClassify byName = hotelServiceClassifyMapper.selectByCompanyIdAndName(companyId, req.getName());
    if (byName != null && !byName.getId().equals(id)) {
      throw new ServiceException("存在重复项，请检查服务分类" + req.getName());
    }

    classify.setCode(req.getCode());
    classify.setName(req.getName());
    classify.setStatus(req.getStatus());
    classify.setUpdateUser(ThreadLocalHelper.getUser().getName());
    classify.setUpdateTime(new Date());

    return ResultVO.success(hotelServiceClassifyMapper.updateByPrimaryKeySelective(classify) > 0);
  }

  @Override
  public ResultVO<Boolean> updateStatus(ServiceClassifyUpdateStatusReq req) {
    log.info("酒店服务更新状态入参：[{}]->", req);
    if (StatusEnums.INVALID.getCode().equals(req.getStatus()) &&
        CollUtil.isNotEmpty(hotelServiceExtendMapper.getByServiceClassifyId(ThreadLocalHelper.getCompanyId(), req.getId()))) {
        throw new ServiceException("已有服务内容关联此服务分类，该项不可设置为无效");
    }
    return ResultVO.success(hotelServiceClassifyMapper.updateStatus(req.getId(), req.getStatus()) > 0);
  }

  @Override
  public ResultVO<Boolean> delete(BaseIdReq req) {
    log.info("酒店服务删除入参：[{}]->", req);
    if (CollUtil.isNotEmpty(hotelServiceExtendMapper.getByServiceClassifyId(ThreadLocalHelper.getCompanyId(), req.getId()))) {
      throw new ServiceException("已有服务内容关联此服务分类，该项不可删除");
    }
    return ResultVO.success(hotelServiceClassifyMapper.deleteById(req.getId()) > 0);
  }

  @Override
  public ResultVO<PageInfo<HsHotelServiceClassify>> query(Integer pageNo, Integer pageSize, String name, Integer status) {
    if (pageNo == null && pageSize == null) {
      pageNo = 1;
      pageSize = 10;

    }
    PageHelper.startPage(pageNo, pageSize);

    return ResultVO.success(new PageInfo<>(
        hotelServiceClassifyMapper.select(ThreadLocalHelper.getCompanyId(), name, status)
    ));
  }
}




