package com.shands.mod.main.service.datarevision.impl;

import com.delonix.bi.dao.mapper.AdsTradeConsumerMemberMapper;
import com.shands.mod.dao.mapper.BiDrptMemberUserSrcMapper;
import com.shands.mod.dao.mapper.BiMemberPromotionPrizeMapper;
import com.shands.mod.dao.mapper.DwsMemTargetIncDayMapper;
import com.shands.mod.dao.mapper.board.ModUserTempDetailsMapper;
import com.shands.mod.dao.mapper.datarevision.DeptDataMapper;
import com.shands.mod.dao.mapper.datarevision.DwsScmUniformPurchaseRateMapper;
import com.shands.mod.dao.mapper.homerevision.DwsTradeRevenueIncDayDao;
import com.shands.mod.dao.mapper.syncuc.ModUserRegisterMapper;
import com.shands.mod.dao.model.datarevision.bo.ManagementAnalysisBo;
import com.shands.mod.dao.model.datarevision.vo.ClassificationVo;
import com.shands.mod.dao.model.datarevision.vo.ManagementAnalysisDetailsVo;
import com.shands.mod.dao.model.datarevision.vo.ManagementAnalysisVo;
import com.shands.mod.dao.model.datarevision.vo.StatisticsMenuVo;
import com.shands.mod.dao.model.enums.HotelTypeEnum;
import com.shands.mod.dao.model.enums.StatisticsDataMenuEnum;
import com.shands.mod.dao.model.enums.UserRightsTypeEnum;
import com.shands.mod.dao.model.enums.YearTypeEnum;
import com.shands.mod.dao.model.homerevision.ManagementAnalysisEnum;
import com.shands.mod.dao.model.homerevision.StatisticsDataEnum;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.main.service.common.HotelInfoCommonService;
import com.shands.mod.main.service.datarevision.DateRevisionDetailService;
import com.shands.mod.main.util.DateUtils;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.service.BaseHotelInfoCommonService;
import com.shands.mod.service.ModUserCommonService;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/**
 * @Description:
 * @Author: Java菜鸟
 * @CreateDate: 2022/5/31 16:11
 */
@Slf4j
@Service
@SuppressWarnings("all")
@RequiredArgsConstructor
public class DateRevisionDetailServiceImpl implements DateRevisionDetailService {

  private final DwsTradeRevenueIncDayDao dwsTradeRevenueIncDayDao;
  private final ModUserTempDetailsMapper modUserTempDetailsMapper;
  private final BaseHotelInfoCommonService baseHotelInfoCommonService;
  private final HotelInfoCommonService hotelInfoCommonService;
  private final ModUserCommonService modUserCommonService;
  private final BiDrptMemberUserSrcMapper drptMemberUserSrcMapper;
  private final DwsMemTargetIncDayMapper dwsMemTargetIncDayMapper;
  private final BiMemberPromotionPrizeMapper memberPromotionPrizeMapper;
  private final DeptDataMapper deptDataMapper;
  private final ModUserRegisterMapper userRegisterMapper;
  private final DwsScmUniformPurchaseRateMapper dwsScmUniformPurchaseRateMapper;
  private final AdsTradeConsumerMemberMapper adsTradeConsumerMemberMapper;

  @Value("${els.h5url:https://testx-m.kaiyuanhotels.com}")
  private String sxeUrl;
  @Override
  public List<StatisticsMenuVo> statisticsDataMenuList(String type) {
    List<String> param = modUserCommonService.getUserRights(
        ThreadLocalHelper.getUser().getId(),
        ThreadLocalHelper.getCompanyId(),
        UserRightsTypeEnum.APP);

    if (null == param || param.size() == 0 ){
      log.info("权限为空！{}",ThreadLocalHelper.getUser().getId());
      return null;
    }

    List<StatisticsMenuVo> moduleMenuVos = new ArrayList<>();
    if (HotelTypeEnum.BLOC.name().equals(type)){
      //经营分析
      if(param.contains(StatisticsDataMenuEnum.Bloc_operate_analyze.getDataCode())) {
        StatisticsMenuVo moduleMenuDailyVo = new StatisticsMenuVo();
        moduleMenuDailyVo.setModuleName(StatisticsDataEnum.OPERATE_ANALYZE.getModuleName());
        moduleMenuDailyVo.setModuleCode(StatisticsDataEnum.OPERATE_ANALYZE.getModuleCode());
        moduleMenuDailyVo.setUrl(sxeUrl + "/sxe/businessAnalysis");
        moduleMenuVos.add(moduleMenuDailyVo);
      }
      //会员分析
      if(param.contains(StatisticsDataMenuEnum.Bloc_member_analyze.getDataCode())) {
        StatisticsMenuVo moduleMenuMemberVo = new StatisticsMenuVo();
        moduleMenuMemberVo.setModuleName(StatisticsDataEnum.MEMBER_ANALYZE.getModuleName());
        moduleMenuMemberVo.setModuleCode(StatisticsDataEnum.MEMBER_ANALYZE.getModuleCode());
        moduleMenuMemberVo.setUrl(sxeUrl + "/sxe/memberAnalyse");
        moduleMenuVos.add(moduleMenuMemberVo);
      }
      //网评分析
      if(param.contains(StatisticsDataMenuEnum.Bloc_comment_analyze.getDataCode())) {
        StatisticsMenuVo moduleMenuGwDailyVo = new StatisticsMenuVo();
        moduleMenuGwDailyVo.setModuleName(StatisticsDataEnum.COMMENT_ANALYZE.getModuleName());
        moduleMenuGwDailyVo.setModuleCode(StatisticsDataEnum.COMMENT_ANALYZE.getModuleCode());
        moduleMenuGwDailyVo.setUrl( sxeUrl + "/sxe/netComment/memberStatis");
        moduleMenuVos.add(moduleMenuGwDailyVo);
      }
      //官网分析
      if(param.contains(StatisticsDataMenuEnum.Bloc_gw_analyze.getDataCode())) {
        StatisticsMenuVo moduleMenuCommentVo = new StatisticsMenuVo();
        moduleMenuCommentVo.setModuleName(StatisticsDataEnum.GW_ANALYZE.getModuleName());
        moduleMenuCommentVo.setModuleCode(StatisticsDataEnum.GW_ANALYZE.getModuleCode());
        moduleMenuCommentVo.setUrl(sxeUrl + "/sxe/Offical");
        moduleMenuVos.add(moduleMenuCommentVo);
      }

      //项目分析
      if(param.contains(StatisticsDataMenuEnum.Bloc_project_analyze.getDataCode())) {
        StatisticsMenuVo moduleMenuCommentVo = new StatisticsMenuVo();
        moduleMenuCommentVo.setModuleName(StatisticsDataEnum.PROJECT_ANALYZE.getModuleName());
        moduleMenuCommentVo.setModuleCode(StatisticsDataEnum.PROJECT_ANALYZE.getModuleCode());
        moduleMenuCommentVo.setUrl(sxeUrl + "/sxe/itemAnalyse");
        moduleMenuVos.add(moduleMenuCommentVo);
      }

    }else if (HotelTypeEnum.HOTEL.name().equals(type)){
      //经营数据
      if(param.contains(StatisticsDataMenuEnum.Hotel_operate_analyze.getDataCode())) {
        StatisticsMenuVo moduleMenuDailyVo = new StatisticsMenuVo();
        moduleMenuDailyVo.setModuleName(StatisticsDataEnum.OPERATE_ANALYZE.getModuleName());
        moduleMenuDailyVo.setModuleCode(StatisticsDataEnum.OPERATE_ANALYZE.getModuleCode());
        moduleMenuDailyVo.setUrl(sxeUrl + "/sxe/businessAnalysis");
        moduleMenuVos.add(moduleMenuDailyVo);
      }
      //会员数据
      if(param.contains(StatisticsDataMenuEnum.Hotel_member_analyze.getDataCode())) {
        StatisticsMenuVo moduleMenuMemberVo = new StatisticsMenuVo();
        moduleMenuMemberVo.setModuleName(StatisticsDataEnum.MEMBER_ANALYZE.getModuleName());
        moduleMenuMemberVo.setModuleCode(StatisticsDataEnum.MEMBER_ANALYZE.getModuleCode());
        moduleMenuMemberVo.setUrl(sxeUrl + "/sxe/memberAnalyse");
        moduleMenuVos.add(moduleMenuMemberVo);
      }
      //网评分析
//      if(param.contains(StatisticsDataMenuEnum.Hotel_comment_analyze.getDataCode())) {
//        StatisticsMenuVo moduleMenuGwDailyVo = new StatisticsMenuVo();
//        moduleMenuGwDailyVo.setModuleName(StatisticsDataEnum.COMMENT_ANALYZE.getModuleName());
//        moduleMenuGwDailyVo.setModuleCode(StatisticsDataEnum.COMMENT_ANALYZE.getModuleCode());
//        moduleMenuGwDailyVo.setUrl(sxeUrl + "/sxe/netComment/memberStatis");
//        moduleMenuVos.add(moduleMenuGwDailyVo);
//      }
      //服务统计
      if(param.contains(StatisticsDataMenuEnum.Hotel_service_statistics.getDataCode())) {
        StatisticsMenuVo moduleMenuServiceVo = new StatisticsMenuVo();
        moduleMenuServiceVo.setModuleName(StatisticsDataEnum.SERVICE_STATISTICS.getModuleName());
        moduleMenuServiceVo.setModuleCode(StatisticsDataEnum.SERVICE_STATISTICS.getModuleCode());
        moduleMenuVos.add(moduleMenuServiceVo);
      }
    }
    return moduleMenuVos;
  }

  @Override
  public List<StatisticsMenuVo> memberH5MenuList(String type) {
    List<String> param = modUserCommonService.getUserRights(ThreadLocalHelper.getUser().getId(),
        ThreadLocalHelper.getCompanyId(),
        UserRightsTypeEnum.APP);
    if (null == param || param.size() == 0 ){
      log.info("权限为空！{}",ThreadLocalHelper.getUser().getId());
      return null;
    }
    List<String> codes = modUserCommonService.getUserDataBord(ThreadLocalHelper.getUser().getId());
    List<StatisticsMenuVo> moduleMenuVos = new ArrayList<>();
    if (HotelTypeEnum.BLOC.name().equals(type)){
      //面对面会员发展
      if(param.contains(StatisticsDataMenuEnum.Bloc_face_member.getDataCode())) {
        StatisticsMenuVo moduleFaceMemberVo = new StatisticsMenuVo();
        //Date maxDate = drptMemberUserSrcMapper.findMaxDate();
        moduleFaceMemberVo.setModuleName(StatisticsDataEnum.FACE_MEMBER.getModuleName());
        moduleFaceMemberVo.setModuleCode(StatisticsDataEnum.FACE_MEMBER.getModuleCode());
        moduleFaceMemberVo.setDataTime(DateUtils.dateToString(new Date(),DateUtils.DATE_FORMAT_YYYY_MM_DD_N));
        moduleMenuVos.add(moduleFaceMemberVo);
      }
      //官网激励产量
      if(param.contains(StatisticsDataMenuEnum.Bloc_gw_excitation.getDataCode())) {
        StatisticsMenuVo moduleGwExcitationVo = new StatisticsMenuVo();
        Date maxDate = memberPromotionPrizeMapper.findMaxDate();
        moduleGwExcitationVo.setModuleName(StatisticsDataEnum.GW_EXCITATION.getModuleName());
        moduleGwExcitationVo.setModuleCode(StatisticsDataEnum.GW_EXCITATION.getModuleCode());
        moduleGwExcitationVo.setDataTime(DateUtils.dateToString(maxDate,DateUtils.DATE_FORMAT_YYYY_MM_DD_N));
        moduleMenuVos.add(moduleGwExcitationVo);
      }
      //会员发展完成率
      if(param.contains(StatisticsDataMenuEnum.Bloc_member_finish.getDataCode())) {
        StatisticsMenuVo moduleMemberFinishVo = new StatisticsMenuVo();
        Date maxDate = dwsMemTargetIncDayMapper.findMaxDate();
        moduleMemberFinishVo.setModuleName(StatisticsDataEnum.MEMBER_FINISH.getModuleName());
        moduleMemberFinishVo.setModuleCode(StatisticsDataEnum.MEMBER_FINISH.getModuleCode());
        moduleMemberFinishVo.setDataTime(DateUtils.dateToString(maxDate,DateUtils.DATE_FORMAT_YYYY_MM_DD_N));
        moduleMenuVos.add(moduleMemberFinishVo);
      }
      //员工激活率
      if(param.contains(StatisticsDataMenuEnum.Bloc_staff_activation.getDataCode())) {
        StatisticsMenuVo moduleStaffActivationVo = new StatisticsMenuVo();
        Date maxDate =userRegisterMapper.findMaxDate();
        moduleStaffActivationVo.setModuleName(StatisticsDataEnum.STAFF_ACTIVATION.getModuleName());
        moduleStaffActivationVo.setModuleCode(StatisticsDataEnum.STAFF_ACTIVATION.getModuleCode());
        moduleStaffActivationVo.setDataTime(DateUtils.dateToString(maxDate,DateUtils.DATE_FORMAT_YYYY_MM_DD_N));
        moduleMenuVos.add(moduleStaffActivationVo);
      }

    }else if (HotelTypeEnum.HOTEL.name().equals(type)){
      Date maxDate = dwsTradeRevenueIncDayDao.findMaxDate();
      String dateToString = DateUtils.dateToString(maxDate, DateUtils.DATE_FORMAT_YYYY_MM_DD_N);
      //面对面会员发展
      if(param.contains(StatisticsDataMenuEnum.Hotel_face_member.getDataCode())) {
        StatisticsMenuVo moduleFaceMemberVo = new StatisticsMenuVo();
        moduleFaceMemberVo.setModuleName(StatisticsDataEnum.FACE_MEMBER.getModuleName());
        moduleFaceMemberVo.setModuleCode(StatisticsDataEnum.FACE_MEMBER.getModuleCode());
        moduleFaceMemberVo.setDataTime(dateToString);
        moduleMenuVos.add(moduleFaceMemberVo);
      }
      //会员发展（绑定企业）
      if(param.contains(StatisticsDataMenuEnum.Hotel_enterprise_member.getDataCode())) {
        StatisticsMenuVo moduleGwExcitationVo = new StatisticsMenuVo();
        moduleGwExcitationVo.setModuleName(StatisticsDataEnum.ENTERPRISE_MEMBER.getModuleName());
        moduleGwExcitationVo.setModuleCode(StatisticsDataEnum.ENTERPRISE_MEMBER.getModuleCode());
        moduleGwExcitationVo.setDataTime(dateToString);
        moduleMenuVos.add(moduleGwExcitationVo);
      }
      //消费会员
      if(param.contains(StatisticsDataMenuEnum.Hotel_consumption_member.getDataCode())) {
        Date consumerMaxDate = adsTradeConsumerMemberMapper.findMaxDateByConsumer();
        StatisticsMenuVo moduleMemberFinishVo = new StatisticsMenuVo();
        moduleMemberFinishVo.setModuleName(StatisticsDataEnum.CONSUMPTION_MEMBER.getModuleName());
        moduleMemberFinishVo.setModuleCode(StatisticsDataEnum.CONSUMPTION_MEMBER.getModuleCode());
        moduleMemberFinishVo.setDataTime(DateUtils.dateToString(consumerMaxDate, DateUtils.DATE_FORMAT_YYYY_MM_DD_N));
        moduleMenuVos.add(moduleMemberFinishVo);
      }
      //新增消费会员
      if(param.contains(StatisticsDataMenuEnum.Hotel_new_consumption_member.getDataCode())) {
        StatisticsMenuVo moduleStaffActivationVo = new StatisticsMenuVo();
        moduleStaffActivationVo.setModuleName(StatisticsDataEnum.NEW_CONSUMPTION_MEMBER.getModuleName());
        moduleStaffActivationVo.setModuleCode(StatisticsDataEnum.NEW_CONSUMPTION_MEMBER.getModuleCode());
        moduleStaffActivationVo.setDataTime(dateToString);
        moduleMenuVos.add(moduleStaffActivationVo);
      }
    }
    return moduleMenuVos;
  }

  @Override
  public List<StatisticsMenuVo> consumerH5MenuList() {
    List<String> param = modUserCommonService.getUserRights(ThreadLocalHelper.getUser().getId(), ThreadLocalHelper.getCompanyId(), UserRightsTypeEnum.APP);
    if (null == param || param.size() == 0 ){
      log.info("权限为空！{}",ThreadLocalHelper.getUser().getId());
      return null;
    }
    List<String> codes = modUserCommonService.getUserDataBord(ThreadLocalHelper.getUser().getId());
    List<StatisticsMenuVo> moduleMenuVos = new ArrayList<>();
    Date maxDate = dwsTradeRevenueIncDayDao.findMaxDate();
    String dateToString = DateUtils.dateToString(maxDate, DateUtils.DATE_FORMAT_YYYY_MM_DD_N);
    if (param.contains(StatisticsDataMenuEnum.Hotel_Member_Revenue.getDataCode())) {
      Date memberMaxDate = deptDataMapper.findMaxDate();
      StatisticsMenuVo menuVo = new StatisticsMenuVo();
      menuVo.setModuleName(StatisticsDataEnum.MEMBER_REVENUE.getModuleName());
      menuVo.setModuleCode(StatisticsDataEnum.MEMBER_REVENUE.getModuleCode());
      menuVo.setDataTime(DateUtils.dateToString(memberMaxDate, DateUtils.DATE_FORMAT_YYYY_MM_DD_N));
      moduleMenuVos.add(menuVo);
    }
    if (param.contains(StatisticsDataMenuEnum.Hotel_Room_Night.getDataCode())) {
      StatisticsMenuVo menuVo = new StatisticsMenuVo();
      menuVo.setModuleName(StatisticsDataEnum.ROOM_RIGHT.getModuleName());
      menuVo.setModuleCode(StatisticsDataEnum.ROOM_RIGHT.getModuleCode());
      menuVo.setDataTime(dateToString);
      moduleMenuVos.add(menuVo);
    }
    if (param.contains(StatisticsDataMenuEnum.Hotel_Room_Revenue.getDataCode())) {
      StatisticsMenuVo menuVo = new StatisticsMenuVo();
      menuVo.setModuleName(StatisticsDataEnum.ROOM_REVENUE.getModuleName());
      menuVo.setModuleCode(StatisticsDataEnum.ROOM_REVENUE.getModuleCode());
      menuVo.setDataTime(dateToString);
      moduleMenuVos.add(menuVo);
    }
    return moduleMenuVos;
  }

  @Override
  public List<ManagementAnalysisVo> managementAnalysisVos(ManagementAnalysisBo managementAnalysisBo) {
    long l = System.currentTimeMillis();
    List<ManagementAnalysisVo> vos = new ArrayList<>();
    int count = 0;
    ModHotelInfo modHotelInfo = hotelInfoCommonService.findHotelInfo(ThreadLocalHelper.getCompanyId());
    String hotelType = "BLOC";
    //集团
    if ("000001".equals(modHotelInfo.getHotelCode())){
      if (null == managementAnalysisBo.getHotelCodes()){
        List<String> codes = modUserCommonService.getUserDataBord(ThreadLocalHelper.getUser().getId());
        codes = dwsTradeRevenueIncDayDao.analysisCodes(codes);
        managementAnalysisBo.setHotelCodes(codes);
      }
    }else {
      //酒店
      List<String> codes = new ArrayList<>();
      codes.add(modHotelInfo.getHotelCode());
      managementAnalysisBo.setHotelCodes(codes);
      hotelType = "HOTEL";
    }
    //今年
    List<ManagementAnalysisDetailsVo> thisYear = dwsTradeRevenueIncDayDao.selectAnalysis(YearTypeEnum.THIS_YEAR.name(), managementAnalysisBo,hotelType);
    //去年
    List<ManagementAnalysisDetailsVo> lastYear = dwsTradeRevenueIncDayDao.selectAnalysis(YearTypeEnum.LAST_YEAR.name(), managementAnalysisBo,hotelType);
    //OCC趋势
    ManagementAnalysisVo managementAnalysisOCCVo = new ManagementAnalysisVo();
    List<ClassificationVo> occThisYear =  thisYear.stream().map(s -> new ClassificationVo(s.getOCC(),s.getTime())).collect(Collectors.toList());
    List<ClassificationVo> occLastYear =  lastYear.stream().map(s -> new ClassificationVo(s.getOCC(),s.getTime())).collect(Collectors.toList());
    managementAnalysisOCCVo.setModuleName(ManagementAnalysisEnum.ZH_OCC_TREND.getModuleName());
    managementAnalysisOCCVo.setUnit(ManagementAnalysisEnum.ZH_OCC_TREND.getUnit());
    managementAnalysisOCCVo.setGeneralDrawingThisYearVos(occThisYear);
    managementAnalysisOCCVo.setGeneralDrawingLastYearVos(occLastYear);
    vos.add(managementAnalysisOCCVo);
    // ADR 趋势
    ManagementAnalysisVo managementAnalysisADRVo = new ManagementAnalysisVo();
    List<ClassificationVo> adrThisYear =  thisYear.stream().map(s -> new ClassificationVo(s.getADR(),s.getTime())).collect(Collectors.toList());
    List<ClassificationVo> adrLastYear =  lastYear.stream().map(s -> new ClassificationVo(s.getADR(),s.getTime())).collect(Collectors.toList());
    managementAnalysisADRVo.setModuleName(ManagementAnalysisEnum.ZH_ADR_TREND.getModuleName());
    managementAnalysisADRVo.setUnit(ManagementAnalysisEnum.ZH_ADR_TREND.getUnit());
    managementAnalysisADRVo.setGeneralDrawingThisYearVos(adrThisYear);
    managementAnalysisADRVo.setGeneralDrawingLastYearVos(adrLastYear);
    vos.add(managementAnalysisADRVo);
    //RevPAR趋势
    ManagementAnalysisVo managementAnalysisRevPARVo = new ManagementAnalysisVo();
    List<ClassificationVo> revPARThisYear =  thisYear.stream().map(s -> new ClassificationVo(s.getRevPAR(),s.getTime())).collect(Collectors.toList());
    List<ClassificationVo> revPARLastYear =  lastYear.stream().map(s -> new ClassificationVo(s.getRevPAR(),s.getTime())).collect(Collectors.toList());
    managementAnalysisRevPARVo.setModuleName(ManagementAnalysisEnum.ZH_RevPAR_TREND.getModuleName());
    managementAnalysisRevPARVo.setUnit(ManagementAnalysisEnum.ZH_RevPAR_TREND.getUnit());
    managementAnalysisRevPARVo.setGeneralDrawingThisYearVos(revPARThisYear);
    managementAnalysisRevPARVo.setGeneralDrawingLastYearVos(revPARLastYear);
    vos.add(managementAnalysisRevPARVo);
    //营业收入趋势
    ManagementAnalysisVo managementAnalysisBusinessVo = new ManagementAnalysisVo();
    List<ClassificationVo> businessThisYear =  thisYear.stream().map(s -> new ClassificationVo(s.getOperatingIncom(),s.getTime())).collect(Collectors.toList());
    List<ClassificationVo> businessLastYear =  lastYear.stream().map(s -> new ClassificationVo(s.getOperatingIncom(),s.getTime())).collect(Collectors.toList());
    managementAnalysisBusinessVo.setModuleName(ManagementAnalysisEnum.BUSINESS_INCOME_TREND.getModuleName());
    managementAnalysisBusinessVo.setUnit(ManagementAnalysisEnum.BUSINESS_INCOME_TREND.getUnit());
    managementAnalysisBusinessVo.setGeneralDrawingThisYearVos(businessThisYear);
    managementAnalysisBusinessVo.setGeneralDrawingLastYearVos(businessLastYear);
    vos.add(managementAnalysisBusinessVo);
    //房费收入趋势
    ManagementAnalysisVo managementAnalysisRoomVo = new ManagementAnalysisVo();
    List<ClassificationVo> roomThisYear =  thisYear.stream().map(s -> new ClassificationVo(s.getRoomIncom(),s.getTime())).collect(Collectors.toList());
    List<ClassificationVo> roomLastYear =  lastYear.stream().map(s -> new ClassificationVo(s.getRoomIncom(),s.getTime())).collect(Collectors.toList());
    managementAnalysisRoomVo.setModuleName(ManagementAnalysisEnum.ROOM_INCOME_TREND.getModuleName());
    managementAnalysisRoomVo.setUnit(ManagementAnalysisEnum.ROOM_INCOME_TREND.getUnit());
    managementAnalysisRoomVo.setGeneralDrawingThisYearVos(roomThisYear);
    managementAnalysisRoomVo.setGeneralDrawingLastYearVos(roomLastYear);
    vos.add(managementAnalysisRoomVo);
    //餐饮收入趋势
    ManagementAnalysisVo managementAnalysisFoodVo = new ManagementAnalysisVo();
    List<ClassificationVo> foodThisYear =  thisYear.stream().map(s -> new ClassificationVo(s.getFoodIncom(),s.getTime())).collect(Collectors.toList());
    List<ClassificationVo> foodLastYear =  lastYear.stream().map(s -> new ClassificationVo(s.getFoodIncom(),s.getTime())).collect(Collectors.toList());
    managementAnalysisFoodVo.setModuleName(ManagementAnalysisEnum.FOOD_INCOME_TREND.getModuleName());
    managementAnalysisFoodVo.setUnit(ManagementAnalysisEnum.FOOD_INCOME_TREND.getUnit());
    managementAnalysisFoodVo.setGeneralDrawingThisYearVos(foodThisYear);
    managementAnalysisFoodVo.setGeneralDrawingLastYearVos(foodLastYear);
    vos.add(managementAnalysisFoodVo);
    //其他收入趋势
    ManagementAnalysisVo managementAnalysisOtherVo = new ManagementAnalysisVo();
    List<ClassificationVo> otherThisYear =  thisYear.stream().map(s -> new ClassificationVo(s.getOtherIncom(),s.getTime())).collect(Collectors.toList());
    List<ClassificationVo> otherLastYear =  lastYear.stream().map(s -> new ClassificationVo(s.getOtherIncom(),s.getTime())).collect(Collectors.toList());
    managementAnalysisOtherVo.setModuleName(ManagementAnalysisEnum.OTHER_INCOME_TREND.getModuleName());
    managementAnalysisOtherVo.setUnit(ManagementAnalysisEnum.OTHER_INCOME_TREND.getUnit());
    managementAnalysisOtherVo.setGeneralDrawingThisYearVos(otherThisYear);
    managementAnalysisOtherVo.setGeneralDrawingLastYearVos(otherLastYear);
    vos.add(managementAnalysisOtherVo);
    //供应链
    ManagementAnalysisVo managementAnalysisScmVo = new ManagementAnalysisVo();
    List<ClassificationVo> thisScm = dwsScmUniformPurchaseRateMapper.selectAnalysis(
        YearTypeEnum.THIS_YEAR.name(), managementAnalysisBo);
    List<ClassificationVo> lastScm = dwsScmUniformPurchaseRateMapper.selectAnalysis(
        YearTypeEnum.LAST_YEAR.name(), managementAnalysisBo);
    managementAnalysisScmVo.setModuleName(ManagementAnalysisEnum.SCM_PURCHASE_TREND.getModuleName());
    managementAnalysisScmVo.setUnit(ManagementAnalysisEnum.SCM_PURCHASE_TREND.getUnit());
    managementAnalysisScmVo.setGeneralDrawingThisYearVos(thisScm);
    managementAnalysisScmVo.setGeneralDrawingLastYearVos(lastScm);
    vos.add(managementAnalysisScmVo);
    log.info("耗时——{}",System.currentTimeMillis()-l);
    return vos;
  }
}
