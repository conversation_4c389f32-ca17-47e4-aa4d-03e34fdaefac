package com.shands.mod.main.service.betterwood.rpc.req;


import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CategoryReq {

  @ApiModelProperty("产品类型")
  private String categorySub;
  @ApiModelProperty("客户类型")
  private String guesttypeCode;
  @ApiModelProperty("协议号，可能会有多个")
  private List<String> custAccountList;

  @ApiModelProperty("是否查询权益")
  private Boolean needQueryRights;

  /**
   * 3 为企业会员 4 为协议会员
   */
  @ApiModelProperty(value = "价格类型 主要用于区分门店协议和集团协议",
      example = "0普通 2兑换券 3集团协议 4门店协议")
  private Integer priceType;

}
