package com.shands.mod.main.service.hotelservice.impl;

import com.shands.mod.dao.mapper.ModRouteMapper;
import com.shands.mod.dao.mapper.hs.HotelServiceMapper;
import com.shands.mod.dao.model.ModRoute;
import com.shands.mod.dao.model.ModService;
import com.shands.mod.dao.model.hs.HotelService;
import com.shands.mod.main.service.hotelservice.IModRouteService;
import com.shands.mod.main.service.hotelservice.IModServiceService;
import com.shands.mod.main.util.ThreadLocalHelper;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ModRouteServiceImpl implements IModRouteService {

  @Resource
  ModRouteMapper mapper;
  @Resource
  HotelServiceMapper hotelServiceMapper;
  @Resource
  IModServiceService modServiceService;

  @Override
  public int deleteByPrimaryKey(Integer id) {
    return mapper.deleteByPrimaryKey(id);
  }

  @Override
  public int insert(ModRoute record) {
    return mapper.insert(record);
  }

  @Override
  public int insertSelective(ModRoute record) {
    log.info("平台 添加常用理由  请求参数：" + record.toString());
    record.setUpdateTime(new Date());
    record.setUpdateUser(ThreadLocalHelper.getUser().getId());
    record.setCreateTime(new Date());
    record.setCreateUser(ThreadLocalHelper.getUser().getId());
    return mapper.insertSelective(record);
  }

  @Override
  public ModRoute selectByPrimaryKey(Integer id) {
    log.info("平台 常用理由获取详情  请求参数：id = " + id);
    return mapper.selectByPrimaryKey(id);
  }

  @Override
  public int updateByPrimaryKeySelective(ModRoute record) {
    log.info("平台 修改常用理由  请求参数：" + record.toString());
    record.setUpdateTime(new Date());
    record.setUpdateUser(ThreadLocalHelper.getUser().getId());
    return mapper.updateByPrimaryKeySelective(record);
  }

  @Override
  public int updateByPrimaryKey(ModRoute record) {
    return mapper.updateByPrimaryKey(record);
  }

  @Override
  public int updateStatus(ModRoute record) {
    log.info("平台 常用理由修改状态  请求参数：" + record.toString());
    record.setUpdateTime(new Date());
    record.setUpdateUser(ThreadLocalHelper.getUser().getId());
    return mapper.updateByPrimaryKeySelective(record);
  }

  @Override
  public List<ModRoute> query() {
    List<ModRoute> modRoutes = mapper.query();
    for (ModRoute m : modRoutes) {
      String serviceType = m.getServiceType();
      if (serviceType.length() > 0) {
        //数据库中存储的是code值，根据code值返回给前端服务名称
        List<String> list = Arrays.asList(serviceType.split(","));
        List<String> arr = new ArrayList<>();
        for (String s : list) {
          ModService modService = modServiceService.selectByPrimaryKey(Integer.valueOf(s));
          if (modService != null) {
            arr.add(modService.getServiceType());
          }
        }
        String name = StringUtils.join(arr, ",");
        m.setServiceType(name);
      }
    }
    return modRoutes;
  }

  @Override
  public List<ModRoute> reason(HotelService hotelService, ModRoute modRouteres) {
    log.info("请求参数 服务id" + hotelService.getId() + " 订单/工单 1订单 2工单：" + modRouteres.getUseOrder());
    Integer useOrder = modRouteres.getUseOrder();
    HotelService hotelService1 = hotelServiceMapper.selectByPrimaryKey(hotelService.getId());
    List<ModRoute> reason = new ArrayList<>();
    if (hotelService1 != null) {
      //根据code去服务配置表里面查找对应的id
      ModService findByCode = modServiceService.findByCode(hotelService1.getServiceType());
      int id = findByCode.getId();
      Map map = new HashMap();
      map.put("serviceType", id);
      map.put("useOrder", useOrder);
      reason = mapper.reason(map);
    } else {
      log.info("未找到服务");
    }
    return reason;
  }

  @Override
  public List<ModRoute> invoiceRoute() {
    return mapper.invoiceRoute();
  }
}
