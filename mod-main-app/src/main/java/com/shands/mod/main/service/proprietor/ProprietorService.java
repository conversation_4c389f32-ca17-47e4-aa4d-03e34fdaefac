package com.shands.mod.main.service.proprietor;

import com.shands.mod.dao.model.MobileAndAreaCode;
import com.shands.mod.dao.model.proprietor.VerifyProprietorMobilesRes;

import java.util.List;
import java.util.Set;

/**
 * 业主相关操作的服务
 */
public interface ProprietorService {

    /**
     * 校验给定的手机号是否属于业主
     *
     * @param mobileAndAreaCodeList 需要校验的手机号、区号列表
     * @return 手机号与业主状态的映射（true表示是业主，false表示不是业主）
     */
    List<VerifyProprietorMobilesRes> verifyProprietorMobiles(List<MobileAndAreaCode> mobileAndAreaCodeList);

    Set<String> getConfigProprietorMobiles();

    Set<String> getUcProprietorMobiles(List<Integer> proprietorPostIds);
}
