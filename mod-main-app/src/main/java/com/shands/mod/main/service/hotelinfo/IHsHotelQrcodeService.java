package com.shands.mod.main.service.hotelinfo;

import com.shands.mod.dao.model.hs.HsHotelQrcode;

public interface IHsHotelQrcodeService {

  int deleteByPrimaryKey(Integer id);

  int insert(HsHotelQrcode record);

  int insertSelective(HsHotelQrcode record);

  HsHotelQrcode selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(HsHotelQrcode record);

  int updateByPrimaryKey(HsHotelQrcode record);

  int deleteByCompany(Integer companyId);

  int replaceHotelCode(HsHotelQrcode record);

}
