package com.shands.mod.main.remote.cms;

import com.betterwood.base.common.model.Result;

import com.shands.mod.config.FeignConfigure;
import com.shands.mod.dao.model.proprietor.ProprietorCardRecycleQo;
import com.shands.mod.dao.model.proprietor.ProprietorCardSendQo;

import com.shands.mod.main.remote.cms.fallback.RemoteBdwCmsCateCardServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(
    contextId = "remoteBdwCmsCateCardService",
    value = "bdw-cms",
    fallbackFactory = RemoteBdwCmsCateCardServiceFallbackFactory.class,
    configuration = FeignConfigure.class
)
public interface RemoteBdwCmsCateCardService {

    @PostMapping({"/proprietor_card/send"})
    Result<String> sendCard(@RequestBody ProprietorCardSendQo var1);

    @PostMapping({"/proprietor_card/recycle"})
    Result<String> recycleCard(@RequestBody ProprietorCardRecycleQo var1);
}
