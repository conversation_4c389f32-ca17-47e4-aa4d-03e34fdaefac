package com.shands.mod.main.service.statistics.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.shands.mod.dao.mapper.board.ModNewDataBoardMapper;
import com.shands.mod.dao.mapper.datarevision.AdsAppTradeRevenueDMapper;
import com.delonix.bi.dao.mapper.AdsPowerDlEcoAppEmployeePerfDMapper;
import com.shands.mod.dao.mapper.hs.ModMessageLogMapper;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.datarevision.vo.ModuleMenuVo;
import com.shands.mod.dao.model.enums.HotelTypeEnum;
import com.shands.mod.dao.model.enums.UserRightsTypeEnum;
import com.shands.mod.dao.model.enums.board.DataBoardModuleEnum;
import com.shands.mod.dao.model.newDataBoard.vo.HomeDataDetailsVo;
import com.shands.mod.dao.model.statistics.dto.AdsAppTradeRevenueDDto;
import com.shands.mod.dao.model.res.AdsHotelPerformanceHDTO;
import com.shands.mod.dao.model.statistics.vo.BaseStatisticsDataVo;
import com.shands.mod.dao.model.statistics.vo.BetterwoodCardDataVo;
import com.shands.mod.dao.model.statistics.vo.BillNotifyVo;
import com.shands.mod.dao.model.v0701.pojo.ModMessageLog;
import com.shands.mod.dao.util.ThousandSeparatorUtil;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.main.config.BetterwoodConfig;
import com.shands.mod.main.enums.NewDataBoardUrlEnum;
import com.shands.mod.main.service.common.UserInfoCommonService;
import com.shands.mod.main.service.message.MessageStateEnum;
import com.shands.mod.main.service.message.MessageTypeEnum;
import com.shands.mod.main.service.statistics.HomeDataStatisticService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.service.ModUserCommonService;
import com.shands.mod.util.BaseConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 首页数据统计服务实现类
 * @Author: mazhiyong
 */
@Service
@Slf4j
public class HomeDataStatisticServiceImpl implements HomeDataStatisticService {

    @Autowired
    private ModMessageLogMapper modMessageLogMapper;

    @Autowired
    private UserInfoCommonService userInfoCommonService;

    @Autowired
    private ModNewDataBoardMapper modNewDataBoardMapper;

    @Autowired
    private ModUserCommonService modUserCommonService;

    @Autowired
    private AdsAppTradeRevenueDMapper adsAppTradeRevenueDMapper;

    @Autowired
    private AdsPowerDlEcoAppEmployeePerfDMapper adsPowerDlEcoAppEmployeePerfDMapper;

    @Autowired
    private ModHotelInfoDao modHotelInfoDao;

    @Autowired
    private BetterwoodConfig betterwoodConfig;

    private static final String MSG_TITLE = "结算账单";
    
    // 百达卡本人权限编码
    private static final String MENU_CODE_BETTERWOOD_CARD = "home_data_betterwood_card";
    // 百达卡本店权限编码
    private static final String MENU_CODE_BETTERWOOD_CARD_HOTEL = "home_data_betterwood_card_hotel";

    @Value("${els.h5url:https://m.kaiyuanhotels.com}")
    private String sxeH5Url;

    @Override
    public BillNotifyVo queryBillNotifyLast() {
        BillNotifyVo result = new BillNotifyVo();

        // 权限校验
        Integer userId = ThreadLocalHelper.getUser().getId();
        int hotelId = ThreadLocalHelper.getCompanyId();

        List<String> param = userInfoCommonService.getUserRights(userId, hotelId, UserRightsTypeEnum.APP);
        final String finalStatementCode = DataBoardModuleEnum.FINAL_STATEMENT.name().toLowerCase();
        if (CollectionUtils.isEmpty(param) || !param.contains(finalStatementCode)) {
            result.setShowFlag(false);
            log.error("用户权限不足,请联系管理员！");
            return result;
        }

        List<String> userRoles = modUserCommonService.getUserRoles(userId);
        List<ModuleMenuVo> dataBoards = modNewDataBoardMapper.findModuleMenuDetailList(HotelTypeEnum.HOTEL.name(), userRoles,userId);
        ModuleMenuVo vo = dataBoards.stream()
                .filter(v -> finalStatementCode.equals(v.getModuleCode()))
                .findFirst().orElse(null);
        if (Objects.isNull(vo)) {
            result.setShowFlag(false);
            log.error("用户权限不足,请联系管理员！");
            return result;
        }


       // 获取当前用户的最新未读账单消息
       ModMessageLog messageLog = new ModMessageLog();
       messageLog.setRecipientId(String.valueOf(ThreadLocalHelper.getUser().getId())); // 当前用户ID
       messageLog.setMessageType(MessageTypeEnum.MESSAGE_TYPE_APP.getTypeCode()); // APP消息类型
       messageLog.setSendState(MessageStateEnum.SEND_SUCCESS.getStateCode()); // 发送成功状态
       messageLog.setMessageTitle(MSG_TITLE);
       messageLog.setCompanyId(hotelId);

       // 查询未读消息列表
       List<ModMessageLog> messageLogList = modMessageLogMapper.queryMessageByStatu(messageLog);

       if (CollUtil.isEmpty(messageLogList) || Objects.isNull( messageLogList.get(0)) ||
               MessageStateEnum.READ_IS.getStateCode() == messageLogList.get(0).getReadState() ) {
           result.setShowFlag(false);
       } else {
           result.setShowFlag(true);
           result.setNotifyMsg(messageLogList.get(0).getMessageContent());
           result.setId(String.valueOf(messageLogList.get(0).getId()));
           result.setUrl(sxeH5Url + "/sxe/statement?screen=full");
       }

       return result;
    }

    @Override
    public BaseStatisticsDataVo getCurrentMonthAppConsumeData() {
        BaseStatisticsDataVo result = new BaseStatisticsDataVo();

        // 1. 获取当前用户id和酒店信息
        Integer userId = ThreadLocalHelper.getUser().getId();
        int hotelId = ThreadLocalHelper.getCompanyId();
        String hotelCode = modHotelInfoDao.queryById(hotelId).getHotelCode();

        // 2. 检查用户权限
        List<String> userRoles = modUserCommonService.getUserRoles(userId);
        List<ModuleMenuVo> moduleMenuVos = modNewDataBoardMapper.findModuleMenuDetailList(HotelTypeEnum.HOTEL.name(), userRoles, userId);

        // 查找APP消费会员数据模块
        final String appConsumeDataCode = DataBoardModuleEnum.APP_CONSUME_MEMBER_DATA.name();
        ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
                .filter(v -> appConsumeDataCode.equals(v.getModuleCode()))
                .findFirst().orElse(null);

        // 如果没有权限，设置为不显示
        if (bigModuleMenu == null) {
            log.error("用户权限不足, userId:{}", userId);
            throw new ServiceException("用户权限不足,请联系管理员！");
        }

        // 3. 计算t-1日自然月日期范围
        Date monthEnd;
        Date monthStart;
        Date yesterday;
        if (StrUtil.isNotBlank(betterwoodConfig.getQueryDate())) {
            Date queryDate = DateUtil.parse(betterwoodConfig.getQueryDate(), BaseConstants.FORMAT_DATE);
            yesterday = DateUtil.offsetDay(queryDate, -1);
            monthEnd = DateUtil.beginOfDay(yesterday);
            monthStart = DateUtil.beginOfMonth(yesterday);
        } else {
            yesterday = DateUtil.yesterday();
            monthEnd = DateUtil.beginOfDay(yesterday);
            monthStart = DateUtil.beginOfMonth(yesterday);
        }

        // 4. 查询昨日数据
        AdsAppTradeRevenueDDto memberData = adsAppTradeRevenueDMapper.selectRangeData(
                monthStart, monthEnd, hotelCode);

        // 5. 获取指标列表
        List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
                .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());

        // 6. 构建数据列表
        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(bigModuleMenu.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();
            // 赋值操作
            this.fillDataValue(memberData, detailsVo);
            dataList.add(detailsVo);
        }

        // 7. 设置返回结果
        result.setTitle(bigModuleMenu.getModuleName());
        result.setTitleDesc(bigModuleMenu.getDescName());
        result.setBizData(DateUtil.format(yesterday, DateTimeFormatter.ofPattern(BaseConstants.FORMAT_DATE)));
        result.setShowFlag(true);
        result.setDataList(dataList);

        return result;
    }

    /**
     * 填充数据值
     * @param data 数据源
     * @param detailsVo 详情VO
     */
    private void fillDataValue(AdsAppTradeRevenueDDto data, HomeDataDetailsVo detailsVo) {
        if (data == null) {
            detailsVo.setValue("-");
            return;
        }

        String code = detailsVo.getCode();
        Object value = data.getValueByFieldName(code);
        if (Objects.nonNull(value)) {
            BigDecimal bigDecimalValue = BigDecimal.ZERO;
            if (value instanceof BigDecimal) {
                bigDecimalValue = (BigDecimal) value;
            } else if (value instanceof Long) {
                bigDecimalValue = BigDecimal.valueOf((long) value);
            } else {
                // 对于其他类型，直接使用字符串值
                detailsVo.setValue(String.valueOf(value));
                return;
            }
            detailsVo.setValue(ThousandSeparatorUtil.format(bigDecimalValue));
        } else {
            detailsVo.setValue("-");
        }
    }

    @Override
    public BetterwoodCardDataVo getBetterwoodCardData() {
        // 1. 获取当前用户id和酒店信息
        Integer userId = ThreadLocalHelper.getUser().getId();
        Integer ucId = ThreadLocalHelper.getUser().getUcId();
        int hotelId = ThreadLocalHelper.getCompanyId();
        String hotelCode = modHotelInfoDao.queryById(hotelId).getHotelCode();

        // 2. 检查用户权限
        List<String> userRights = userInfoCommonService.getUserRights(userId, hotelId, UserRightsTypeEnum.APP);
        List<String> userRoles = modUserCommonService.getUserRoles(userId);
        List<ModuleMenuVo> moduleMenuVos = modNewDataBoardMapper.findModuleMenuDetailList(
                HotelTypeEnum.HOTEL.name(), userRoles, userId);

        // 查找百达卡指标模块
        final String betterwoodCardCode = DataBoardModuleEnum.BETTERWOOD_CARD_DATA.name();
        ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
                .filter(v -> betterwoodCardCode.equals(v.getModuleCode()))
                .findFirst().orElse(null);

        if (bigModuleMenu == null) {
            log.error("用户权限不足, userId:{}", userId);
            throw new ServiceException("用户权限不足,请联系管理员！");
        }

        // 3. 计算t-1日自然月日期范围
        Date monthEnd;
        Date monthStart;
        Date yesterday;
        if (StrUtil.isNotBlank(betterwoodConfig.getQueryDate())) {
            Date queryDate = DateUtil.parse(betterwoodConfig.getQueryDate(), BaseConstants.FORMAT_DATE);
            yesterday = DateUtil.offsetDay(queryDate, -1);
            monthEnd = DateUtil.beginOfDay(yesterday);
            monthStart = DateUtil.beginOfMonth(yesterday);
        } else {
            yesterday = DateUtil.yesterday();
            monthEnd = DateUtil.beginOfDay(yesterday);
            monthStart = DateUtil.beginOfMonth(yesterday);
        }

        // 4. 构建返回结果
        BetterwoodCardDataVo result = new BetterwoodCardDataVo();

        // 5. 检查本人权限并获取数据
        if (!CollectionUtils.isEmpty(userRights) && userRights.contains(MENU_CODE_BETTERWOOD_CARD)) {
            AdsHotelPerformanceHDTO personalData = adsPowerDlEcoAppEmployeePerfDMapper
                    .sumPerformanceByHotelAndUser(hotelCode, String.valueOf(ucId), monthStart, monthEnd);
            result.setPersonal(buildStatisticsData(personalData, bigModuleMenu, moduleMenuVos, false));
        }

        // 6. 检查本店权限并获取数据
        if (!CollectionUtils.isEmpty(userRights) && userRights.contains(MENU_CODE_BETTERWOOD_CARD_HOTEL)) {
            AdsHotelPerformanceHDTO hotelData = adsPowerDlEcoAppEmployeePerfDMapper
                    .sumPerformanceByHotel(hotelCode, monthStart, monthEnd);
            result.setHotel(buildStatisticsData(hotelData, bigModuleMenu, moduleMenuVos, true));
        }

        return result;
    }

    /**
     * 构建统计数据
     * @param data 原始数据
     * @param bigModuleMenu 大模块菜单
     * @param moduleMenuVos 所有模块菜单
     * @return 统计数据
     */
    private BaseStatisticsDataVo buildStatisticsData(AdsHotelPerformanceHDTO data,
                                                     ModuleMenuVo bigModuleMenu,
                                                     List<ModuleMenuVo> moduleMenuVos,
                                                     boolean isHotel) {
        BaseStatisticsDataVo result = new BaseStatisticsDataVo();
        
        // 获取指标列表
        List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
                .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());

        // 构建数据列表
        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(bigModuleMenu.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .url(StrUtil.isNotBlank(NewDataBoardUrlEnum.getUrl(indicator.getModuleCode(), isHotel)) ?
                            sxeH5Url + NewDataBoardUrlEnum.getUrl(indicator.getModuleCode(), isHotel) : "")
                    .build();
            // 赋值操作
            this.fillBetterwoodCardDataValue(data, detailsVo);
            dataList.add(detailsVo);
        }

        // 设置返回结果
        result.setTitle(bigModuleMenu.getModuleName());
        result.setTitleDesc(bigModuleMenu.getDescName());
        result.setShowFlag(true);
        result.setDataList(dataList);

        return result;
    }

    /**
     * 填充百达卡数据值
     * @param data 数据源
     * @param detailsVo 详情VO
     */
    private void fillBetterwoodCardDataValue(AdsHotelPerformanceHDTO data, HomeDataDetailsVo detailsVo) {
        if (data == null) {
            detailsVo.setValue("-");
            return;
        }

        String code = detailsVo.getCode();
        Object value = data.getValueByFieldName(code);
        boolean isMoney = false;
        if (Objects.nonNull(value)) {
            BigDecimal bigDecimalValue = BigDecimal.ZERO;
            if (value instanceof BigDecimal) {
                bigDecimalValue = (BigDecimal) value;
                isMoney = true;
            } else if (value instanceof Long) {
                bigDecimalValue = BigDecimal.valueOf((Long) value);
            } else if (value instanceof Double) {
                bigDecimalValue = BigDecimal.valueOf((Double) value);
                isMoney = true;
            } else {
                // 对于其他类型，直接使用字符串值
                detailsVo.setValue(String.valueOf(value));
                return;
            }
            detailsVo.setValue(isMoney ? "¥" + ThousandSeparatorUtil.format(bigDecimalValue) : ThousandSeparatorUtil.format(bigDecimalValue));
        } else {
            detailsVo.setValue("-");
        }
    }
}
