package com.shands.mod.main.service.hotelservice;

import com.shands.mod.dao.model.Dict;
import com.shands.mod.dao.model.hs.HotelService;
import com.shands.mod.dao.model.hs.HotelServiceProperty;
import com.shands.mod.dao.model.req.hs.goods.ListReq;
import com.shands.mod.dao.model.res.hs.HotelServicePropertyRes;
import com.shands.mod.dao.model.res.hs.good.GoodsRes;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/24
 * @desc 服务属性配置管理service
*/
public interface IHotelServicePropertyService {
  int deleteByPrimaryKey(Integer id);

  int insert(HotelServiceProperty record);

  int insertSelective(HotelServiceProperty record);

  HotelServiceProperty selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(HotelServiceProperty record);

  int updateByPrimaryKey(HotelServiceProperty record);

  // 在添加service时，需要在字典表里面找到相对应的属性，然后添加到属性表中，每个酒店都有相对应的商品服务类型和商品属性
  int insertByHotelService(Dict dict, HotelService hotelService);

  List<HotelServicePropertyRes> getByComId();

  Integer getId(Integer hotelServiceId, String property);

  List<GoodsRes> listByServiceId(ListReq queryReq);
}
