package com.shands.mod.main.service.hotelservice;

import com.shands.mod.dao.model.hs.HsHotelServiceRule;
import com.github.pagehelper.PageInfo;
import com.shands.mod.dao.model.req.hs.hotel.HsHotelServiceRuleReq;
import com.shands.mod.exception.NormalException;
import java.util.Map;

public interface IHotelServiceRuleService {

  int deleteByPrimaryKey(Integer id);

  int insert(HsHotelServiceRule record);

  int insertSelective(HsHotelServiceRule record)throws NormalException;

  HsHotelServiceRule selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(HsHotelServiceRuleReq record)throws NormalException;

  int updateByPrimaryKey(HsHotelServiceRule record);

  int delete(Integer id);

  int updateStatus(Map<String, Integer> map);

  PageInfo ruleList();

}
