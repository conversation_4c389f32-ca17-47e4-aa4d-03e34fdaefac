package com.shands.mod.main.service.sales.tool.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.betterwood.base.common.model.Result;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.shands.mod.dao.mapper.sales.tool.ModProxyOrderMapperExt;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.enums.BreakfastEnum;
import com.shands.mod.dao.model.enums.MainOrderPayStatusEnum;
import com.shands.mod.dao.model.enums.MainOrderStatusEnum;
import com.shands.mod.dao.model.sales.tool.domain.ModProxyOrder;
import com.shands.mod.dao.model.sales.tool.domain.ModProxyOrderExample;
import com.shands.mod.dao.model.sales.tool.dto.BetterwoodOrderStatusMsg;
import com.shands.mod.dao.model.sales.tool.dto.VerifyInCompanyDto;
import com.shands.mod.dao.model.sales.tool.req.CancelProxyOrderReq;
import com.shands.mod.dao.model.sales.tool.req.CreateOrderReq;
import com.shands.mod.dao.model.sales.tool.req.GuestInfoReq;
import com.shands.mod.dao.model.sales.tool.req.ProxyOrderNotifyReq;
import com.shands.mod.dao.model.sales.tool.req.QueryPriceListReq;
import com.shands.mod.dao.model.sales.tool.req.QueryProxyOrderDetailReq;
import com.shands.mod.dao.model.sales.tool.req.QueryProxyOrderListReq;
import com.shands.mod.dao.model.sales.tool.req.VerifyInCompanyReq;
import com.shands.mod.dao.model.sales.tool.res.CreateOrderRes;
import com.shands.mod.dao.model.sales.tool.res.HotelPriceListRes;
import com.shands.mod.dao.model.sales.tool.res.OrderInfoRes;
import com.shands.mod.dao.model.sales.tool.res.OrderListRes;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.model.syncuc.ModUser;
import com.shands.mod.dao.model.v0701.dto.GuestDto;
import com.shands.mod.dao.model.v0701.dto.OrderMainAddDto;
import com.shands.mod.dao.model.v0701.dto.OrderMainAddRspDto;
import com.shands.mod.dao.model.v0701.dto.OrderMainDTO;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.main.config.BetterwoodConfig;
import com.shands.mod.main.config.CompanyMemberConfig;
import com.shands.mod.main.config.ProxyOrderConfig;
import com.shands.mod.main.enums.PayMethodEnum;
import com.shands.mod.main.remote.company.CompanyFeignService;
import com.shands.mod.main.remote.hotel.RemoteBdwHotelService;
import com.shands.mod.main.remote.member.CompanyMemberFeignService;
import com.shands.mod.main.remote.sell.SellRpcService;
import com.shands.mod.main.service.betterwood.rpc.BdxRpcService;
import com.shands.mod.main.service.betterwood.rpc.req.CategoryReq;
import com.shands.mod.main.service.betterwood.rpc.req.HotelDetailPriceReq;
import com.shands.mod.main.service.betterwood.rpc.resp.CmsInterestOffersItem;
import com.shands.mod.main.service.betterwood.rpc.resp.CompanyContractVO;
import com.shands.mod.main.service.betterwood.rpc.resp.CompanyInfoVO;
import com.shands.mod.main.service.betterwood.rpc.resp.CompanyProtocol;
import com.shands.mod.main.service.betterwood.rpc.resp.RateAvailabilityResponse;
import com.shands.mod.main.service.betterwood.rpc.resp.RateDetailResponse;
import com.shands.mod.main.service.sales.tool.IModProxyOrderService;
import com.shands.mod.main.service.syncuc.IModUserService;
import com.shands.mod.main.util.DateUtils;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.BaseThreadLocalHelper;
import com.shands.mod.util.BeanUtils;
import com.shands.mod.util.TimeUtils;
import com.shands.mod.vo.UserInfoVO;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * @Description: 销售员下单实现类
 * @Author: wj
 * @Date: 2024/8/14 14:40
 */
@Service
@Slf4j
public class ModProxyOrderServiceImpl implements IModProxyOrderService {

  @Autowired
  private ModProxyOrderMapperExt modProxyOrderMapperExt;

  @Autowired
  private IModUserService modUserService;
  @Resource
  private ModHotelInfoDao modHotelInfoDao;

  @Autowired
  private RemoteBdwHotelService remoteBdwHotelService;
  @Autowired
  private SellRpcService sellRpcService;
  @Autowired
  private BdxRpcService bdxRpcService;
  @Autowired
  private BetterwoodConfig betterwoodConfig;
  @Autowired
  private ProxyOrderConfig proxyOrderConfig;

  @Autowired
  private CompanyMemberConfig companyMemberConfig;

  @Autowired
  private CompanyMemberFeignService companyMemberFeignService;
  
  @Autowired
  private CompanyFeignService companyFeignService;


  private static final String DEFAULT_PROTOCOL_NO = "CM000001";

  private static final Integer DEFAULT_CHANNEL_TYPE = 6;

  private static final String DEFAULT_REMARK = "【下单员工：${user}】";


  @Override
  public PageInfo<OrderListRes> queryOrderList(QueryProxyOrderListReq req) {
    if (null == BaseThreadLocalHelper.getUser()) {
      return new PageInfo<>(Lists.newArrayList());
    }
    PageHelper.startPage(req.getPageNo(), req.getPageSize());
    List<OrderListRes> list = modProxyOrderMapperExt.queryList(
        BaseThreadLocalHelper.getUser().getUcId(), req.getStatus(), req.getParam());
    return new PageInfo<>(list);
  }

  @Override
  public OrderInfoRes queryOrderDetail(QueryProxyOrderDetailReq req) throws Exception {
    String orderMainNo = req.getOrderMainNo();
    ModProxyOrder proxyOrder = this.getModProxyOrder(orderMainNo);
    if (proxyOrder == null) {
      return null;
    }
    OrderMainDTO mainDTO = remoteBdwHotelService.queryMainOrderInfo(orderMainNo,
        BaseConstants.CHANNEL_TYPE_DELONG);
    if (mainDTO == null) {
      log.warn("main order info not found with orderMainNo: {}", orderMainNo);
      return null;
    }
    OrderInfoRes orderInfoRes = new OrderInfoRes();
    BeanUtils.copyProperties(orderInfoRes, proxyOrder);
    orderInfoRes.setBreakfastNum(proxyOrder.getBreakfastNum());
    orderInfoRes.setBkName(BreakfastEnum.getEnum(proxyOrder.getBreakfastNum()).getDescription());
    orderInfoRes.setNights(DateUtils.dayDiff(proxyOrder.getArrDate(), proxyOrder.getDepDate()));
    orderInfoRes.setArrDate(proxyOrder.getArrDate().getTime());
    orderInfoRes.setDepDate(proxyOrder.getDepDate().getTime());
    orderInfoRes.setCreateTime(proxyOrder.getCreateTime().getTime());

    Integer status = mainDTO.getStatus();
    orderInfoRes.setStatus(status);
    orderInfoRes.setStatusLabel(
        MainOrderStatusEnum.getEnum(status).getProxyOrderStatus().getDescription());
    orderInfoRes.setPayStatus(mainDTO.getPayStatus());
    orderInfoRes.setPayStatusLabel(
        MainOrderPayStatusEnum.getEnum(mainDTO.getPayStatus()).getDescription());
    orderInfoRes.setBookerPhoneAreaCode(mainDTO.getBookerPhoneAreaCode());
    orderInfoRes.setPaymentTypeLabel(PayMethodEnum.getPayMethodEnum(proxyOrder.getPaymentType()).getDesc());
    orderInfoRes.setCancelable(
        !Objects.isNull(mainDTO.getShowCancel()) && mainDTO.getShowCancel());

    if (Objects.nonNull(proxyOrder.getUcId())) {
      ModUser modUser = modUserService.queryByUcId(proxyOrder.getUcId());
      if (Objects.nonNull(modUser)) {
        orderInfoRes.setSalesName(modUser.getName());
      }
    }
    return orderInfoRes;
  }

  public @Nullable ModProxyOrder getModProxyOrder(String orderMainNo) {
    ModProxyOrderExample example = new ModProxyOrderExample();
    example.createCriteria().andOrderMainNoEqualTo(orderMainNo);
    List<ModProxyOrder> orders = modProxyOrderMapperExt.selectByExample(example);
    if (orders.isEmpty()) {
      log.warn("Order not found with orderMainNo: {}", orderMainNo);
      return null;
    }
    return orders.get(0);
  }

  @Override
  public Boolean confirmNotify(ProxyOrderNotifyReq req) {
    ModProxyOrder proxyOrder = this.getModProxyOrder(req.getOrderMainNo());
    if (proxyOrder == null) {
      return false;
    }
    proxyOrder.setConfirmState(1);
    proxyOrder.setUpdateTime(new Date());
    return modProxyOrderMapperExt.updateByPrimaryKey(proxyOrder) > 0;
  }

  @Override
  public void handleOrderStatusMsg(String message) {
    if (StringUtils.isEmpty(message)) {
      log.warn("Betterwood order status msg is empty");
      return;
    }
    BetterwoodOrderStatusMsg msg = JSON.parseObject(message,
        BetterwoodOrderStatusMsg.class);
    if (!BaseConstants.CHANNEL_TYPE_DELONG.equals(msg.getChannelType())) {
      log.warn("Betterwood order channel type is wrong");
      return;
    }
    ModProxyOrder proxyOrder = this.getModProxyOrder(msg.getOrderMainNo());
    if (proxyOrder == null) {
      return;
    }
    if (Objects.nonNull(msg.getPaymentType())){
      proxyOrder.setPaymentType(msg.getPaymentType());
    }
    proxyOrder.setStatus(msg.getStatus());
    proxyOrder.setUpdateTime(new Date());
    modProxyOrderMapperExt.updateByPrimaryKey(proxyOrder);
  }


  @Override
  public List<HotelPriceListRes> getPriceList(QueryPriceListReq req) {
    if (companyMemberConfig.getRefactorCompanySwitch()) {
      return getPriceListV2(req);
    }


    long companyId = Long.parseLong(req.getCompanyId());
    CompanyInfoVO companyInfoVO = bdxRpcService.findByCompanyId(companyId);
    if (null == companyInfoVO) {
      throw new RuntimeException("获取企业信息失败");
    }

    //获取企业协议
    String protocolNo = StringUtils.isBlank(req.getProtocolNo()) ? getProtocolNo(companyInfoVO, req.getHotelCode()) : req.getProtocolNo();
    if(proxyOrderConfig.getCompanyProtocolSwitch() && DEFAULT_PROTOCOL_NO.equals(protocolNo)) {
      return new ArrayList<>();
    }

    //调用ava获取价格
    HotelDetailPriceReq hotelDetailPriceReq = new HotelDetailPriceReq();
    hotelDetailPriceReq.setArrDate(req.getArrDate());
    hotelDetailPriceReq.setDepDate(req.getDepDate());
    hotelDetailPriceReq.setHotelCode(req.getHotelCode());
    hotelDetailPriceReq.setHourRoomFlag(0);

    List<CategoryReq> categoryReqList = new ArrayList<>();
    CategoryReq categoryReq = new CategoryReq();
    categoryReq.setCategorySub("COMPANY");
    categoryReq.setCustAccountList(Collections.singletonList(protocolNo));
    categoryReq.setGuesttypeCode("PROTOCOL");
    categoryReq.setNeedQueryRights(false);
    categoryReq.setPriceType(DEFAULT_PROTOCOL_NO.equals(protocolNo) ? 3 : 4);
    categoryReqList.add(categoryReq);
    hotelDetailPriceReq.setCategoryReqList(categoryReqList);

    hotelDetailPriceReq.setRoomNum(1);
    hotelDetailPriceReq.setNightNum((int) TimeUtils.getDays(req.getDepDate(), req.getArrDate()));
    hotelDetailPriceReq.setQueryActivity(0);
    hotelDetailPriceReq.setCouponChannelType(DEFAULT_CHANNEL_TYPE);

    List<Long> cardIdList = Optional.ofNullable(companyInfoVO.getCardId())
        .map(id -> Lists.newArrayList(id.longValue()))
        .orElse(Lists.newArrayList());
    hotelDetailPriceReq.setCardIds(cardIdList);

    RateAvailabilityResponse rateAvailabilityResponse = sellRpcService.hotelDetailPrice(
        hotelDetailPriceReq);
    if (null == rateAvailabilityResponse || CollUtil.isEmpty(rateAvailabilityResponse.getRateDetailList())) {
      return new ArrayList<>();
    }
    List<RateDetailResponse> rateDetailList = rateAvailabilityResponse.getRateDetailList();
    //非企业协议过滤企业会员价
    if(!DEFAULT_PROTOCOL_NO.equals(protocolNo)) {
      // 门店协议
      rateDetailList = rateDetailList.stream().filter(rateDetailResponse -> "COMPANY".equals(rateDetailResponse.getProdCategorySub())).collect(Collectors.toList());
    } else if(this.isNewQueryPriceInterface()){
      // 企业身份
      rateDetailList = rateDetailList.stream().filter(rateDetailResponse -> BooleanUtils.isTrue(rateDetailResponse.getEnterpriseCard())).collect(Collectors.toList());
    }
    List<HotelPriceListRes> hotelPriceListResList = rateDetailList.stream().map(rateDetailResponse -> {
      HotelPriceListRes hotelPriceListRes = new HotelPriceListRes();
      hotelPriceListRes.setAvailableRooms(rateDetailResponse.getAvaliableRooms());
      hotelPriceListRes.setAvgPrice((long) rateDetailResponse.getPriceOffersInfoList().get(0).getAvgPrice());
      hotelPriceListRes.setTotalPrice((long) rateDetailResponse.getPriceOffersInfoList().get(0).getTotalPrice());
      // 企业身份卡价格
      if (BooleanUtils.isTrue(rateDetailResponse.getEnterpriseCard())) {
        rateDetailResponse.getPriceOffersInfoList().forEach(info -> {
          if (Objects.nonNull(info.getInterestOffersItem()) && info.getInterestOffersItem().getCredentialType() == 3) {
            hotelPriceListRes.setAvgPrice((long) info.getAvgPrice());
            hotelPriceListRes.setTotalPrice((long) info.getTotalPrice());
          }
        });
      }
      hotelPriceListRes.setRoomTypeName(rateDetailResponse.getRoomTypeName());
      hotelPriceListRes.setBkName(rateDetailResponse.getPackages().get(0).getName());
      PayMethodEnum payMethodEnum = PayMethodEnum.getPayMethodEnumByPayMethod(rateDetailResponse.getPayMethod());
      hotelPriceListRes.setPaymentType(payMethodEnum.getPaymentType());
      hotelPriceListRes.setPaymentTypeDesc(payMethodEnum.getDescription());
      hotelPriceListRes.setPaymentTypeDescCs(PayMethodEnum.CS.getDescription());
      hotelPriceListRes.setPaymentTypeDescPp(PayMethodEnum.PP.getDescription());
      hotelPriceListRes.setProductCode(rateDetailResponse.getProductCode());
      return hotelPriceListRes;
    }).collect(Collectors.toList());

    hotelPriceListResList = sortHotelPriceList(hotelPriceListResList);
    return hotelPriceListResList;
  }


  public List<HotelPriceListRes> getPriceListV2(QueryPriceListReq req) {
    long companyId = Long.parseLong(req.getCompanyId());
    CompanyInfoVO companyInfoVO = bdxRpcService.findByCompanyId(companyId);
    if (null == companyInfoVO) {
      throw new RuntimeException("获取企业信息失败");
    }

    // 检查合同信息
    Result<CompanyContractVO> companyContractVOResult = companyMemberFeignService.queryHotelContract(companyId, req.getHotelCode());

    if (companyContractVOResult == null || !companyContractVOResult.isOk()) {
      throw new RuntimeException("获取企业合同失败");
    }

    CompanyContractVO companyContractVO = companyContractVOResult.getData();
    if (companyContractVO == null || companyContractVO.getStatus() == 0) {
      throw new RuntimeException("合同未启用，请去德胧PMS操作启用。");
    }

    if (companyContractVO.getContractEndTime() == null || companyContractVO.getContractEndTime().isBefore(LocalDateTime.now())) {
      throw new RuntimeException("合同已过期，请去德胧PMS操作有效期。");
    }

    //调用ava获取价格
    HotelDetailPriceReq hotelDetailPriceReq = new HotelDetailPriceReq();
    hotelDetailPriceReq.setArrDate(req.getArrDate());
    hotelDetailPriceReq.setDepDate(req.getDepDate());
    hotelDetailPriceReq.setHotelCode(req.getHotelCode());
    hotelDetailPriceReq.setHourRoomFlag(0);

    List<CategoryReq> categoryReqList = new ArrayList<>();
    CategoryReq categoryReq = new CategoryReq();
    categoryReq.setCategorySub("COMPANY");
    categoryReq.setCustAccountList(Collections.singletonList(String.valueOf(companyId)));
    categoryReq.setGuesttypeCode("PROTOCOL");
    categoryReq.setNeedQueryRights(false);
    categoryReq.setPriceType(4);
    categoryReqList.add(categoryReq);
    hotelDetailPriceReq.setCategoryReqList(categoryReqList);

    hotelDetailPriceReq.setRoomNum(1);
    hotelDetailPriceReq.setNightNum((int) TimeUtils.getDays(req.getDepDate(), req.getArrDate()));
    hotelDetailPriceReq.setQueryActivity(0);
    hotelDetailPriceReq.setCouponChannelType(DEFAULT_CHANNEL_TYPE);

    List<Long> cardIdList = Optional.ofNullable(companyInfoVO.getCardId())
        .map(id -> Lists.newArrayList(id.longValue()))
        .orElse(Lists.newArrayList());
    hotelDetailPriceReq.setCardIds(cardIdList);

    RateAvailabilityResponse rateAvailabilityResponse = sellRpcService.hotelDetailPrice(
        hotelDetailPriceReq);
    if (null == rateAvailabilityResponse || CollUtil.isEmpty(rateAvailabilityResponse.getRateDetailList())) {
      throw new RuntimeException("房价码未配置价格，请去德胧PMS检查房价码配置。");
    }
    List<RateDetailResponse> rateDetailList = rateAvailabilityResponse.getRateDetailList();
    // 过滤企业会员价
    rateDetailList = rateDetailList.stream().filter(rateDetailResponse -> "COMPANY".equals(rateDetailResponse.getProdCategorySub())).collect(Collectors.toList());

    if (CollUtil.isEmpty(rateDetailList)) {
      throw new RuntimeException("房价码未配置价格，请去德胧PMS检查房价码配置。");
    }

    List<HotelPriceListRes> hotelPriceListResList = rateDetailList.stream().map(rateDetailResponse -> {
      HotelPriceListRes hotelPriceListRes = new HotelPriceListRes();
      hotelPriceListRes.setAvailableRooms(rateDetailResponse.getAvaliableRooms());
      hotelPriceListRes.setAvgPrice((long) rateDetailResponse.getPriceOffersInfoList().get(0).getAvgPrice());
      hotelPriceListRes.setTotalPrice((long) rateDetailResponse.getPriceOffersInfoList().get(0).getTotalPrice());
      // 企业身份卡价格
      if (BooleanUtils.isTrue(rateDetailResponse.getEnterpriseCard())) {
        rateDetailResponse.getPriceOffersInfoList().forEach(info -> {
          if (Objects.nonNull(info.getInterestOffersItem()) && info.getInterestOffersItem().getCredentialType() == 3) {
            hotelPriceListRes.setAvgPrice((long) info.getAvgPrice());
            hotelPriceListRes.setTotalPrice((long) info.getTotalPrice());
          }
        });
      }
      hotelPriceListRes.setRoomTypeName(rateDetailResponse.getRoomTypeName());
      hotelPriceListRes.setBkName(rateDetailResponse.getPackages().get(0).getName());
      PayMethodEnum payMethodEnum = PayMethodEnum.getPayMethodEnumByPayMethod(rateDetailResponse.getPayMethod());
      hotelPriceListRes.setPaymentType(payMethodEnum.getPaymentType());
      hotelPriceListRes.setPaymentTypeDesc(payMethodEnum.getDescription());
      hotelPriceListRes.setPaymentTypeDescCs(PayMethodEnum.CS.getDescription());
      hotelPriceListRes.setPaymentTypeDescPp(PayMethodEnum.PP.getDescription());
      hotelPriceListRes.setProductCode(rateDetailResponse.getProductCode());
      return hotelPriceListRes;
    }).collect(Collectors.toList());

    hotelPriceListResList = sortHotelPriceList(hotelPriceListResList);
    return hotelPriceListResList;
  }

  @Override
  public CreateOrderRes createOrder(CreateOrderReq req) {

    handleCreateOrderReq(req);

    UserInfoVO userInfoVO = BaseThreadLocalHelper.getUser();
    if (null == userInfoVO) {
      throw new RuntimeException("请重新登录");
    }

    PayMethodEnum payMethodEnum = PayMethodEnum.getPayMethodEnum(req.getPaymentType());
    if(!payMethodEnum.equals(PayMethodEnum.PP) && !payMethodEnum.equals(PayMethodEnum.CS)) {
      throw new RuntimeException("参数错误");
    }

    //根据hotelCode查询hotelName
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryHotelInfoByCode(req.getHotelCode());
    if(null == modHotelInfo) {
      throw new RuntimeException("获取酒店信息失败");
    }

    long companyId = Long.parseLong(req.getCompanyId());
    CompanyInfoVO companyInfoVO = bdxRpcService.findByCompanyId(companyId);
    if (null == companyInfoVO) {
      throw new RuntimeException("获取企业信息失败");
    }

    //获取企业协议
    String protocolNo = getProtocolNo(companyInfoVO, req.getHotelCode());

    //调用ava获取价格
    HotelDetailPriceReq hotelDetailPriceReq = new HotelDetailPriceReq();
    hotelDetailPriceReq.setArrDate(req.getArrDate());
    hotelDetailPriceReq.setDepDate(req.getDepDate());
    hotelDetailPriceReq.setHotelCode(req.getHotelCode());
    hotelDetailPriceReq.setHourRoomFlag(0);

    List<CategoryReq> categoryReqList = new ArrayList<>();
    CategoryReq categoryReq = new CategoryReq();
    categoryReq.setCategorySub("COMPANY");
    if (companyMemberConfig.getRefactorCompanySwitch()) {
      categoryReq.setCustAccountList(Collections.singletonList(String.valueOf(companyId)));
      categoryReq.setPriceType(4);
    } else {
      categoryReq.setCustAccountList(Collections.singletonList(protocolNo));
      categoryReq.setPriceType(DEFAULT_PROTOCOL_NO.equals(protocolNo) ? 3 : 4);
    }

    categoryReq.setGuesttypeCode("PROTOCOL");
    categoryReq.setNeedQueryRights(false);
    categoryReqList.add(categoryReq);
    hotelDetailPriceReq.setCategoryReqList(categoryReqList);
    hotelDetailPriceReq.setProductCode(req.getProductCode());

    hotelDetailPriceReq.setRoomNum(1);
    hotelDetailPriceReq.setNightNum((int) TimeUtils.getDays(req.getDepDate(), req.getArrDate()));
    hotelDetailPriceReq.setQueryActivity(0);
    hotelDetailPriceReq.setCouponChannelType(DEFAULT_CHANNEL_TYPE);

    List<Long> cardIdList = Optional.ofNullable(companyInfoVO.getCardId())
        .map(id -> Lists.newArrayList(id.longValue()))
        .orElse(Lists.newArrayList());
    hotelDetailPriceReq.setCardIds(cardIdList);

    RateAvailabilityResponse rateAvailabilityResponse = sellRpcService.hotelDetailPrice(
        hotelDetailPriceReq);
    if (null == rateAvailabilityResponse) {
      throw new RuntimeException("获取价格失败");
    }
    RateDetailResponse rateDetailResponse = rateAvailabilityResponse.getRateDetailList().stream().filter(rateDetailResponse1 -> req.getProductCode().equals(rateDetailResponse1.getProductCode())).findFirst().orElse(null);
    if (null == rateDetailResponse) {
      throw new RuntimeException("获取价格失败");
    }
    //查询销售id
   /* SalesmanQueryReq salesmanQueryReq = new SalesmanQueryReq();
    salesmanQueryReq.setUcId(BaseThreadLocalHelper.getUser().getUcId().toString());
    SalesmanInfoVO salesmanInfoVO = bdxRpcService.salesmanQuery(salesmanQueryReq);
    if (null == salesmanInfoVO) {
      throw new RuntimeException("获取销售ID失败");
    }*/
    OrderMainAddRspDto orderMainAddRspDto = createOrderByEmployee(req, rateDetailResponse,
        userInfoVO);
    if (null != orderMainAddRspDto) {
      //插入记录表
      ModProxyOrder modProxyOrder = ModProxyOrder.builder()
          .ucId(userInfoVO.getUcId())
          //.salesId(salesmanInfoVO.getSalesId())
          .orderMainNo(orderMainAddRspDto.getOrderMainId())
          .status(0 == req.getPaymentType() ? 0 : 1)
          .confirmState(0)
          .paymentType(req.getPaymentType())
          .totalPrice(req.getOrderAmount())
          .companyId(req.getCompanyId())
          .companyName(req.getCompanyName())
          .activeType(DEFAULT_PROTOCOL_NO.equals(protocolNo) ? 3 : 4)
          .protocolNo(protocolNo)
          .hotelCode(req.getHotelCode())
          .hotelName(modHotelInfo.getHotelName())
          .roomTypeName(rateDetailResponse.getRoomTypeName())
          .arrDate(new Date(req.getArrDate()))
          .depDate(new Date(req.getDepDate()))
          .roomNum(req.getRoomNum())
          .breakfastNum(
              BreakfastEnum.getEnumByCode(rateDetailResponse.getPackages().get(0).getCode())
                  .getNum())
          .bookerName(req.getBookerName())
          .bookerPhone(req.getBookerPhone())
          .guestNames(req.getGuestList().stream().map(GuestInfoReq::getGuestName)
              .collect(Collectors.joining(",")))
          .remark(req.getRemark())
          .createTime(new Date())
          .updateTime(new Date())
          .createBy((long) userInfoVO.getId())
          .updateBy((long) userInfoVO.getId())
          .build();
      if (companyMemberConfig.getRefactorCompanySwitch()) {
        modProxyOrder.setActiveType(4);
        modProxyOrder.setProtocolNo(String.valueOf(companyId));
      }
      // 外订间夜数记录
      modProxyOrder.setOutOrderRoomNight((int) getOutOrderRoomNight(req));

      modProxyOrderMapperExt.insertSelective(modProxyOrder);

      CreateOrderRes createOrderRes = new CreateOrderRes();
      createOrderRes.setOrderMainNo(orderMainAddRspDto.getOrderMainId());
      createOrderRes.setProxyOrderId(modProxyOrder.getId());
      return createOrderRes;
    }
    throw new RuntimeException("下单失败");
  }

  private void handleCreateOrderReq(CreateOrderReq req) {
    // 兼容老版本，默认发短信
    if (req.getSendSMS() == null) {
      req.setSendSMS(true);
    }
  }

  /**
   * 获取外订间夜数
   */
  private long getOutOrderRoomNight(CreateOrderReq req) {
    // 外订间夜数记录
    VerifyInCompanyReq verifyInCompanyReq = new VerifyInCompanyReq();
    verifyInCompanyReq.setCompanyId(Long.parseLong(req.getCompanyId()));

    List<VerifyInCompanyDto> companyMemberList = new ArrayList<>();
    req.getGuestList().forEach(guest -> {
      VerifyInCompanyDto verifyInCompanyDto = new VerifyInCompanyDto();
      verifyInCompanyDto.setMobile(guest.getGuestPhone());
      verifyInCompanyDto.setAreaCode(guest.getGuestPhoneAreaCode());
      companyMemberList.add(verifyInCompanyDto);
    });
    verifyInCompanyReq.setCompanyMemberList(companyMemberList);

    Result<List<VerifyInCompanyDto>> verifyInCompanyRes = companyFeignService.verifyInCompany(verifyInCompanyReq);
    if (verifyInCompanyRes != null && verifyInCompanyRes.isOk()) {
      long companyMemberNum = verifyInCompanyRes.getData().stream()
          .filter(verifyInCompanyDto -> !verifyInCompanyDto.getInCompany())
          .count();
      return companyMemberNum * ((req.getDepDate() - req.getArrDate()) /  86400000);
    }
    return 0L;
  }


  @Override
  public Boolean cancel(CancelProxyOrderReq req) {
    // 1. 通过req获取单号
    String orderMainNo = req.getOrderMainNo();

    // 2. 查询单号对应在数据库里的订单是否能取消
    ModProxyOrder proxyOrder = this.getModProxyOrder(orderMainNo);
    if (proxyOrder == null) {
      throw new ServiceException("订单不存在");
    }

    List<Integer> validStatuses = Arrays.asList(
        MainOrderStatusEnum.BOOKED.getStatus(),
        MainOrderStatusEnum.PENDING_PAYMENT.getStatus()
    );

    if (!validStatuses.contains(proxyOrder.getStatus())) {
      log.error("订单无法取消，订单号 {} 状态 {}", orderMainNo, proxyOrder.getStatus());
      throw new ServiceException("当前订单不能取消");
    }

    // 查询远程订单信息
    OrderMainDTO mainDTO = remoteBdwHotelService.queryMainOrderInfo(orderMainNo, BaseConstants.CHANNEL_TYPE_DELONG);
    if (mainDTO == null) {
      log.warn("main order info not found with orderMainNo: {}", orderMainNo);
      throw new ServiceException("订单不存在");
    }

    // 处理已取消的状态
    if (MainOrderStatusEnum.CANCELLED.getStatus().equals(mainDTO.getStatus())) {
      updateOrderStatus(proxyOrder, MainOrderStatusEnum.CANCELLED.getStatus());
      log.info("订单已取消, 订单号 {}", orderMainNo);
      return true;
    }

    // 验证订单状态
    if (!validStatuses.contains(mainDTO.getStatus())) {
      log.error("订单无法取消，订单号 {} 状态 {}", orderMainNo, mainDTO.getStatus());
      throw new ServiceException("当前订单不能取消");
    }

    // 3. 调用取消接口
    boolean isCancelled = remoteBdwHotelService.cancelOrder(orderMainNo, mainDTO.getUserId());

    // 4. 取消成功则返回成功并且更新状态
    if (isCancelled) {
      updateOrderStatus(proxyOrder, MainOrderStatusEnum.CANCELLED.getStatus());
      log.info("订单取消成功，订单号 {}", orderMainNo);
      return true;
    } else {
      log.info("订单取消失败，订单号 {}", orderMainNo);
      throw new ServiceException("订单取消失败");
    }
  }

  private void updateOrderStatus(ModProxyOrder proxyOrder, int status) {
    proxyOrder.setStatus(status);
    proxyOrder.setUpdateTime(new Date());
    modProxyOrderMapperExt.updateByPrimaryKeySelective(proxyOrder);
  }

  /**
   * 判断是否使用新接口查询价格 todo 稳定后删除
   * @return
   */
  private boolean isNewQueryPriceInterface(){
    return betterwoodConfig.getBetterwoodHotelDetailPriceUrl().endsWith("/api/price/hotelDetailPriceForDelonix");
  }

  private OrderMainAddRspDto createOrderByEmployee(CreateOrderReq req,
      RateDetailResponse rateDetailResponse, UserInfoVO userInfoVO) {
    OrderMainAddDto orderMainAddDto = new OrderMainAddDto();
    orderMainAddDto.setChannelType(BaseConstants.CHANNEL_TYPE_DELONG.toString());
    orderMainAddDto.setHotelCode(req.getHotelCode());
    orderMainAddDto.setBookerPhone(req.getBookerPhone());
    orderMainAddDto.setBookerPhoneAreaCode(
        StringUtils.isBlank(req.getBookerPhoneAreaCode()) ? BaseConstants.DEFAULT_AREA_CODE : req.getBookerPhoneAreaCode());
    orderMainAddDto.setBookerUserId(req.getBookerUserId());
    orderMainAddDto.setStartTime(new Date(req.getArrDate()));
    orderMainAddDto.setEndTime(new Date(req.getDepDate()));
    orderMainAddDto.setProductCode(rateDetailResponse.getProductCode());
    orderMainAddDto.setTotalCount(req.getRoomNum());
    orderMainAddDto.setRemark((StringUtils.isBlank(req.getRemark()) ? "" :req.getRemark())  + DEFAULT_REMARK.replace("${user}",userInfoVO.getName()));
    orderMainAddDto.setPaymentType(req.getPaymentType());
    orderMainAddDto.setOrderChannel(1);
    if (companyMemberConfig.getRefactorCompanySwitch()) {
      orderMainAddDto.setProtocolNo(req.getCompanyId());
    } else {
      orderMainAddDto.setProtocolNo(rateDetailResponse.getProtocolNo());
    }

    orderMainAddDto.setHourRoomType(0);
    orderMainAddDto.setBookerName(req.getBookerName());
    orderMainAddDto.setProdCategorySub(rateDetailResponse.getProdCategorySub());
    orderMainAddDto.setRateCode(rateDetailResponse.getRateCode());
    orderMainAddDto.setHotelRoomType(rateDetailResponse.getRoomType());
    orderMainAddDto.setIfAgreementRate(true);
    orderMainAddDto.setCancelPolicy(rateDetailResponse.getCancelDesc());
    orderMainAddDto.setIsSell(rateDetailResponse.getIsSell());
    orderMainAddDto.setCancelRule(rateDetailResponse.getCancelRule());
    orderMainAddDto.setCancelPreHour(rateDetailResponse.getCancelPreHour());
    orderMainAddDto.setUcId(userInfoVO.getUcId());
    orderMainAddDto.setPlantformType(req.getClientType());
    orderMainAddDto.setSendMsg(req.getSendSMS());
    if(CollectionUtils.isNotEmpty(req.getGuestList())) {
      List<GuestDto> guestDtoList = new ArrayList<>();
      for(GuestInfoReq guestInfoReq : req.getGuestList()) {
        GuestDto guestDto = new GuestDto();
        guestDto.setGuestName(guestInfoReq.getGuestName());
        if(StringUtils.isNotBlank(guestInfoReq.getGuestPhone())) {
          guestDto.setGuestPhone(guestInfoReq.getGuestPhone());
          guestDto.setGuestPhoneAreaCode(StringUtils.isBlank(guestInfoReq.getGuestPhoneAreaCode()) ? BaseConstants.DEFAULT_AREA_CODE : guestInfoReq.getGuestPhoneAreaCode());
        }
        guestDtoList.add(guestDto);
      }
      orderMainAddDto.setGuestList(guestDtoList);
    }
    // 百达企业卡
    if (CollectionUtils.isNotEmpty(rateDetailResponse.getPriceOffersInfoList())){
      CmsInterestOffersItem item = rateDetailResponse.getPriceOffersInfoList().get(0)
          .getInterestOffersItem();
      if (Objects.nonNull(item)){
        orderMainAddDto.setIdentityCode(item.getIdentityCode());
        orderMainAddDto.setCardId(item.getCardId());
      }
    }
    return remoteBdwHotelService.createOrderByEmployee(orderMainAddDto);
  }

  private String getProtocolNo(CompanyInfoVO companyInfoVO, String hotelCode) {
    //查询销售id
    String protocolNo = DEFAULT_PROTOCOL_NO;
    CompanyProtocol protocol = companyInfoVO.getCompanyProtocols().stream()
        .filter(companyProtocol -> hotelCode.equals(companyProtocol.getHotelCode())).findFirst()
        .orElse(null);
    if (null != protocol && StringUtils.isNotBlank(protocol.getProtocolNo())) {
      protocolNo = protocol.getProtocolNo();
    }
    return protocolNo;
  }


  /**
   * 价格排序
   * @param hotelPriceListResList
   * @return
   */
  private List<HotelPriceListRes> sortHotelPriceList(List<HotelPriceListRes> hotelPriceListResList) {
    if(CollectionUtils.isEmpty(hotelPriceListResList)) {
      return hotelPriceListResList;
    }
    Map<String, List<HotelPriceListRes>> listMap = hotelPriceListResList.stream().collect(Collectors.groupingBy(HotelPriceListRes::getRoomTypeName));
    List<String> roomTypeNameList = new ArrayList<>(listMap.keySet());
    roomTypeNameList.sort((s1,s2) -> {
      int totalAvailableRooms1 = listMap.get(s1).stream().mapToInt(HotelPriceListRes::getAvailableRooms).sum();
      int totalAvailableRooms2 = listMap.get(s2).stream().mapToInt(HotelPriceListRes::getAvailableRooms).sum();
      if(totalAvailableRooms1 - totalAvailableRooms2 == 0 && totalAvailableRooms1 == 0) {
        Long minAvgPrice1 = listMap.get(s1).stream().mapToLong(HotelPriceListRes::getAvgPrice).min().getAsLong();
        Long minAvgPrice2 = listMap.get(s2).stream().mapToLong(HotelPriceListRes::getAvgPrice).min().getAsLong();
        return minAvgPrice1.compareTo(minAvgPrice2);
      }
      if(totalAvailableRooms1 == 0) {
        return 1;
      }
      if(totalAvailableRooms2 == 0) {
        return -1;
      }
      Long minAvgPrice1 = listMap.get(s1).stream().mapToLong(HotelPriceListRes::getAvgPrice).min().getAsLong();
      Long minAvgPrice2 = listMap.get(s2).stream().mapToLong(HotelPriceListRes::getAvgPrice).min().getAsLong();
      return minAvgPrice1.compareTo(minAvgPrice2);
    });
    List<HotelPriceListRes> result = new ArrayList<>();
    roomTypeNameList.forEach(roomTypeName -> {
      List<HotelPriceListRes> hotelPriceListRes = listMap.get(roomTypeName);
      hotelPriceListRes.sort((s1,s2) -> {
        if(s1.getAvailableRooms().equals(s2.getAvailableRooms()) && s1.getAvailableRooms() == 0) {
          return s1.getAvgPrice().compareTo(s2.getAvgPrice());
        }
        if(s1.getAvailableRooms() == 0) {
          return 1;
        }
        if(s2.getAvailableRooms() == 0) {
          return -1;
        }
        return s1.getAvgPrice().compareTo(s2.getAvgPrice());
      });
      result.addAll(hotelPriceListRes);
    });
    return result;
  }

}
