package com.shands.mod.main.util;

import com.shands.mod.util.BaseConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by CC~ on 14-7-23.
 */
@Slf4j
public class DateUtil {

  public static final String DAY = "天";

  public static final String HOUR = "小时";

  public static final String MINUTE = "分钟";

  public static final String TIME_DATE_BEGIN = " 00:00:00";
  public static final String TIME_DATE_END = " 23:59:59";

  /**
   * 得到指定月的天数
   *
   * @param year
   * @param month
   * @return
   */
  public static int getMonthLastDay(int year, int month) {
    Calendar a = Calendar.getInstance();
    a.set(Calendar.YEAR, year);
    a.set(Calendar.MONTH, month - 1);
    a.set(Calendar.DATE, 1);// 把日期设置为当月第一天
    a.roll(Calendar.DATE, -1);// 日期回滚一天，也就是最后一天
    int maxDate = a.get(Calendar.DATE);
    return maxDate;
  }

  /**
   * 增加年
   *
   * @param date
   * @param i
   * @return
   */
  public static Date addYear(Date date, Integer i) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    calendar.add(Calendar.YEAR, i);
    return calendar.getTime();
  }

  // 增加月
  public static Date addMonth(Date date, Integer i) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    calendar.add(Calendar.MONTH, i);
    return calendar.getTime();
  }


  // 增加月
  public static Date addSecond(Date date, Integer i) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    calendar.add(Calendar.SECOND, i);
    return calendar.getTime();
  }

  public static String formatDateByStr(Date date) {
    if (date == null) {
      return StringUtils.EMPTY;
    } else {
      SimpleDateFormat sdf = new SimpleDateFormat(BaseConstants.FORMAT_SHORT_DATE);
      return sdf.format(date);
    }
  }

  public static String localDateTimeToStr(LocalDateTime localDateTime) {
    DateTimeFormatter fmt = DateTimeFormatter.ofPattern(BaseConstants.FORMAT_TIME);
    String format = localDateTime.format(fmt);
    return format;
  }

  /**
   * 格式化时间
   *
   * @param date
   * @return
   */
  public static String formatDate(Date date) {
    if (date == null) {
      return StringUtils.EMPTY;
    } else {
      SimpleDateFormat sdf = new SimpleDateFormat(BaseConstants.FORMAT_TIME);
      return sdf.format(date);
    }
  }

  public static String formatDatOf(Date date) {
    if (date == null) {
      return StringUtils.EMPTY;
    } else {
      SimpleDateFormat sdf = new SimpleDateFormat(BaseConstants.FORMAT_DATE);
      return sdf.format(date);
    }
  }

  public static String formatDateZero(Date date) {
    if (date == null) {
      return StringUtils.EMPTY;
    } else {
      SimpleDateFormat sdf = new SimpleDateFormat(BaseConstants.FORMAT_DATE);
      return sdf.format(date) + TIME_DATE_BEGIN;
    }
  }

  public static String formatDateEnd(Date date) {
    if (date == null) {
      return StringUtils.EMPTY;
    } else {
      SimpleDateFormat sdf = new SimpleDateFormat(BaseConstants.FORMAT_DATE);
      return sdf.format(date) + TIME_DATE_END;
    }
  }

  public static String formatYesterdayDateZero() {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, -1);
    Date time = cal.getTime();
    SimpleDateFormat sdf = new SimpleDateFormat(BaseConstants.FORMAT_DATE);
    return sdf.format(time) + TIME_DATE_BEGIN;

  }

  public static String formatYesterdayDateEnd() {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, -1);
    Date time = cal.getTime();
    SimpleDateFormat sdf = new SimpleDateFormat(BaseConstants.FORMAT_DATE);
    return sdf.format(time) + TIME_DATE_END;

  }

  public static String formatDateByTem(Date date, String tem) {
    if (date == null) {
      return StringUtils.EMPTY;
    } else {
      SimpleDateFormat sdf = new SimpleDateFormat(tem);
      return sdf.format(date);
    }
  }

  /**
   * String ---> Date
   *
   * @param str
   * @return
   */
  public static Date parseDate(String str) {
    SimpleDateFormat sdf = new SimpleDateFormat(BaseConstants.FORMAT_TIME);
    Date date = null;
    if (StringUtils.isBlank(str)) {
      return null;
    }
    try {
      date = sdf.parse(str);
    } catch (ParseException e) {
      log.error(e.getMessage(), e);
    }
    return date;
  }

  public static Date parseDateByDemo(String str, String demo) {
    SimpleDateFormat sdf = new SimpleDateFormat(demo);
    Date date = null;
    if (StringUtils.isBlank(str)) {
      return null;
    }
    try {
      date = sdf.parse(str);
    } catch (ParseException e) {
      log.error(e.getMessage(), e);
    }
    return date;
  }

  public static Date parseDateByYearAndMonth(Integer year, Integer month) {
    Calendar calendar = Calendar.getInstance();
    calendar.set(Calendar.YEAR, year);
    calendar.set(Calendar.MONTH, month - 1);
    calendar.set(Calendar.DATE, 1);
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    return calendar.getTime();
  }

  public static Date parseDateLastDay() {
    Calendar calendar = Calendar.getInstance();
    calendar.add(Calendar.YEAR, 1);
    calendar.set(Calendar.MONTH, 0);
    calendar.set(Calendar.DATE, 1);
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);
    calendar.add(Calendar.DAY_OF_YEAR, -1);
    return calendar.getTime();
  }

  public static Date parseDayToZero(Date date) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);
    return calendar.getTime();
  }

  public static Date parseDayToZero(String s) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(parseDate(s));
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);
    return calendar.getTime();
  }

  public static Date parseDayAddOne(Date date) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    calendar.add(Calendar.DAY_OF_MONTH, 1);
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);
    return calendar.getTime();
  }

  public static Date parseDayAdd(Date date, Integer i) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    calendar.add(Calendar.DAY_OF_MONTH, i);
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);
    return calendar.getTime();
  }

  public static Date parseLastDateByYearAndMonth(Integer year, Integer month) {
    Calendar calendar = Calendar.getInstance();
    calendar.set(Calendar.YEAR, year);
    calendar.set(Calendar.MONTH, month - 1);
    calendar.set(Calendar.DATE, 1);
    calendar.roll(Calendar.DATE, -1);
    calendar.set(Calendar.HOUR_OF_DAY, 23);
    calendar.set(Calendar.MINUTE, 59);
    calendar.set(Calendar.SECOND, 59);
    calendar.set(Calendar.MILLISECOND, 999);
    return calendar.getTime();
  }

  public static Boolean equalDate(Date date1, Date date2) {
    if (formatDate(date1).equals(formatDate(date2))) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * 获取当前日期是星期几<br>
   *
   * @param dt
   * @return 当前日期是星期几
   */
  public static String getWeekOfDate(Date dt) {
    String[] weekDays = {"日", "一", "二", "三", "四", "五", "六"};
    Calendar cal = Calendar.getInstance();
    cal.setTime(dt);
    int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
    if (w < 0) {
      w = 0;
    }
    return "周".concat(weekDays[w]);
  }

  /**
   * '计算两个时间相隔几天'
   *
   * @param date1
   * @param date2
   * @return
   */
  public static Integer intervaDays(Date date1, Date date2) {
    Long l = parseDayToZero(date2).getTime() - parseDayToZero(date1).getTime();
    Long li = l / (1000 * 60 * 60 * 24);
    return li.intValue();
  }

  public static Calendar getCalendar(Date date) {
    Calendar calDate = Calendar.getInstance();
    calDate.setTime(date);
    return calDate;
  }

  /**
   * 获取两个日期相差的月数
   *
   * @param d1
   *            较大的日期
   * @param d2
   *            较小的日期
   * @return 如果d1>d2返回 月数差 否则返回0
   */
  public static int getMonthDiff(Date d1, Date d2) {
    Calendar c1 = Calendar.getInstance();
    Calendar c2 = Calendar.getInstance();
    c1.setTime(d1);
    c2.setTime(d2);
    if (c1.getTimeInMillis() < c2.getTimeInMillis()) {
      return 0;
    }
    int year1 = c1.get(Calendar.YEAR);
    int year2 = c2.get(Calendar.YEAR);
    int month1 = c1.get(Calendar.MONTH);
    int month2 = c2.get(Calendar.MONTH);
    int day1 = c1.get(Calendar.DAY_OF_MONTH);
    int day2 = c2.get(Calendar.DAY_OF_MONTH);
    // 获取年的差值 假设 d1 = 2015-8-16 d2 = 2011-9-30
    int yearInterval = year1 - year2;
    // 如果 d1的 月-日 小于 d2的 月-日 那么 yearInterval-- 这样就得到了相差的年数
    if (month1 < month2 || month1 == month2 && day1 < day2) {
      yearInterval--;
    }
    // 获取月数差值
    int monthInterval = (month1 + 12) - month2;
    if (day1 < day2) {
      monthInterval--;
    }
    monthInterval %= 12;
    return yearInterval * 12 + monthInterval;
  }

  /**
   * 获得该月第一天
   *
   * @param year
   * @param month
   * @return
   */
  public static String getFirstDayOfMonth(int year, int month) {
    Calendar cal = Calendar.getInstance();
    // 设置年份
    cal.set(Calendar.YEAR, year);
    // 设置月份
    cal.set(Calendar.MONTH, month - 1);
    // 获取某月最小天数
    int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
    // 设置日历中月份的最小天数
    cal.set(Calendar.DAY_OF_MONTH, firstDay);
    // 格式化日期
    SimpleDateFormat sdf = new SimpleDateFormat(BaseConstants.FORMAT_DATE);
    String firstDayOfMonth = sdf.format(cal.getTime());
    return firstDayOfMonth;
  }

  /**
   * 获得该月最后一天
   *
   * @param year
   * @param month
   * @return
   */
  public static String getLastDayOfMonth(int year, int month) {
    Calendar cal = Calendar.getInstance();
    // 设置年份
    cal.set(Calendar.YEAR, year);
    // 设置月份
    cal.set(Calendar.MONTH, month - 1);
    // 获取某月最大天数
    int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
    // 设置日历中月份的最大天数
    cal.set(Calendar.DAY_OF_MONTH, lastDay);
    // 格式化日期
    SimpleDateFormat sdf = new SimpleDateFormat(BaseConstants.FORMAT_DATE);
    String lastDayOfMonth = sdf.format(cal.getTime());
    return lastDayOfMonth;

  }

  /**
   * 获得该月最后一天
   *
   * @return
   */
  public static String getLastDayOfMonth(String formatStr, int month, Date date) {
    SimpleDateFormat format = new SimpleDateFormat(formatStr);
    Calendar calendar = Calendar.getInstance();

    // 设置为传入的时间
    calendar.setTime(date);

    // 设置月份
    calendar.add(Calendar.MONTH, -month);

    // 获取某月最大天数
    int lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);

    // 设置日历中月份的最大天数
    calendar.set(Calendar.DAY_OF_MONTH, lastDay);

    // 返回格式化后的日期字符串
    return format.format(calendar.getTime());
  }


  /**
   * 根据参数获取指定时间
   *
   * @param amount
   * @return
   */
  public static String getDay(Integer amount) {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, amount);
    Date time = cal.getTime();
    return new SimpleDateFormat(BaseConstants.FORMAT_DATE).format(time);
  }

  /**
   * 根据参数获取指定时间
   *
   * @param amount
   * @return
   */
  public static String getDayMinute(Integer amount) {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, amount);
    Date time = cal.getTime();
    return new SimpleDateFormat(BaseConstants.FORMAT_TIME_MINUTE).format(time);
  }

  /**
   * 获取前天
   *
   * @return String
   */
  public static String geTanteayer() {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, -2);
    Date time = cal.getTime();
    return new SimpleDateFormat(BaseConstants.FORMAT_DATE).format(time);
  }

  /**
   * 获取昨天
   *
   * @return String
   */
  public static String getYestoday() {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, -1);
    Date time = cal.getTime();
    return new SimpleDateFormat(BaseConstants.FORMAT_DATE).format(time);
  }

  /**
   * 获取昨天所在月
   *
   * @return String
   */
  public static String getYestodayByMonth() {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, -1);
    Date time = cal.getTime();
    return new SimpleDateFormat(BaseConstants.FORMAT_MONTH).format(time);
  }

  /**
   * 获取昨天所在年
   *
   * @return String
   */
  public static String getYestodayByYears() {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, -1);
    Date time = cal.getTime();
    return new SimpleDateFormat("yyyy").format(time);
  }

  /**
   * 获取当前日期后一天(支付宝微信付款用)
   *
   * @param type
   *            支付宝 or 微信
   * @return
   */
  public static String getNextDate(String type, int date) {
    SimpleDateFormat sdf;
    if ("zfb".equals(type)) {
      sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    } else {
      sdf = new SimpleDateFormat("yyyyMMddHHmmss");
    }
    Long nextDay = System.currentTimeMillis() + date * 24 * 60 * 60 * 1000;
    return sdf.format(nextDay);
  }

  public static String getDateFormatStr(String type) {
    if ("day".equals(type)) {
      Calendar cal = Calendar.getInstance();
      cal.add(Calendar.DATE, 0);
      Date time = cal.getTime();
      return new SimpleDateFormat(BaseConstants.FORMAT_DATE).format(time);
    } else if ("month".equals(type)) {
      Calendar cal = Calendar.getInstance();
      cal.add(Calendar.MONTH, 0);
      Date time = cal.getTime();
      return new SimpleDateFormat(BaseConstants.FORMAT_MONTH).format(time);
    } else {
      return StringUtils.EMPTY;
    }
  }

  public static String getLastDateFormatStr(String type) {
    if ("day".equals(type)) {
      Calendar cal = Calendar.getInstance();
      cal.add(Calendar.DATE, -1);
      Date time = cal.getTime();
      return new SimpleDateFormat(BaseConstants.FORMAT_DATE).format(time);
    } else if ("month".equals(type)) {
      Calendar cal = Calendar.getInstance();
      cal.add(Calendar.MONTH, -1);
      Date time = cal.getTime();
      return new SimpleDateFormat(BaseConstants.FORMAT_MONTH).format(time);
    } else {
      return StringUtils.EMPTY;
    }
  }

  public static List<String> intervalDate(String start, String end) throws Exception {
    List<String> stringList = new ArrayList<>();
    SimpleDateFormat sdf = new SimpleDateFormat(BaseConstants.FORMAT_DATE);
    Calendar st = Calendar.getInstance();
    Calendar ed = Calendar.getInstance();
    st.setTime(sdf.parse(start));
    ed.setTime(sdf.parse(end));
    while (!st.after(ed)) {
      stringList.add(sdf.format(st.getTime()));
      System.out.println(sdf.format(st.getTime()));
      st.add(Calendar.DAY_OF_YEAR, 1);
    }
    return stringList;
  }

  public static List<String> getDaysByYear(int year) {
    Calendar c = Calendar.getInstance();
    List<String> dates = new ArrayList<>();
    for (int i = 0; i < 12; i++) {
      c.set(year, i, 1);
      int lastDay = c.getActualMaximum(Calendar.DATE);
      for (int j = 1; j <= lastDay; j++) {
        String month = StringUtils.EMPTY;
        String day = StringUtils.EMPTY;
        if (i < 9) {
          month = "-0" + (i + 1);
        } else {
          month = "-" + (i + 1);
        }
        if (j < 10) {
          day = "-0" + j;
        } else {
          day = "-" + j;
        }
        String date = year + month + day;
        System.out.println(date);
        dates.add(date);
      }
    }
    return dates;
  }

  public static Map<String, String> getLastStartTimeAndEndTime(String timeType) {
    Map<String, String> map = new HashMap<>();
    String startTime = StringUtils.EMPTY;
    String endTime = StringUtils.EMPTY;
    Calendar cal_1 = Calendar.getInstance();
    cal_1.add(Calendar.DATE, -1);
    if ("years".equals(timeType)) {
      cal_1.set(Calendar.DAY_OF_MONTH, 1);
      cal_1.set(Calendar.MONTH, 0);
      startTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
      cal_1.set(Calendar.MONTH, 11);
      cal_1.set(Calendar.DAY_OF_MONTH, cal_1.getActualMaximum(Calendar.DAY_OF_MONTH));
      endTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
    } else if ("month".equals(timeType)) {
      cal_1.add(Calendar.MONTH, -1);
      cal_1.set(Calendar.DAY_OF_MONTH, 1);
      startTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
      cal_1.set(Calendar.DAY_OF_MONTH, cal_1.getActualMaximum(Calendar.DAY_OF_MONTH));
      endTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
    } else {

      startTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
      endTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
    }
    map.put("startTime", startTime + TIME_DATE_BEGIN);
    map.put("endTime", endTime + TIME_DATE_END);
    return map;
  }

  public static Map<String, String> getStartTimeAndEndTime(String timeType) {
    Map<String, String> map = new HashMap<>();
    String startTime = StringUtils.EMPTY;
    String endTime = StringUtils.EMPTY;
    Calendar cal_1 = Calendar.getInstance();
    cal_1.setTime(new Date());
    if ("years".equals(timeType)) {
      cal_1.set(Calendar.DAY_OF_MONTH, 1);
      cal_1.set(Calendar.MONTH, 0);
      startTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
      cal_1.set(Calendar.MONTH, 11);
      cal_1.set(Calendar.DAY_OF_MONTH, cal_1.getActualMaximum(Calendar.DAY_OF_MONTH));
      endTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
    } else if ("month".equals(timeType)) {
      cal_1.set(Calendar.DAY_OF_MONTH, 1);
      startTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
      cal_1.set(Calendar.DAY_OF_MONTH, cal_1.getActualMaximum(Calendar.DAY_OF_MONTH));
      endTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
    } else {
      startTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
      endTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
    }
    map.put("startTime", startTime + TIME_DATE_BEGIN);
    map.put("endTime", endTime + TIME_DATE_END);
    return map;
  }

  public static Map<String, String> getStartTimeAndEndTime(String timeType, String time) {
    Map<String, String> map = new HashMap<>();
    String startTime = StringUtils.EMPTY;
    String endTime = StringUtils.EMPTY;
    Calendar cal_1 = Calendar.getInstance();
    if ("years".equals(timeType)) {
      cal_1.setTime(cn.hutool.core.date.DateUtil.parse(time, "yyyy"));
      cal_1.set(Calendar.DAY_OF_MONTH, 1);
      cal_1.set(Calendar.MONTH, 0);
      startTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
      cal_1.set(Calendar.MONTH, 11);
      cal_1.set(Calendar.DAY_OF_MONTH, cal_1.getActualMaximum(Calendar.DAY_OF_MONTH));
      endTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
    } else if ("month".equals(timeType)) {
      cal_1.setTime(cn.hutool.core.date.DateUtil.parse(time, BaseConstants.FORMAT_MONTH));
      cal_1.set(Calendar.DAY_OF_MONTH, 1);
      startTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
      cal_1.set(Calendar.DAY_OF_MONTH, cal_1.getActualMaximum(Calendar.DAY_OF_MONTH));
      endTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
    } else {
      cal_1.setTime(cn.hutool.core.date.DateUtil.parse(time, BaseConstants.FORMAT_DATE));
      startTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
      endTime = cn.hutool.core.date.DateUtil.format(cal_1.getTime(), BaseConstants.FORMAT_DATE);
    }
    map.put("startTime", startTime + TIME_DATE_BEGIN);
    map.put("endTime", endTime + TIME_DATE_END);
    return map;
  }

  /**
   * 判断校验日期是否在日期区间（包含）
   *
   * @param checkDate
   *            校验日期
   * @param dateBegin
   *            开始日期
   * @param dateEnd
   *            结束日期
   * @return
   */
  public static boolean isDateInterval(Date checkDate, Date dateBegin, Date dateEnd) {
    if (null == dateBegin || null == dateEnd) {
      return false;
    }
    LocalDate localCheck = dateToLocalDate(checkDate);
    LocalDate localBegin = dateToLocalDate(dateBegin);
    LocalDate localEnd = dateToLocalDate(dateEnd);
    if (localBegin.isBefore(localCheck) && localEnd.isAfter(localCheck)) {
      return true;
    }
    if (localCheck.equals(localBegin) || localCheck.equals(localEnd)) {
      return true;
    }
    return false;
  }

  /**
   * localDate -> Date
   *
   * @param localDate
   * @return
   */
  public static Date localDateToDate(LocalDate localDate) {
    return Date.from(localDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
  }

  /**
   * Date -> LocalDate
   *
   * @param date
   * @return
   */
  public static LocalDate dateToLocalDate(Date date) {
    ZoneId zone = ZoneId.systemDefault();
    LocalDate localDateTime = date.toInstant().atZone(zone).toLocalDate();
    return localDateTime;
  }

  /**
   * Date -> LocalDateTime
   *
   * @param date
   * @return
   */
  public static LocalDateTime dateToLocalDateTime(Date date) {
    ZoneId zone = ZoneId.systemDefault();
    LocalDateTime localDateTime = date.toInstant().atZone(zone).toLocalDateTime();
    return localDateTime;
  }

  /**
   * LocalDateTime -> Date
   *
   * @param localDateTime
   * @return
   */
  public static Date localDateTimeToDate(LocalDateTime localDateTime) {
    ZoneId zone = ZoneId.systemDefault();
    Instant instant = localDateTime.atZone(zone).toInstant();
    return Date.from(instant);
  }

  /**
   * 获取上个月开始时间
   *
   * @return
   * @throws ParseException
   */
  public static String getBeforeFirstMonthdateByString() throws ParseException {
    SimpleDateFormat sf = new SimpleDateFormat(BaseConstants.FORMAT_DATE);
    Calendar calendar = Calendar.getInstance();
    calendar.add(Calendar.MONTH, -1);
    calendar.set(Calendar.DAY_OF_MONTH, 1);
    return sf.format(calendar.getTime()) + TIME_DATE_BEGIN;
  }

  /**
   * 获取上个月结束时间
   *
   * @return
   * @throws ParseException
   */
  public static String getBeforeLastMonthdateByString() throws ParseException {
    SimpleDateFormat sf = new SimpleDateFormat(BaseConstants.FORMAT_DATE);
    Calendar calendar = Calendar.getInstance();
    int month = calendar.get(Calendar.MONTH);
    calendar.set(Calendar.MONTH, month - 1);
    calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
    return sf.format(calendar.getTime()) + TIME_DATE_END;
  }

  /**
   * 获取上个月月份
   *
   * @return
   */
  public static String getLastMonth(String formatStr, Date date) {
    SimpleDateFormat format = new SimpleDateFormat(formatStr);
    //Date date = new Date();
    Calendar calendar = Calendar.getInstance();
    // 设置为当前时间
    calendar.setTime(date);
    // 设置为上一个月
    calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
    date = calendar.getTime();
    return format.format(date);
  }



  /**
   * 获取上个月月份
   *
   * @return
   */
  public static String getLastMonth(int month, String formatStr) {
    SimpleDateFormat format = new SimpleDateFormat(formatStr);
    Date date = new Date();
    Calendar calendar = Calendar.getInstance();
    // 设置为当前时间
    calendar.setTime(date);
    // 设置为上一个月
    calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - month);
    date = calendar.getTime();
    return format.format(date);
  }


  /**
   * 获取上个月月份
   *
   * @return
   */
  public static String getLastMonth() {
    SimpleDateFormat format = new SimpleDateFormat(BaseConstants.FORMAT_MONTH);
    Date date = new Date();
    Calendar calendar = Calendar.getInstance();
    // 设置为当前时间
    calendar.setTime(date);
    // 设置为上一个月
    calendar.add(Calendar.MONTH, -1);
    date = calendar.getTime();
    return format.format(date);
  }




  public static String getStringMonth(Date date) {
    SimpleDateFormat format = new SimpleDateFormat("yyyMM");
    // Date date = new Date();
    Calendar calendar = Calendar.getInstance();
    // 设置为当前时间
    calendar.setTime(date);
    // 设置为上一个月
    calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
    date = calendar.getTime();
    return format.format(date);
  }

  /**
   * 获取季度 第一天或最后一天
   *
   * @param quarters
   *            0本季度，1下季度，-1上季度 以此类推
   * @param isFirst
   *            true获取开始时间 false获取结束时间
   * @return java.lang.String
   */
  public static String getStartOrEndDayOfQuarter(long quarters, Boolean isFirst) {
    LocalDate resDate = LocalDate.now().plusMonths(quarters * 3);
    Month month = resDate.getMonth();
    Month firstMonthOfQuarter = month.firstMonthOfQuarter();
    Month endMonthOfQuarter = Month.of(firstMonthOfQuarter.getValue() + 2);
    if (isFirst) {
      resDate = LocalDate.of(resDate.getYear(), firstMonthOfQuarter, 1);
    } else {
      resDate =
          LocalDate.of(resDate.getYear(), endMonthOfQuarter, endMonthOfQuarter.length(resDate.isLeapYear()));
    }
    return resDate.toString();
  }

  /**
   * 格式转换
   *
   * @param time
   * @return
   */
  public static String getStringTime(String time) {
    SimpleDateFormat fmt1 = new SimpleDateFormat(BaseConstants.FORMAT_DATE);
    SimpleDateFormat fmt2 = new SimpleDateFormat("yyyyMM");
    try {
      return fmt2.format(fmt1.parse(time));
    } catch (ParseException e) {
      log.error(e.getMessage(), e);
      return null;
    }
  }

  /**
   *  获取当前季度
   * @return
   */
  public static String getCurrentQuarter(){
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(new Date());
    int q = (calendar.get(Calendar.MONTH)+1 + 2) / 3;
    int year = calendar.get(Calendar.YEAR);
    return q == 1 ? (year - 1) + "Q4" : year + "Q" + (q-1);
  }

  /**
   *  判断当前时间是否在审核期 审核期为 每季度的第7天到第22天
   * @return
   */
  public static boolean valid(int startDay, int endDay){
    boolean bool;
    long currentDate = System.currentTimeMillis();
    long start = getDateByCurrentQuarter(startDay);
    long end = getDateByCurrentQuarter(endDay);
    bool = start < currentDate;
    if(bool){
      bool = currentDate < end;
    }
    return bool;
  }

  /**
   * 获取当前季度的开始时间
   * @return
   */
  public static long getDateByCurrentQuarter(int day){
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(new Date());
    int q = (calendar.get(Calendar.MONTH)+1 + 2) / 3;
    calendar.set(Calendar.DATE, day);
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    switch (q){
      case 1:
        calendar.set(Calendar.MONTH, 0);
        break;
      case 2:
        calendar.set(Calendar.MONTH, 3);
        break;
      case 3:
        calendar.set(Calendar.MONTH, 6);
        break;
      case 4:
        calendar.set(Calendar.MONTH, 9);
        break;
    }
    return calendar.getTime().getTime();
  }

  /**
   * 判断是否当前月份 yyyy-mm
   * @param monthStr
   * @return
   */
  public static boolean isCurrentMonth(String monthStr) {
    // 定义日期格式
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(BaseConstants.FORMAT_MONTH);

    try {
      // 解析输入的月份字符串
      YearMonth inputMonth = YearMonth.parse(monthStr, formatter);

      // 获取当前月份
      YearMonth currentMonth = YearMonth.now();

      // 比较输入的月份和当前月份
      return inputMonth.equals(currentMonth);
    } catch (DateTimeParseException e) {
      // 如果解析失败，返回false
      return false;
    }
  }

  public static boolean isLessThanCurrentMonth(String monthStr) {
    // 定义日期格式
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(BaseConstants.FORMAT_MONTH);

    try {
      // 解析输入的月份字符串
      YearMonth inputMonth = YearMonth.parse(monthStr, formatter);

      // 获取当前月份
      YearMonth currentMonth = YearMonth.now();

      // 比较输入的月份和当前月份
      return inputMonth.isBefore(currentMonth);
    } catch (DateTimeParseException e) {
      // 如果解析失败，返回false
      return false;
    }
  }

  public static boolean isLessThanCurrentMonth(Date bizMonth) {
    // 将 Date 转换为 YearMonth
    YearMonth inputMonth = YearMonth.from(bizMonth.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());

    // 获取当前月份
    YearMonth currentMonth = YearMonth.from(LocalDate.now());

    // 比较输入的月份和当前月份
    return inputMonth.isBefore(currentMonth);
  }


  /**
   * 计算两个时间的分差
   * @param startDate 开始时间
   * @param endDate 结束时间
   * @return
   */
  public static long getMinuteDifference(Date startDate, Date endDate) {
    endDate = endDate == null ? new Date() : endDate;
    startDate = startDate == null ? new Date() : startDate;
    long diff = endDate.getTime() - startDate.getTime();
    return diff / (60000);
  }


  public static long getTodayStartTime(){
    Calendar calendar = Calendar.getInstance();
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);
    return calendar.getTimeInMillis();
  }


  public static long getTodayEndTime(){
    Calendar calendar = Calendar.getInstance();
    calendar.set(Calendar.HOUR_OF_DAY, 23);
    calendar.set(Calendar.MINUTE, 59);
    calendar.set(Calendar.SECOND, 59);
    calendar.set(Calendar.MILLISECOND, 999);
    return calendar.getTimeInMillis();
  }




  /*public static void main(String[] args) {
    System.out.println(getCurrentQuarter());
    System.out.println(valid(1,11));

    System.out.println(getDateByCurrentQuarter(1));
  }*/
}
