package com.shands.mod.main.service.voucher.strategy;

import com.shands.mod.dao.model.enums.ShareChannelEnum;
import com.shands.mod.dao.model.sales.tool.dto.QrCodeParameters;
import com.shands.mod.dao.model.sales.tool.dto.ShareParameters;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.main.config.BetterwoodConfig;
import com.shands.mod.main.config.VoucherBenefitConfig;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description:
 * @Author: wj
 * @Date: 2025/2/19 10:29
 */
@Service("voucherQrCodeStrategy")
@Slf4j
public class VoucherQrCodeStrategy implements VoucherShareStrategy {

  public static final String AMOUNT_CARD_TITLE = "%d元立减券，扫码即享";
  public static final String BENEFIT_CARD_TITLE = "%s，扫码即享";

  public static final String CARD_DESC = "- 更多会员升级礼遇 -";

  @Autowired
  private BetterwoodConfig betterwoodConfig;

  @Resource
  private VoucherBenefitConfig voucherBenefitConfig;

  @Override
  public String getChannelId() {
    return ShareChannelEnum.QR_CODE.getChannelId();
  }

  @Override
  public ShareParameters execute(ModHotelInfo modHotelInfo, List<String> templateIdList,
      String recordCode, Integer worthPrice) {
    QrCodeParameters parameters = new QrCodeParameters();
    parameters.setTitle(generateTitle(worthPrice, templateIdList));
    parameters.setQrCode(generateH5Link(recordCode));
    parameters.setDescription(CARD_DESC);
    return parameters;
  }

  private String generateTitle(Integer worthPrice, List<String> templateIdList) {
    if (Objects.nonNull(worthPrice) && worthPrice > 0) {
      return String.format(AMOUNT_CARD_TITLE, worthPrice / 100);
    } else {
      String benefitTitle = getBenefitTitle(templateIdList);
      return String.format(BENEFIT_CARD_TITLE, benefitTitle);
    }
  }

  /**
   * 根据早餐>升房>延迟优先级查找第一个符合条件的权益券，并返回对应的标题
   * @param templateIds 模板ID列表
   * @return 权益券标题
   */
  private String getBenefitTitle(List<String> templateIds) {
    return voucherBenefitConfig.getTemplates().stream()
        .filter(benefitTemplate -> templateIds.stream()
            .anyMatch(templateId -> templateId.contains(benefitTemplate.getCode())))
        .findFirst()
        .map(benefitTemplate -> benefitTemplate.getName().replace("券", "礼遇"))
        .orElse("免费权益");
  }

  /**
   * 生成 H5 跳转链接
   * @param code 用户的 code
   * @return 完整的跳转 URL
   */
  private String generateH5Link(String code){
    // 使用传入的 code 格式化 h5VoucherPage 模板
    return String.format(betterwoodConfig.getH5VoucherPage(), code);

  }

}
