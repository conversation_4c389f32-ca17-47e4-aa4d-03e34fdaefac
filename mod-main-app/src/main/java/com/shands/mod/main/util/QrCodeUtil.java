package com.shands.mod.main.util;

import lombok.extern.slf4j.Slf4j;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import javax.imageio.ImageIO;

@Slf4j
public class QrCodeUtil {

  /**
   * 合成二维码和背景图片为新的二维码图片
   *
   * @param backPicPath
   * @param code
   * @param
   */
  public static File combineCodeAndPicToInputstream(String backPicPath, BufferedImage code,Integer qrCodeId,String roomNum) {
    try {
      long name = System.currentTimeMillis();
      // 下载背景图片到本地
      QrCodeUtil.saveToFile(backPicPath, name);

      // 修改图片属性
      //二维码或小图在大图的左上角坐标
      //为了保证大图背景不变色，formatName必须为"png"
      BufferedImage big = ImageIO.read(new File("./" + name + ".png"));
      int radius = (code.getWidth() + code.getHeight()) / 40;//圆角半径
      BufferedImage small = setRadius(code, radius, 0, 0);
      Graphics2D g = big.createGraphics();
      int x = (big.getWidth() - small.getWidth()) / 2;
      int y = (int) (big.getHeight()*0.42745);
      g.drawImage(small, x, y, small.getWidth(), small.getHeight(), null);
      //g.dispose();
      if(roomNum != null) {
        Color color = new Color(252, 219, 218, 255);
        g.setColor(color); //根据图片的背景设置水印颜色
        Font font = new Font("黑体", Font.BOLD, 24);
        g.setFont(font);
        g.drawString("No."+roomNum, (float) (big.getWidth() * 0.45),
            (float) (big.getHeight() * 0.82));  //画出水印 qrCodeId.toString()
      }
      g.dispose();

      // 生成新的二维码图片
      File newScanImage = new File("./"+qrCodeId+".png");
     // ImageIO.write(big, "png", new FileOutputStream(newScanImage));
      ImageIO.write(big, "png", newScanImage);
      // 删除本地新的二维码图片
      QrCodeUtil.delFolder("./" + name + ".png");
      return newScanImage;
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return null;
  }


  public static void saveToFile(String destUrl, long name) {
    FileOutputStream fos = null;
    BufferedInputStream bis = null;
    HttpURLConnection httpUrl = null;
    URL url = null;
    int BUFFER_SIZE = 1024;
    byte[] buf = new byte[BUFFER_SIZE];
    int size = 0;
    try {
      url = new URL(destUrl);
      try {
        httpUrl = (HttpURLConnection) url.openConnection();
      } catch (IOException e) {
        log.error(e.getMessage(), e);
      }
      httpUrl.connect();
      bis = new BufferedInputStream(httpUrl.getInputStream());
      fos = new FileOutputStream("./" + name + ".png");
      while ((size = bis.read(buf)) != -1) {
        fos.write(buf, 0, size);
      }
      fos.flush();
    } catch (IOException | ClassCastException e) {
      log.error(e.getMessage(), e);
    } finally {
      try {
        fos.close();
        bis.close();
        httpUrl.disconnect();
      } catch (IOException e) {
      } catch (NullPointerException e) {
      }
    }
  }

  public static void delFolder(String folderPath) {
    try {
      delAllFile(folderPath); // 删除完里面所有内容
      String filePath = folderPath;
      filePath = filePath.toString();
      java.io.File myFilePath = new java.io.File(filePath);
      myFilePath.delete(); // 删除空文件夹
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }

  public static boolean delAllFile(String path) {
    boolean flag = false;
    File file = new File(path);
    if (!file.exists()) {
      return flag;
    }
    if (!file.isDirectory()) {
      return flag;
    }
    String[] tempList = file.list();
    File temp = null;
    for (int i = 0; i < tempList.length; i++) {
      if (path.endsWith(File.separator)) {
        temp = new File(path + tempList[i]);
      } else {
        temp = new File(path + File.separator + tempList[i]);
      }
      if (temp.isFile()) {
        temp.delete();
      }
      if (temp.isDirectory()) {
        delAllFile(path + "/" + tempList[i]);// 先删除文件夹里面的文件
        delFolder(path + "/" + tempList[i]);// 再删除空文件夹
        flag = true;
      }
    }
    return flag;
  }

  /**
   * 图片设置圆角
   *
   * @param srcImage
   * @param radius
   * @param border
   * @param padding
   * @return
   * @throws IOException
   */
  public static BufferedImage setRadius(BufferedImage srcImage, int radius, int border, int padding)
      throws IOException {
    int width = srcImage.getWidth();
    int height = srcImage.getHeight();
    int canvasWidth = width + padding * 2;
    int canvasHeight = height + padding * 2;

    BufferedImage image = new BufferedImage(canvasWidth, canvasHeight, BufferedImage.TYPE_INT_ARGB);
    Graphics2D gs = image.createGraphics();
    gs.setComposite(AlphaComposite.Src);
    gs.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
    gs.setColor(Color.WHITE);
    gs.fill(new RoundRectangle2D.Float(0, 0, canvasWidth, canvasHeight, radius, radius));
    gs.setComposite(AlphaComposite.SrcAtop);
    gs.drawImage(setClip(srcImage, radius), padding, padding, null);
    if (border != 0) {
      gs.setColor(Color.GRAY);
      gs.setStroke(new BasicStroke(border));
      gs.drawRoundRect(padding, padding, canvasWidth - 2 * padding, canvasHeight - 2 * padding,
          radius, radius);
    }
    gs.dispose();
    return image;
  }

  public static BufferedImage setClip(BufferedImage srcImage, int radius) {
    int width = srcImage.getWidth();
    int height = srcImage.getHeight();
    BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
    Graphics2D gs = image.createGraphics();

    gs.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
    gs.setClip(new RoundRectangle2D.Double(0, 0, width, height, radius, radius));
    gs.drawImage(srcImage, 0, 0, null);
    gs.dispose();
    return image;
  }

}