package com.shands.mod.main.util;

/**
 * @ClassName: CorsConfig
 * @Function: TODO
 * @Date: 2020/11/19
 * @Author: Xin<PERSON><PERSON>_<PERSON>
 */

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 跨域配置
 */
//@Configuration
public class CorsConfig implements WebMvcConfigurer {
    /**
     * 跨域处理
     *
     * @param registry
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**").allowedOrigins("*").allowedMethods("POST", "GET", "PUT", "OPTIONS", "DELETE")
            .maxAge(3600).allowCredentials(true).allowedHeaders("*");
    }
}
