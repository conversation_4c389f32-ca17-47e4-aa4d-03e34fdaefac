package com.shands.mod.main.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum NewDataBoardUrlEnum {

    BETTERWOOD_CARD_CNT_DATA_SELF("bdCardCnt", "/sxe/performance?tab=0&type=cardSalesMembers", false),
    BETTERWOOD_CARD_CNT_DATA_HOTEL("bdCardCnt", "/sxe/bdwcard", true)
    ;


    private final String code;
    private final String url;
    private final boolean isHotel;


    public static String getUrl(String code, boolean isHotel) {
        for (NewDataBoardUrlEnum value : NewDataBoardUrlEnum.values()) {
            if (value.getCode().equals(code) && value.isHotel() == isHotel) {
                return value.getUrl();
            }
        }
        return "";
    }


}
