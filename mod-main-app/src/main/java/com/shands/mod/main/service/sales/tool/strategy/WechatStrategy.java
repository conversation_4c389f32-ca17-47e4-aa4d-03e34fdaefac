package com.shands.mod.main.service.sales.tool.strategy;

import cn.hutool.core.net.url.UrlQuery;
import com.shands.mod.dao.model.enums.ShareChannelEnum;
import com.shands.mod.dao.model.sales.tool.dto.ShareInfoDTO;
import com.shands.mod.dao.model.sales.tool.dto.ShareParameters;
import com.shands.mod.dao.model.sales.tool.dto.WechatParameters;
import com.shands.mod.main.config.BetterwoodConfig;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 微信小程序分享
 * @Author: wj
 * @Date: 2024/8/12 18:07
 */
@Service("wechatStrategy")
@Slf4j
public class WechatStrategy implements ShareStrategy {

  public static final String CARD_TITLE = "%s已订好，请确认行程！";

  public static final String CARD_DESC = "我预订好了酒店，请点击确认行程信息";

  @Autowired
  private BetterwoodConfig betterwoodConfig;

  @Override
  public String getChannelId() {
    return ShareChannelEnum.WECHAT.getChannelId();
  }

  @Override
  public ShareParameters execute(ShareInfoDTO dto) {
    WechatParameters parameters = new WechatParameters();
    String path = betterwoodConfig.getBetterwoodJourneyCardPage();
    try {
      Map<String, String> map = new HashMap<>();
      map.put("eventType", dto.getEventType());
      map.put("id", dto.getOrderMainNo());
      map.put("salesId", dto.getSalesId());
      UrlQuery urlQuery = new UrlQuery().addAll(map);
      path = path.concat("?").concat(urlQuery.toString());
    } catch (Exception e) {
      log.error("WechatStrategy.execute error: ", e);
    }
    parameters.setPath(path);
    String title = String.format(CARD_TITLE, dto.getHotelName());
    parameters.setTitle(title);
    parameters.setDescription(CARD_DESC);
    parameters.setImageUrl(dto.getImageUrl());

    return parameters;
  }

  @Override
  public ShareInfoDTO queryShareParam(String param) {
    return null;
  }

}
