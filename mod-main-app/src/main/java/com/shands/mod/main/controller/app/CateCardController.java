package com.shands.mod.main.controller.app;


import com.betterwood.log.core.annotation.ResultLog;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.shands.mod.dao.model.catecard.req.MiniAppQrCodeReqDto;
import com.shands.mod.dao.model.catecard.req.UserSaleCardRecordReqDto;
import com.shands.mod.dao.model.catecard.resp.AppQrCodeResp;
import com.shands.mod.dao.model.catecard.resp.CateCardDetailInfoResp;
import com.shands.mod.dao.model.catecard.resp.CateCardInfoResp;
import com.shands.mod.dao.model.catecard.resp.UserInfoResp;
import com.shands.mod.dao.model.catecard.resp.UserSaleCardRecordResp;
import com.shands.mod.main.service.betterwood.resp.MiniAppQrCodeResp;
import com.shands.mod.main.service.catecard.CateCardService;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@RequestMapping("/card")
@RestController
public class CateCardController {

  @Autowired
  private CateCardService cateCardService;

  @ApiOperation(value = "售卖权益卡前置校验")
  @GetMapping("/user/preSaleCheck")
  public ResultVO<Boolean> preSaleCheck() {
    return ResultVO.success(cateCardService.preSaleCheck());
  }


  @ApiOperation(value = "权益卡列表")
  @GetMapping("/list")
  public ResultVO<List<CateCardDetailInfoResp>> cardList(@RequestParam(value = "getAllInfo", defaultValue = "true") boolean getAllInfo) {
    return ResultVO.success(cateCardService.cardDetailList(getAllInfo));
  }


  @ApiOperation(value = "获取卡详情")
  @GetMapping("/detail")
  @ResultLog(name = "CateCardController.detail", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<CateCardDetailInfoResp> queryCardDetailInfo(@RequestParam("cardId") Long cardId) {
    return ResultVO.success(cateCardService.queryCardDetailInfo(cardId));
  }

  @ApiOperation(value = "获取用户信息")
  @GetMapping("/userInfo")
  @ResultLog(name = "CateCardController.currentLoginUserInfo", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<UserInfoResp> currentLoginUserInfo() {
    return ResultVO.success(cateCardService.currentLoginUserInfo());
  }

  @ApiOperation(value = "获取微信二维码生成")
  @PostMapping("/miniapp/qrCode")
  @ResultLog(name = "CateCardController.qrCode", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<MiniAppQrCodeResp> qrCode(@RequestBody MiniAppQrCodeReqDto miniAppQrCodeReqDto) {
    return ResultVO.success(cateCardService.qrCode(miniAppQrCodeReqDto));
  }


  @ApiOperation(value = "app 二维码生成")
  @GetMapping("/app/qrCode")
  @ResultLog(name = "CateCardController.generateAppCode", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<AppQrCodeResp> generateAppCode(@RequestParam("cardId") Long cardId) {
    return ResultVO.success(cateCardService.generateAppCode(cardId));
  }


  @ApiOperation(value = "员工销售纪录")
  @PostMapping("/user/saleRecords")
  @ResultLog(name = "CateCardController.userSaleCardRecords", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<UserSaleCardRecordResp> userSaleCardRecords(@RequestBody UserSaleCardRecordReqDto userSaleCardRecordReq) {
    return ResultVO.success(cateCardService.userSaleCardRecords(userSaleCardRecordReq));
  }

}
