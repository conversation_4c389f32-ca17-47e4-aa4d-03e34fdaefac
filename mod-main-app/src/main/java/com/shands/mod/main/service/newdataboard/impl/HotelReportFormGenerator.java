package com.shands.mod.main.service.newdataboard.impl;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.draw.SolidLine;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.itextpdf.forms.PdfAcroForm;
import com.itextpdf.forms.fields.PdfTextFormField;
import com.itextpdf.forms.fields.PdfFormField;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.io.font.constants.StandardFonts;

import java.io.FileNotFoundException;
import java.io.IOException;

public class HotelReportFormGenerator {
    
    // 定义房型数据结构
    private static class RoomTypeData {
        String roomType;
        boolean isMainCategory;
        
        RoomTypeData(String roomType, boolean isMainCategory) {
            this.roomType = roomType;
            this.isMainCategory = isMainCategory;
        }
    }
    
    // 房型数据
    private static final RoomTypeData[] ROOM_TYPES = {
        new RoomTypeData("名成豪华双床房", true),
        new RoomTypeData("百达屋", false),
        new RoomTypeData("会员价", false),
        new RoomTypeData("百达卡", false),
        new RoomTypeData("OTA", false),
        new RoomTypeData("携程", false),
        new RoomTypeData("美团", false),
        new RoomTypeData("飞猪", false),
        new RoomTypeData("抖音", false),
        new RoomTypeData("协议客户", false),
        new RoomTypeData("企业卡", false),
        new RoomTypeData("缴下", false),
        new RoomTypeData("名成豪华大床房", true),
        new RoomTypeData("百达屋", false),
        new RoomTypeData("会员价", false),
        new RoomTypeData("OTA", false),
        new RoomTypeData("携程", false),
        new RoomTypeData("美团", false),
        new RoomTypeData("飞猪", false),
        new RoomTypeData("抖音", false),
        new RoomTypeData("协议客户", false),
        new RoomTypeData("企业卡", false),
        new RoomTypeData("缴下", false),
        new RoomTypeData("名成豪华大床房", true)
    };
    
    public static void main(String[] args) {
        try {
            createHotelReportForm("hotel_report_form.pdf");
            System.out.println("PDF表单创建成功！");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public static void createHotelReportForm(String dest) throws IOException {
        PdfWriter writer = new PdfWriter(dest);
        PdfDocument pdfDoc = new PdfDocument(writer);
        Document document = new Document(pdfDoc);
        
        // 设置中文字体支持（如果需要的话）
        PdfFont font = PdfFontFactory.createFont(StandardFonts.HELVETICA);
        
        // 创建表单
        PdfAcroForm form = PdfAcroForm.getAcroForm(pdfDoc, true);
        
        // 标题部分
        createTitle(document, form);
        
        // 副标题
        createSubtitle(document, form);
        
        // 创建主表格
        createMainTable(document, form);
        
        document.close();
    }
    
    private static void createTitle(Document document, PdfAcroForm form) throws IOException {
        // 创建标题段落
        Paragraph title = new Paragraph()
            .setTextAlignment(TextAlignment.CENTER)
            .setFontSize(18)
            .setBold()
            .setMarginBottom(10);
        
        // 添加标题文本字段
        Rectangle titleRect = new Rectangle(150, 750, 300, 20);
        PdfTextFormField titleField = PdfFormField.createText(titleRect, "hotelName", "深圳蛇口海上世界开元名庭酒店");
        titleField.setFontSize(18);
        titleField.setJustification(1); // 居中对齐
        form.addField(titleField);
        
        document.add(new Paragraph("\n\n")); // 为标题字段留出空间
    }
    
    private static void createSubtitle(Document document, PdfAcroForm form) throws IOException {
        // 副标题段落
        Paragraph subtitle = new Paragraph()
            .setTextAlignment(TextAlignment.CENTER)
            .setFontSize(12)
            .setMarginBottom(20);
        
        // 年月字段
        Rectangle yearMonthRect = new Rectangle(200, 720, 200, 15);
        PdfTextFormField yearMonthField = PdfFormField.createText(yearMonthRect, "yearMonth", "营运房型实收ADR 2025年4月");
        yearMonthField.setFontSize(12);
        yearMonthField.setJustification(1);
        form.addField(yearMonthField);
        
        document.add(new Paragraph("\n\n")); // 为副标题字段留出空间
    }
    
    private static void createMainTable(Document document, PdfAcroForm form) throws IOException {
        // 创建主表格 - 7列
        Table mainTable = new Table(UnitValue.createPercentArray(new float[]{3, 1.5f, 1.5f, 1.5f, 1.5f, 1.5f, 1.5f}))
            .setWidth(UnitValue.createPercentValue(100))
            .setMarginTop(50);
        
        // 定义蓝色背景
        DeviceRgb blueBackground = new DeviceRgb(54, 96, 146);
        DeviceRgb yellowBackground = new DeviceRgb(255, 255, 0);
        
        // 表头
        String[] headers = {"房型&渠道", "综合门市价", "已售间夜", "房费", "ADR", "实际间夜数", "ADR"};
        for (String header : headers) {
            Cell headerCell = new Cell()
                .add(new Paragraph(header).setFontColor(ColorConstants.WHITE).setBold())
                .setBackgroundColor(blueBackground)
                .setTextAlignment(TextAlignment.CENTER)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setPadding(8);
            mainTable.addHeaderCell(headerCell);
        }
        
        // 添加数据行
        int fieldIndex = 0;
        for (int i = 0; i < ROOM_TYPES.length; i++) {
            RoomTypeData roomType = ROOM_TYPES[i];
            
            // 房型名称列
            Cell roomTypeCell = new Cell()
                .add(new Paragraph(roomType.roomType))
                .setTextAlignment(TextAlignment.LEFT)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setPadding(5);
            
            if (roomType.isMainCategory) {
                roomTypeCell.setBackgroundColor(yellowBackground);
            }
            
            mainTable.addCell(roomTypeCell);
            
            // 数据列 - 创建可填写字段
            for (int j = 0; j < 6; j++) {
                Cell dataCell = new Cell()
                    .setTextAlignment(TextAlignment.CENTER)
                    .setVerticalAlignment(VerticalAlignment.MIDDLE)
                    .setPadding(5);
                
                if (roomType.isMainCategory) {
                    dataCell.setBackgroundColor(yellowBackground);
                }
                
                // 创建表单字段
                String fieldName = String.format("data_%d_%d", i, j);
                Rectangle fieldRect = new Rectangle(0, 0, 60, 15); // 这个位置会被自动调整
                PdfTextFormField dataField = PdfFormField.createText(fieldRect, fieldName, "");
                dataField.setFontSize(10);
                dataField.setJustification(1); // 居中对齐
                
                // 根据列类型设置默认值
                String defaultValue = getDefaultValue(i, j, roomType.isMainCategory);
                if (!defaultValue.isEmpty()) {
                    dataField.setValue(defaultValue);
                }
                
                form.addField(dataField);
                
                mainTable.addCell(dataCell);
                fieldIndex++;
            }
        }
        
        document.add(mainTable);
        
        // 添加说明文字
        Paragraph note = new Paragraph("注：所有数据字段均为可编辑表单字段，可以直接在PDF中填写或通过程序动态填充")
            .setFontSize(10)
            .setMarginTop(20)
            .setTextAlignment(TextAlignment.LEFT);
        document.add(note);
    }
    
    private static String getDefaultValue(int rowIndex, int colIndex, boolean isMainCategory) {
        // 为主要房型类别提供一些示例数据
        if (isMainCategory) {
            switch (colIndex) {
                case 0: return "488"; // 综合门市价
                case 1: return "116"; // 已售间夜
                case 2: return "53,384"; // 房费
                case 3: return "464"; // ADR
                case 4: return "51,225"; // 实际间夜数
                case 5: return "445"; // ADR
            }
        }
        return ""; // 其他行默认为空
    }
    
    // 填充表单数据的方法示例
    public static void fillFormData(String pdfPath, String outputPath, HotelReportData data) throws IOException {
        PdfDocument pdfDoc = new PdfDocument(new PdfReader(pdfPath), new PdfWriter(outputPath));
        PdfAcroForm form = PdfAcroForm.getAcroForm(pdfDoc, false);
        
        // 填充标题和日期
        form.getField("hotelName").setValue(data.hotelName);
        form.getField("yearMonth").setValue(data.yearMonth);
        
        // 填充数据
        for (int i = 0; i < data.roomTypeData.length; i++) {
            RoomTypeReportData roomData = data.roomTypeData[i];
            if (roomData != null) {
                for (int j = 0; j < 6; j++) {
                    String fieldName = String.format("data_%d_%d", i, j);
                    String value = getRoomDataValue(roomData, j);
                    if (form.getField(fieldName) != null) {
                        form.getField(fieldName).setValue(value);
                    }
                }
            }
        }
        
        // 可选：平铺表单（使其不可编辑）
        // form.flattenFields();
        
        pdfDoc.close();
    }
    
    private static String getRoomDataValue(RoomTypeReportData roomData, int columnIndex) {
        switch (columnIndex) {
            case 0: return String.valueOf(roomData.marketPrice);
            case 1: return String.valueOf(roomData.soldRooms);
            case 2: return String.valueOf(roomData.roomRevenue);
            case 3: return String.valueOf(roomData.adr1);
            case 4: return String.valueOf(roomData.actualRooms);
            case 5: return String.valueOf(roomData.adr2);
            default: return "";
        }
    }
    
    // 数据结构类
    public static class HotelReportData {
        public String hotelName;
        public String yearMonth;
        public RoomTypeReportData[] roomTypeData;
        
        public HotelReportData(String hotelName, String yearMonth, int roomTypeCount) {
            this.hotelName = hotelName;
            this.yearMonth = yearMonth;
            this.roomTypeData = new RoomTypeReportData[roomTypeCount];
        }
    }
    
    public static class RoomTypeReportData {
        public int marketPrice;
        public int soldRooms;
        public String roomRevenue;
        public int adr1;
        public String actualRooms;
        public int adr2;
        
        public RoomTypeReportData(int marketPrice, int soldRooms, String roomRevenue, 
                                 int adr1, String actualRooms, int adr2) {
            this.marketPrice = marketPrice;
            this.soldRooms = soldRooms;
            this.roomRevenue = roomRevenue;
            this.adr1 = adr1;
            this.actualRooms = actualRooms;
            this.adr2 = adr2;
        }
    }
}