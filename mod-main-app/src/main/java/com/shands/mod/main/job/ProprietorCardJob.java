package com.shands.mod.main.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.betterwood.base.common.model.Result;
import com.shands.mod.dao.mapper.proprietor.ModProprietorCardSendRecordMapper;
import com.shands.mod.dao.model.proprietor.ModProprietorCardSendRecord;
import com.shands.mod.dao.model.proprietor.ProprietorCardRecycleQo;
import com.shands.mod.dao.model.proprietor.ProprietorCardSendQo;
import com.shands.mod.main.config.ProprietorConfig;
import com.shands.mod.main.remote.cms.RemoteBdwCmsCateCardService;
import com.shands.mod.main.service.proprietor.ProprietorService;
import com.shands.mod.util.BaseConstants;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 业主卡发放定时任务
 */
@Component
@Slf4j
public class ProprietorCardJob {

    @Autowired
    private ProprietorService proprietorService;

    @Autowired
    private RemoteBdwCmsCateCardService remoteBdwCmsCateCardService;

    @Autowired
    private ModProprietorCardSendRecordMapper proprietorCardSendRecordMapper;

    @Autowired
    private ProprietorConfig proprietorConfig;

    /**
     * 业主卡发放定时任务
     * 1. 获取所有业主手机号
     * 2. 获取已发放成功的业主卡记录
     * 3. 对比两个列表：
     *    - 如果业主手机号在表里没有发放成功的记录，调用sendCard方法发卡
     *    - 如果表里存在发放成功记录的手机号不在所有业主手机号里，调用recycleCard回收卡
     * 4. 更新业主卡发放记录表
     */
    @XxlJob("sendProprietorCard")
    public ReturnT<String> sendProprietorCard(String param) {
        log.info("【业主卡发放定时任务开始执行】时间: {}", DateUtil.now());
        log.info("【业主卡发放定时任务参数】: {}", param);
        try {
            // 需要发卡的手机号
            Set<String> mobilesToSend = new HashSet<>();
            Set<String> mobilesToRecycle = new HashSet<>();
            if (StrUtil.isNotBlank(param)) {
                Map paramMap = JSONObject.parseObject(param, Map.class);
                if (null != paramMap.get("mobilesToSend")) {
                    String[] mobilesToSendArr = ((String) paramMap.get("mobilesToSend")).split(",");
                    mobilesToSend.addAll(Arrays.asList(mobilesToSendArr));
                }
                if (null != paramMap.get("mobilesToRecycle")) {
                    String[] mobilesToRecyclArr = ((String) paramMap.get("mobilesToRecycle")).split(",");
                    mobilesToRecycle.addAll(Arrays.asList(mobilesToRecyclArr));
                }
            } else {
                // 1. 获取所有业主手机号
                Set<String> allProprietorMobiles = getAllMobiles();
                log.info("【业主卡发放】获取到业主手机号数量: {}", allProprietorMobiles.size());

                if (CollUtil.isEmpty(allProprietorMobiles)) {
                    log.warn("【业主卡发放】未获取到业主手机号，任务结束");
                    return ReturnT.SUCCESS;
                }

                // 2. 获取已发放成功的业主卡记录
                List<ModProprietorCardSendRecord> successRecords = proprietorCardSendRecordMapper.selectAllSuccess();
                log.info("【业主卡发放】已发放成功的业主卡记录数量: {}", successRecords.size());

                // 提取已发放成功的手机号集合
                Set<String> successMobiles = successRecords.stream()
                        .map(ModProprietorCardSendRecord::getMobile)
                        .collect(Collectors.toSet());

                // 3. 对比两个列表
                // 需要发卡的手机号（业主手机号中不在已发放记录中的）
                mobilesToSend = new HashSet<>(allProprietorMobiles);
                mobilesToSend.removeAll(successMobiles);

                // 需要回收的手机号（已发放记录中不在业主手机号中的）
                mobilesToRecycle = new HashSet<>(successMobiles);
                mobilesToRecycle.removeAll(allProprietorMobiles);
            }



            log.info("【业主卡发放】需要发卡的手机号: {}, 需要回收的手机: {}", mobilesToSend, mobilesToRecycle);

            // 获取业主卡ID
            Integer cardId = proprietorConfig.getCardId();
            if (cardId == null || cardId == 0) {
              log.error("【业主卡发放】业主卡ID未配置");
              return new ReturnT<>(ReturnT.FAIL_CODE, "业主卡ID未配置");
            }

            // 发卡处理
            for (String mobile : mobilesToSend) {
                // 调用发卡接口
                ProprietorCardSendQo sendQo = new ProprietorCardSendQo();
                sendQo.setMobile(mobile);
                sendQo.setAreaCode(BaseConstants.DEFAULT_AREA_CODE);
                sendQo.setCardId(cardId);

                ModProprietorCardSendRecord record = ModProprietorCardSendRecord.builder()
                    .mobile(mobile)
                    .areaCode(BaseConstants.DEFAULT_AREA_CODE)
                    .cardId(cardId)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .build();

                Result<String> sendResult = remoteBdwCmsCateCardService.sendCard(sendQo);
                if (sendResult != null && sendResult.isOk()) {
                  record.setSendRes(1);
                } else {
                  record.setSendRes(0);
                }

                proprietorCardSendRecordMapper.insert(record);

                log.info("【业主卡发放】手机号: {}, 发卡结果: {}", mobile, sendResult);
            }

            // 回收卡处理
            for (String mobile : mobilesToRecycle) {
                // 调用回收卡接口
                ProprietorCardRecycleQo recycleQo = new ProprietorCardRecycleQo();
                recycleQo.setMobile(mobile);
                recycleQo.setAreaCode(BaseConstants.DEFAULT_AREA_CODE);

                Result<String> recycleResult = remoteBdwCmsCateCardService.recycleCard(recycleQo);

                // 更新发卡记录
                if (recycleResult != null && recycleResult.isOk()) {
                  proprietorCardSendRecordMapper.deleteByMobile(mobile);
                }

                log.info("【业主卡回收】手机号: {}, 回收卡结果: {}", mobile, recycleResult);
            }

            log.info("【业主卡发放定时任务执行完成】时间: {}", DateUtil.now());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("【业主卡发放定时任务执行异常】", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "业主卡发放定时任务执行异常：" + e.getMessage());
        }
    }

    /**
     * 获取所有业主手机号
     *
     */
    private Set<String> getAllMobiles() {
        // 从nacos配置中获取业主手机号
        Set<String> configProprietorMobiles = proprietorService.getConfigProprietorMobiles();

        // 从UC中获取业主手机号
        // 使用岗位ID列表查询所有相关的业主手机号
        Set<String> ucProprietorMobiles = proprietorService.getUcProprietorMobiles(proprietorConfig.getPostId());

        // 合并两个集合
        Set<String> allProprietorMobiles = new HashSet<>();
        allProprietorMobiles.addAll(configProprietorMobiles);
        allProprietorMobiles.addAll(ucProprietorMobiles);

        return allProprietorMobiles;
    }


}
