package com.shands.mod.main.service.betterwood.rpc.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @author：joker.liu
 * @date: 2023/12/19 11:44
 */
@Data
public class HotelSetRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 酒店码
     */
    @ApiModelProperty(value = "酒店码")
    private String hotelCode;
    /**
     * 是否关房 T是F关
     */
    @ApiModelProperty(value = "是否关房 T是F关")
    private String isClosed;
    /**
     * 最低限价
     */
    @ApiModelProperty(value = "最低限价")
    private BigDecimal lowPrice;
    @ApiModelProperty(value = "是否开启最低限价，T开启，F关闭")
    private String lowPriceSet;
    @ApiModelProperty(value = "房价码白名单")
    private String whiteRateCodes;
    /**
     * 创建用户
     */
    @ApiModelProperty(value = "创建用户")
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 更新用户
     */
    @ApiModelProperty(value = "更新用户")
    private String updateUser;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    @ApiModelProperty(value = "关房开始时间")
    private LocalDate closedStart;
    @ApiModelProperty(value = "关房结束时间")
    private LocalDate closedEnd;

}
