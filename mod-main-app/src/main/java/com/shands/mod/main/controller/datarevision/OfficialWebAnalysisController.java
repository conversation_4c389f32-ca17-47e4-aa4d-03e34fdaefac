package com.shands.mod.main.controller.datarevision;

import com.shands.mod.dao.model.datarevision.bo.MemberAnalysisBo;
import com.shands.mod.dao.model.datarevision.vo.GeneralDrawingDetailsVo;
import com.shands.mod.main.service.datarevision.OfficialWebAnalysisService;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/31
 **/
@RestController
@Slf4j
@Api(value = "officialWebAnalysis", tags = "官网分析")
@RequestMapping("/officialWebAnalysis")
public class OfficialWebAnalysisController {

  private final OfficialWebAnalysisService officialWebAnalysisService;

  @Autowired
  public OfficialWebAnalysisController(
      OfficialWebAnalysisService officialWebAnalysisService) {
    this.officialWebAnalysisService = officialWebAnalysisService;
  }

  @PostMapping("/officialWebTrafficDistribution")
  @ApiOperation("百达星系APP流量分析")
  @ApiResponses({
      @ApiResponse(code = 0, message = "成功", response = GeneralDrawingDetailsVo.class)
  })
  public ResultVO<List<GeneralDrawingDetailsVo>> officialWebTrafficDistribution(@Valid @RequestBody MemberAnalysisBo memberAnalysisBo){
    return ResultVO.success(officialWebAnalysisService.officialWebTrafficDistribution(memberAnalysisBo));
  }

  @PostMapping("/officialNightTrafficDistribution")
  @ApiOperation("官渠间夜量（入住维度）")
  @ApiResponses({
      @ApiResponse(code = 0, message = "成功", response = GeneralDrawingDetailsVo.class)
  })
  public ResultVO<List<GeneralDrawingDetailsVo>> officialNightTrafficDistribution(@Valid @RequestBody MemberAnalysisBo memberAnalysisBo){
    return ResultVO.success(officialWebAnalysisService.officialNightTrafficDistribution(memberAnalysisBo));
  }

  @PostMapping("/officialNightTrafficProportion")
  @ApiOperation("官渠间夜占比（入住维度）")
  @ApiResponses({
      @ApiResponse(code = 0, message = "成功", response = GeneralDrawingDetailsVo.class)
  })
  public ResultVO<List<GeneralDrawingDetailsVo>> officialNightTrafficProportion(@Valid @RequestBody MemberAnalysisBo memberAnalysisBo){
    return ResultVO.success(officialWebAnalysisService.officialNightTrafficProportion(memberAnalysisBo));
  }

  @PostMapping("/bdwOrderSourceTrend")
  @ApiOperation("百达星系订单来源趋势")
  @ApiResponses({
      @ApiResponse(code = 0, message = "成功", response = GeneralDrawingDetailsVo.class)
  })
  public ResultVO<List<GeneralDrawingDetailsVo>> bdwOrderSourceTrend(@Valid @RequestBody MemberAnalysisBo memberAnalysisBo){
    return ResultVO.success(officialWebAnalysisService.bdwOrderSourceTrend(memberAnalysisBo));
  }

  @PostMapping("/bdwOrderSourceTrendProportion")
  @ApiOperation("百达星系APP订单来源占比趋势")
  @ApiResponses({
      @ApiResponse(code = 0, message = "成功", response = GeneralDrawingDetailsVo.class)
  })
  public ResultVO<List<GeneralDrawingDetailsVo>> bdwOrderSourceTrendProportion(@Valid @RequestBody MemberAnalysisBo memberAnalysisBo){
    return ResultVO.success(officialWebAnalysisService.bdwOrderSourceTrendProportion(memberAnalysisBo));
  }
}