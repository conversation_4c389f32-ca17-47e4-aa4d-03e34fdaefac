package com.shands.mod.main.service.app.impl;

import cn.hutool.system.UserInfo;
import com.shands.mod.dao.mapper.UserMapper;
import com.shands.mod.dao.model.User;
import com.shands.mod.external.model.dto.UcAuthenticationDto;
import com.shands.mod.external.service.UcAuthenticationService;
import com.shands.mod.main.service.app.UcUserService;
import com.shands.mod.main.util.HttpClientUtil;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.JwtUtils;
import com.shands.mod.util.Tools;
import com.shands.mod.vo.ResultVO;
import com.shands.mod.vo.UserInfoVO;
import com.shands.uc.base.util.JwtUtil;
import com.shands.uc.model.auth.UserLoginRes;
import com.shands.uc.model.req.v3.FindUserInfoByMobileReq;
import com.shands.uc.model.req.v3.PublicLoginReq;
import com.shands.uc.model.req.v3.auth.UserAuthInfo;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/8/10
 * @desc 用户通宝信息处理service
 */

@Service
@Slf4j
public class UcUserServiceImpl implements UcUserService {

  @Value("${uc.sercret:380583f66a4c462ba62ca690c231be9f}")
  private String ucSercret;

  @Value("${uc.appId:mod3}")
  private String ucAppId;

  @Value("${uc.domain:http://test-uc3.kaiyuanhotels.com}")
  private String domain;

  @Value("${uc.ucUserName:admin}")
  private String ucUserName;

  @Value("${uc.ucPassword:123456}")
  private String ucPassword;

  @Value("${pms-config.pmsApiUrl}")
  private String pmsApiUrl;

  private final UcAuthenticationService ucAuthenticationService;

  @Resource
  private UserMapper userMapper;

  @Resource
  private RedisTemplate redisTemplate;

  private final long EXPIRATION = 259200;


  public UcUserServiceImpl(
      UcAuthenticationService ucAuthenticationService) {
    this.ucAuthenticationService = ucAuthenticationService;
  }

  @Override
  public UserAuthInfo ucLoginByUsername(String userName) {

    //查询用户通宝信息
    UcAuthenticationDto ucAuthenticationDto = new UcAuthenticationDto();
    ucAuthenticationDto.setAppId(ucAppId);
    ucAuthenticationDto.setSecret(ucSercret);
    ucAuthenticationDto.setDomain(domain);

    PublicLoginReq publicLoginReq = new PublicLoginReq();
    publicLoginReq.setAppId(ucAppId);
    publicLoginReq.setUsername(ucUserName);
    publicLoginReq.setPassword(ucPassword);

    //调用通宝接口 换取token信息
    UserLoginRes userLoginRes = ucAuthenticationService.
        loginByUserName(ucAuthenticationDto,publicLoginReq);

    if(userLoginRes == null ||
        !org.springframework.util.StringUtils.hasText(userLoginRes.getToken())){
      throw new RuntimeException("uc接口鉴权信息获取失败");
    }

    //设置token请求头
    ucAuthenticationDto.setToken(userLoginRes.getToken());

    FindUserInfoByMobileReq findUserInfoByMobileReq = new FindUserInfoByMobileReq();
    findUserInfoByMobileReq.setAppId(ucAppId);
    findUserInfoByMobileReq.setMobile(userName);

    //查询用户通宝信息
    UserAuthInfo userAuthInfo = ucAuthenticationService
        .loginByMobile(ucAuthenticationDto,findUserInfoByMobileReq);

    if(userAuthInfo == null){
      throw new RuntimeException("uc用户信息查询为空");
    }

    //解析通宝用户信息
    if(userAuthInfo.getRoles() == null || userAuthInfo.getRoles().isEmpty()){
      throw new RuntimeException("无权限");
    }

    return userAuthInfo;
  }

  @Override
  public String loginByPhoneAndId(String mobile, Integer ucId) {
    PublicLoginReq publicLoginReq = new PublicLoginReq();
    publicLoginReq.setAppId(ucAppId);
    publicLoginReq.setUsername(mobile);
    publicLoginReq.setPassword("JWT:" + JwtUtils.generateToken(ucId.toString(), JwtUtil.JWT_SECRET, 43200));
    return this.login(publicLoginReq);
  }

  @Override
  public String loginByMobileAndPassword(String mobile, String password) {
    PublicLoginReq publicLoginReq = new PublicLoginReq();
    publicLoginReq.setAppId(ucAppId);
    publicLoginReq.setUsername(mobile);
    publicLoginReq.setPassword("MD5:" + password);
    return this.login(publicLoginReq);
  }

  private String login(PublicLoginReq publicLoginReq) {
    //查询用户通宝信息
    UcAuthenticationDto ucAuthenticationDto = new UcAuthenticationDto();
    ucAuthenticationDto.setAppId(ucAppId);
    ucAuthenticationDto.setSecret(ucSercret);
    ucAuthenticationDto.setDomain(domain);

    //调用通宝接口 换取token信息
    UserLoginRes userLoginRes = ucAuthenticationService.
        loginByUserName(ucAuthenticationDto,publicLoginReq);


    return userLoginRes.getToken();
  }

  @Override
  public ResultVO scanQrLoginUc(Integer userId, String uid) {

    User user = userMapper.selectByPrimaryKey(userId);

    if (user == null) {
      return ResultVO.failed("未获取到用户信息");
    }
    if (user.getUcId() == null || user.getUcId().intValue()<=0) {
      return ResultVO.failed("[" + userId + "]无效通宝用户");
    }

    //扫码时静默登录通宝获取用户token
    String token = this.loginByPhoneAndId(user.getMobile(), user.getUcId());

    if (StringUtils.isBlank(token)){
      return ResultVO.failed("获取通宝用户token异常");
    }

    UcAuthenticationDto ucAuthenticationDto = new UcAuthenticationDto();
    ucAuthenticationDto.setAppId(ucAppId);
    ucAuthenticationDto.setSecret(ucSercret);
    ucAuthenticationDto.setDomain(domain + "/v3/public/loginQrExpire");

    boolean b;
    try {

      b = ucAuthenticationService.loginQrExpire(ucAuthenticationDto, uid);

      if (b){
        ucAuthenticationDto.setDomain(domain + "/v3/public/authorToQr");

        ucAuthenticationDto.setToken(token);
        b = ucAuthenticationService.scanQrLoginUc(ucAuthenticationDto, uid);

      }else {

        return ResultVO.failed("二维码已过期，请刷新重试");
      }

    } catch (Exception e) {
      log.error("[app扫码登录通宝异常]: ", e);
      return ResultVO.failed(e.getMessage());
    }

    if (b) {

      return ResultVO.success();
    }else {

      return ResultVO.failed("扫码登录通宝失败");
    }
  }

  @Override
  public ResultVO scanQrLoginPms(Integer userId, Map<String,String> params) {
    String uid = params.get("uid");
    String qrType = params.get("qrType");
    String ucId = params.get("ucId");
    String xPmsSource = params.getOrDefault("pmsSource", "pms");

    User user = userMapper.selectByPrimaryKey(userId);
    if (user == null) {
      return ResultVO.failed("未获取到用户信息");
    }
    if (user.getUcId() == null || user.getUcId().intValue()<=0) {
      return ResultVO.failed("[" + userId + "]无效通宝用户");
    }
    if("PMS_UNLOCK_LOGIN".equals(qrType)) {
      if(StringUtils.isBlank(ucId) || !ucId.equals(user.getUcId().toString())) {
        return ResultVO.failed("用户不一致");
      }
    }
    String url = pmsApiUrl+"/authorToQr";
    //扫码时静默登录通宝获取用户token
    String token = this.loginByPhoneAndId(user.getMobile(), user.getUcId());
    if (StringUtils.isBlank(token)){
      return ResultVO.failed("获取通宝用户token异常");
    }
    Map maps;
    try {
      Map<String, Object> body = new HashMap<>();
      body.put("uid", uid);
      if ("PMS_UNLOCK_LOGIN".equals(qrType)) {
        body.put("unlock", true);
      }
      //请求头
      Map<String, String> map = new HashMap<>();
      map.put("uc-token",token);
      map.put("x-pms-source", xPmsSource);
      //请求消息
      maps = HttpClientUtil.doJsonPost(url, map, body).toJavaObject(Map.class);
    }catch (Exception e){
      log.error("[app扫码登录异常] ", e);
      return ResultVO.failed(e.getMessage());
    }
    if (maps.get("code").equals(1)){
      return ResultVO.success();
    }
    return ResultVO.failed("扫码登录失败");
  }

  @Override
  public List getUcUserRoles(String ucToken, String ucAppId) {

    Assert.hasText(ucToken, "通宝ucToken不能为空");
    Assert.hasText(ucAppId, "通宝ucAppId不能为空");

    //通宝调用基础请求参数
    UcAuthenticationDto ucAuthenticationDto = new UcAuthenticationDto();
    ucAuthenticationDto.setAppId(ucAppId);
    ucAuthenticationDto.setSecret(ucSercret);
    ucAuthenticationDto.setDomain(domain);
    ucAuthenticationDto.setToken(ucToken);

    PublicLoginReq findUserInfoByMobileReq = new PublicLoginReq();
    findUserInfoByMobileReq.setAppId(ucAppId);

    UserAuthInfo userAuthInfo = ucAuthenticationService.loginByToken(ucAuthenticationDto,findUserInfoByMobileReq);

    if (userAuthInfo == null){
      throw new RuntimeException("未获取到通宝用户信息");
    }

    return userAuthInfo.getRoles();
  }
}
