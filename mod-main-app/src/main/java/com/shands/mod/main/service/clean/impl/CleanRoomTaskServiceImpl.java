package com.shands.mod.main.service.clean.impl;

import static java.util.stream.Collectors.groupingBy;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.mapper.UserMapper;
import com.shands.mod.dao.mapper.hs.cleanRoom.HsCleanRoomCheckMapper;
import com.shands.mod.dao.mapper.hs.cleanRoom.HsCleanRoomCheckUserMapper;
import com.shands.mod.dao.mapper.hs.cleanRoom.HsCleanRoomConfigMapper;
import com.shands.mod.dao.mapper.hs.cleanRoom.HsCleanRoomTaskMapper;
import com.shands.mod.dao.mapper.hs.plan.HsPlanSanitateTaskMapper;
import com.shands.mod.dao.mapper.syncuc.ModDeptDao;
import com.shands.mod.dao.model.enums.CleanStatusEnum;
import com.shands.mod.dao.model.enums.CleanTypeEnum;
import com.shands.mod.dao.model.enums.CloseReasonEnum;
import com.shands.mod.dao.model.enums.RoomStatusEnum;
import com.shands.mod.dao.model.hs.cleanRoom.HsCleanRoomCheckUser;
import com.shands.mod.dao.model.hs.cleanRoom.HsCleanRoomConfig;
import com.shands.mod.dao.model.hs.cleanRoom.HsCleanRoomTask;
import com.shands.mod.dao.model.hs.plan.HsPlanSanitateTask;
import com.shands.mod.dao.model.req.clean.AllotCleanUserReq;
import com.shands.mod.dao.model.req.clean.CleanRoomListReq;
import com.shands.mod.dao.model.req.clean.CleanUserRoomReq;
import com.shands.mod.dao.model.req.clean.DetailByCleanUserReq;
import com.shands.mod.dao.model.res.clean.AllUserAndTaskRes;
import com.shands.mod.dao.model.res.clean.Rooms;
import com.shands.mod.dao.model.res.clean.CheckByTaskIdRes;
import com.shands.mod.dao.model.res.clean.CheckRoomRes;
import com.shands.mod.dao.model.res.clean.CleanUserStatisticsRes;
import com.shands.mod.dao.model.res.clean.CleanUsersRes;
import com.shands.mod.dao.model.res.clean.DetailByCleanUserRes;
import com.shands.mod.dao.model.res.clean.RoomBuildsListVo;
import com.shands.mod.dao.model.res.clean.RoomDetailVo;
import com.shands.mod.dao.model.res.syncuc.ModDeptLastRes;
import com.shands.mod.dao.model.syncuc.ModDept;
import com.shands.mod.dao.model.v0701.dto.RoomStatusDto;
import com.shands.mod.dao.model.v0701.vo.RoomInfoVo;
import com.shands.mod.main.service.clean.ICleanRoomServie;
import com.shands.mod.main.service.clean.ICleanRoomTaskService;
import com.shands.mod.main.service.crs.ICrsRoomStatusService;
import com.shands.mod.main.service.order.OrderCommonService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.main.util.UUIDUtils;
import com.shands.mod.main.util.hs.DateUtils;
import com.shands.mod.vo.ResultVO;
import java.text.Collator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class CleanRoomTaskServiceImpl implements ICleanRoomTaskService {

  @Resource
  private HsCleanRoomTaskMapper cleanRoomTaskMapper;
  @Resource
  private HsPlanSanitateTaskMapper hsPlanSanitateTaskMapper;

  @Resource
  private HsCleanRoomCheckMapper cleanRoomCheckMapper;

  @Resource
  private HsCleanRoomConfigMapper configMapper;

  @Resource
  private ModDeptDao deptDao;

  @Resource
  private UserMapper userMapper;

  @Resource
  private ICleanRoomServie roomServie;

  @Resource
  private HsCleanRoomCheckUserMapper checkUserMapper;

  @Resource
  private OrderCommonService orderCommonService;

  @Resource
  private ICrsRoomStatusService roomStatusService;


  @Override
  public ResultVO cleanRes(Integer companyId, String dateTime) {
    List<CleanUserStatisticsRes> userStatisticsRes = new ArrayList<>();
    List<HsCleanRoomTask> cleanUserRes = cleanRoomTaskMapper.cleanUser(companyId, dateTime);
    Map<Integer, List<HsCleanRoomTask>> listMap = cleanUserRes.stream()
        .collect(Collectors
            .groupingBy(HsCleanRoomTask::getCleanUser, LinkedHashMap::new, Collectors.toList()));
    Set<Integer> cleanUser = listMap.keySet();
    cleanUser.forEach(key -> {
      CleanUserStatisticsRes res = new CleanUserStatisticsRes();
      List<HsCleanRoomTask> task = listMap.get(key);
      res.setCleanUser(key);
      res.setRoomNo(task.get(0).getCleanUserName());
      res.setDateTime(dateTime);
      //分配了多少间房
      res.setRoomType("已分配" + task.size() + "间");

      Map<Integer, List<HsCleanRoomTask>> cleanStatus = task.stream()
          .collect(Collectors.groupingBy(HsCleanRoomTask::getCleanStatus));
      //已清扫间数包含待检查和已完成的
      List<HsCleanRoomTask> wait =
          cleanStatus.get(CleanStatusEnum.WAITING_CHECK.getCode()) != null ? cleanStatus
              .get(CleanStatusEnum.WAITING_CHECK.getCode()) : new ArrayList<>();
      //检查通过
      List<HsCleanRoomTask> checkSucces =
          cleanStatus.get(CleanStatusEnum.COMPLETED.getCode()) != null ? cleanStatus
              .get(CleanStatusEnum.COMPLETED.getCode()) : new ArrayList<>();

      //检查通过
      List<HsCleanRoomTask> startCheck =
          cleanStatus.get(CleanStatusEnum.START_CHECK.getCode()) != null ? cleanStatus
              .get(CleanStatusEnum.START_CHECK.getCode()) : new ArrayList<>();

      int cleanNum = wait.size() + checkSucces.size() + startCheck.size();
      res.setCleanStatus("已清洁" + cleanNum + "间");

      //检查完成间数
      res.setCheckResult("已查" + checkSucces.size() + "间");
      res.setId(UUIDUtils.getUUID());
      userStatisticsRes.add(res);
    });

    return ResultVO.success(userStatisticsRes);
  }

  @Override
  public ResultVO detailByCleanUser(DetailByCleanUserReq req) {
    List<DetailByCleanUserRes> res = cleanRoomTaskMapper.detailByCleanUser(req);
    res.forEach(x -> {
      List<CheckByTaskIdRes> checkByTaskId = cleanRoomCheckMapper
          .checkByTaskId(req.getCompanyId(), x.getTaskId());
      List<String> s = checkByTaskId.stream().map(CheckByTaskIdRes::getCleanTime)
          .collect(Collectors.toList());
      x.setCleanTime(String.join(",返工", s));
      List<String> checkResult = new ArrayList<>();
      for (CheckByTaskIdRes r : checkByTaskId) {
        //当清扫状态为完成时，检查为：时间+ ”无法检查/检查通过“+checkResult
        if (CleanStatusEnum.COMPLETED.getCode().equals(x.getCleanStatus())) {
          if (StringUtils.hasText(x.getNotCheck())) {
            if (x.getNotCheck().equals(r.getCheckResult())) {
              checkResult.add(r.getCheckTime() + " " + r.getCheckResult());
            } else {
              checkResult.add(r.getCheckTime() + " " + x.getNotCheck() + "," + r.getCheckResult());
            }
          } else {
            if ("检查通过".equals(r.getCheckResult())) {
              checkResult.add(r.getCheckTime() + " " + r.getCheckResult());
            } else if("退回返工".equals(r.getCheckResult())){
              checkResult.add(r.getCheckTime() + " " + r.getCheckResult());
            }else{
              checkResult.add(r.getCheckTime() + " " + "检查通过," + r.getCheckResult());
            }
          }
        } else {
          if (StringUtils.hasText(r.getCheckTime())) {
            if (StringUtils.hasText(r.getCheckResult())) {
              checkResult.add(r.getCheckTime() + " " + r.getCheckResult());
            } else {
              checkResult.add(r.getCheckTime());
            }
          }
        }
      }
      x.setCleanUser(req.getCleanUser());
      x.setCheckResult(String.join(";", checkResult));
      x.setId(UUIDUtils.getUUID());
    });
    return ResultVO.success(res);
  }

  @Override
  public ResultVO deptAndUseer(Integer companyId) {
    String dept = configMapper.getDept(companyId);
    List<ModDeptLastRes> res = new ArrayList<>();
    if (org.apache.commons.lang.StringUtils.isEmpty(dept)) {
      return ResultVO.success(res);
    }
    List<String> stringList = Arrays.asList(dept.split(","));
    for (String s : stringList) {
      ModDeptLastRes lastRes = new ModDeptLastRes();
      ModDept modDept = deptDao.queryById(Integer.valueOf(s));
      lastRes.setId(modDept.getId());
      lastRes.setName(modDept.getName());
      lastRes.setUsers(userMapper.selectByUser(companyId, null, null, Integer.valueOf(s),null));
      res.add(lastRes);
    }
    return ResultVO.success(res);
  }

  @Override
  public ResultVO roomListNew(CleanRoomListReq req) {
    log.info("请求房间列表 请求参数："+  JSONObject.toJSONString(req));
    Long begin = System.currentTimeMillis();
    Integer companyId = req.getCompanyId();
    int status = req.getChooseStatus() == null ? 1 : req.getChooseStatus();
    Assert.notNull(companyId, "酒店id不能为空");
    Assert.hasText(req.getType(), "类型不能为空");

    HsCleanRoomConfig cleanRoomConfig = configMapper.queryByCompanyId(companyId);
    Assert.notNull(cleanRoomConfig, "请先在后台管理进行做房配置");

    List<RoomBuildsListVo> roomListVo = new ArrayList<>();
    //酒店房间列表
    List<RoomDetailVo> allRoomList = new ArrayList<>();
    int size = 0;
    try {
      if ("CLEAN".equals(req.getType())) {

        allRoomList = roomServie.getRequiredData(3, companyId);

        allRoomList = allRoomList.stream().filter(t -> t.getSta() != null && !"".equals(t.getSta()))
            .collect(Collectors.toList());

        if (status == 2) {
          allRoomList = allRoomList.stream().filter(t -> t.getCleanUser() != null)
              .collect(Collectors.toList());
        } else if (status == 3) {
          //未分配做房清扫任务 过滤非脏房
          allRoomList = allRoomList.stream().filter(
              t -> t.getCleanUser() == null || (t.getCloseReason() != null
                  && t.getCloseReason() == 1))
              .collect(Collectors.toList());
        }
        //计算脏房有多少
        size = allRoomList.stream()
            .filter(t -> RoomStatusEnum.FREE_DIRTY.getRoomStatu().equals(t.getSta())
                || RoomStatusEnum.BUSY_DIRTY.getRoomStatu().equals(t.getSta()))
            .collect(Collectors.toList()).size() + size;
        roomListVo = roomServie.formatRoomsData(allRoomList, size, req.getType());
        log.info("请求清洁人房间列表 耗时：" + (System.currentTimeMillis()-begin));
        return ResultVO.success(roomListVo);
      } else if ("CHECK".equals(req.getType())) {
        //获取房间列表所需数据
        List<CheckRoomRes> allRoomAndUser = checkUserMapper
            .allRoomAndUser(companyId, req.getDate());
        List<CheckRoomRes> buildings = new ArrayList<>();
        //多次分组 拼接返回参数格式
        Map<String, Map<String, List<CheckRoomRes>>> buildingMap = allRoomAndUser
            .stream().collect(groupingBy(CheckRoomRes::getBuildingCode,
                groupingBy(CheckRoomRes::getFloorCode, TreeMap::new, Collectors.toList())));

        buildingMap.entrySet().stream().forEach(x -> {
          String uuid = UUIDUtils.getUUID();
          CheckRoomRes building = new CheckRoomRes();
          List<CheckRoomRes> floors = new ArrayList<>();
          x.getValue().entrySet().stream().forEach(y -> {
            List<CheckRoomRes> rooms = y.getValue();
            List<CheckRoomRes> resRooms = rooms.stream().
                sorted(Comparator.comparing(CheckRoomRes::getRoomNumber))
                .collect(Collectors.toList());
            CheckRoomRes floor = new CheckRoomRes();
            floor.setFloorCode(y.getKey());
            floor.setArea(resRooms);
            floor.setFloorName(rooms.get(0).getFloorName());
            floor.setId(rooms.get(0).getFloorId());
            floor.setLevel(2);
            floor.setParentId(uuid);
            floor.setSort(rooms.get(0).getSort());
            building.setBuildingName(rooms.get(0).getBuildingName());
            floors.add(floor);
          });
          building.setBuildingCode(x.getKey());
          building.setArea(floors);
          building.setParentId("0");
          building.setId(uuid);
          building.setLevel(1);
          buildings.add(building);

        });
        buildings.forEach(x -> {
          List<CheckRoomRes> floorList = x.getArea();
          for (CheckRoomRes f : floorList) {
            Set<String> checkList = f.getArea().stream()
                .map(CheckRoomRes -> CheckRoomRes.getCheckUserName())
                .collect(Collectors.toSet());
            if (checkList != null && !checkList.isEmpty()) {
              checkList.removeIf(Objects::isNull);
              String checkUser = String.join(",", checkList);
              f.setCheckUserName(checkUser);
            }
          }
        });
        log.info("请求检查人房间列表 耗时：" + (System.currentTimeMillis()-begin));
        //按照楼层sort排序
        for (CheckRoomRes building : buildings) {
          List<CheckRoomRes> area = building.getArea();
          if (CollUtil.isNotEmpty(area)) {
            area.sort(Comparator.comparing(r -> Optional.ofNullable(r).map(CheckRoomRes::getSort).orElse(0)));
          }
        }
        return ResultVO.success(buildings);
      }
    } catch (Exception e) {
      log.error("[做房任务][分配做房列表获取数据异常]", e);
      return ResultVO.failed("请求数据失败");
    }

    return ResultVO.success(roomListVo);
  }

  @Override
  public ResultVO allUserAndTask(Integer companyId) {
    Long begin = System.currentTimeMillis();
    List<AllUserAndTaskRes> res = new ArrayList<>();
    //查询所有的清扫人员
    //List<Rooms> allocatedPeople = cleanRoomTaskMapper.allocatedPeopleNew(companyId);
    List<Rooms> allocatedPeople = this.allocatedPeopleNew(companyId);

    RoomStatusDto dto = new RoomStatusDto();
    dto.setCompanyId(companyId);
    log.info("所有员工所有房间请请求房态接口请求参数:{}", JSONObject.toJSONString(dto));
    List<RoomInfoVo> getAllRoomsSta = roomStatusService.getAllRoomsSta(dto);
    //log.info("所有员工所有房间请请求房态接口返回参数：{}", JSONObject.toJSONString(getAllRoomsSta));
    Map<String, RoomInfoVo> roomInfoVoMap = getAllRoomsSta.stream()
        .collect(Collectors.toMap(RoomInfoVo::getRmno, RoomInfoVo -> RoomInfoVo, (oldValue, newValue) -> newValue));
    //按照清扫人员分组
    Map<Integer, List<Rooms>> listMap = allocatedPeople.stream()
        .collect(Collectors
            .groupingBy(Rooms::getCleanUser));
    Set<Integer> cleanUser = listMap.keySet();
    for (Integer key : cleanUser) {
      AllUserAndTaskRes userRes = new AllUserAndTaskRes();
      userRes.setCleanUser(key);
      userRes.setCleanUserName(listMap.get(key).get(0).getCleanUserName());
      List<Rooms> tasks = listMap.get(key);
      userRes.setAllotNum(tasks.size());
      CleanUsersRes getCleanUser = roomServie.getCleanUserByUserId(companyId,key);
      userRes.setOvertime(getCleanUser !=null && getCleanUser.getOverTime() != null ? getCleanUser.getOverTime() : 0);
      //按照房间分组
      Map<String, List<HsCleanRoomTask>> roomList = tasks.stream()
          .collect(Collectors.groupingBy(HsCleanRoomTask::getRoomNo));
      Set<String> roomKey = roomList.keySet();
      roomKey.forEach(rKey -> {
        if (getAllRoomsSta != null && !getAllRoomsSta.isEmpty()) {
          HsCleanRoomTask task = roomList.get(rKey).get(0);
          task.setOrigRoomSta(roomInfoVoMap.get(rKey).getSta());
        }
      });
      tasks = tasks.stream().
          sorted(Comparator.comparing(HsCleanRoomTask::getRoomNo))
          .collect(Collectors.toList());
      userRes.setRooms(tasks);
      res.add(userRes);
    }
    List<CleanUsersRes> cleanUsersRes = roomServie.getCleanUserFromRedis(companyId);
    List<AllUserAndTaskRes> add = new ArrayList<>();
    for (CleanUsersRes s : cleanUsersRes) {
      if (listMap.get(s.getCleanUser()) == null) {
        AllUserAndTaskRes andTaskRes = new AllUserAndTaskRes();
        andTaskRes.setCleanUser(s.getCleanUser());
        andTaskRes.setCleanUserName(s.getCleanUserName());
        andTaskRes.setOvertime(s.getOverTime() != null ? s.getOverTime() : 0);
        add.add(andTaskRes);
      }
    }
    res.addAll(add);
    res = res.stream().sorted(Comparator.comparing(AllUserAndTaskRes:: getCleanUserName,
        Collator.getInstance(Locale.CHINA))).collect(Collectors.toList());
    log.info("所有员工及其清扫任务耗时："+(System.currentTimeMillis()- begin));
    return ResultVO.success(res);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public ResultVO deleteTaskForUser(List<Integer> ids) {
    List<HsCleanRoomTask> tasks = cleanRoomTaskMapper.taskByMoreId(ids);
    tasks = tasks.stream()
        .filter(t -> !CleanStatusEnum.WAITING_CLEAN.getCode().equals(t.getCleanStatus()))
        .collect(Collectors.toList());
    if (tasks.size() > 0) {
      return ResultVO.failed("有房间不是待清扫状态，不能关闭");
    }
    int closeOrder = cleanRoomTaskMapper
        .closeOrder(ids, CloseReasonEnum.PASSIVITY.getCode());
    roomServie.saveLogBatch(ids,
        "任务关闭", CleanStatusEnum.ORDER_CLOSE.getDesc());

    return ResultVO.success();
  }

  /**
   * web端分配清洁人员 因为房间列表是未分配房间，所以去除掉关闭工单程序
   *
   * @param req
   * @param hotelId
   * @param userId
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public ResultVO allotCleanUser(AllotCleanUserReq req,
      Integer hotelId, Integer userId) {
    log.info(
        "web端分配清洁人员 更新配置请求参数:req=" + JSONObject.toJSONString(req) + ",hotelId=" + hotelId
            + ",userId="
            + userId);
    HsCleanRoomConfig cleanRoomConfig = configMapper.queryByCompanyId(hotelId);
    if (cleanRoomConfig == null || cleanRoomConfig.getBaseDayNum() == null
        || cleanRoomConfig.getBaseMonthNum() == null) {
      return ResultVO.failed("请先去配置底量");
    }
    List<CleanUserRoomReq> roomReq = req.getRoomReq();
    if (roomReq == null || roomReq.isEmpty()) {
      return ResultVO.failed("请求参数：房间信息为空");
    }
    List<String> rooms = roomReq.stream().map(CleanUserRoomReq -> CleanUserRoomReq.getRoomNo())
        .collect(Collectors.toList());
    List<HsCleanRoomCheckUser> users = checkUserMapper.getUserByRoom(hotelId, rooms);
    if (users.isEmpty() || users.size() == 0) {
      return ResultVO.failed("现在有的房间没有设置检查人，请先去设置检查人哦");
    }
    if (!rooms.isEmpty()) {
      Integer roomAllot = cleanRoomTaskMapper.roomAllot(hotelId, rooms);
      if (roomAllot > 0) {
        return ResultVO.failed("当前有房间已经分配，请刷新当前页面后重试");
      }
    }
    Map<String, HsCleanRoomCheckUser> roomMap = users.stream()
        .collect(Collectors
            .toMap(HsCleanRoomCheckUser::getRoomNo, HsCleanRoomCheckUser -> HsCleanRoomCheckUser));
    if (roomReq != null && !roomReq.isEmpty()) {
      CleanUsersRes getCleanUser = roomServie.getCleanUserByUserId(hotelId,req.getCleanUser());
      List<HsCleanRoomTask> tasks = new ArrayList<>();
      for (CleanUserRoomReq x : roomReq) {
        HsCleanRoomTask task = new HsCleanRoomTask();
        String orderNo = orderCommonService.getSnNum();
        task.setOrderNo(orderNo);
        task.setCleanStatus(CleanStatusEnum.WAITING_CLEAN.getCode());
        task.setRoomNo(x.getRoomNo());
        task.setRoomType(x.getRoomType());
        if (org.apache.commons.lang.StringUtils
            .isEmpty(CleanTypeEnum.roomTypeToCleanType(x.getCleanType()))) {
          return ResultVO.failed(x.getRoomNo() + "未传入房态，请传入");
        }
        task.setCleanType(CleanTypeEnum.roomTypeToCleanType(x.getCleanType()));
        task.setOrigRoomSta(x.getCleanType());
        task.setGuestName(x.getGuestName());
        task.setArrivalTime(x.getArrivalTime());
        task.setLeaveTime(x.getLeaveTime());
        task.setCleanUser(req.getCleanUser());
        task.setCleanUserName(req.getCleanUserName());
        task.setAssignCleanUser(userId);
        if (roomMap.get(x.getRoomNo()) != null) {
          task.setCheckUser(roomMap.get(x.getRoomNo()).getCheckUser());
          task.setCheckUserName(roomMap.get(x.getRoomNo()).getCheckUserName());
        } else {
          return ResultVO.failed("房间号为：" + x.getRoomNo() + "没有设置检查人，请先去设置检查人哦");
        }
        task.setCompanyId(hotelId);
        task.setDeleted(0);
        task.setCreateTime(new Date());
        task.setCreateUser(userId);
        task.setUpdateTime(new Date());
        task.setUpdateUser(userId);
        task.setIsDep(x.getIsDep());
        task.setOvertime(getCleanUser != null && getCleanUser.getOverTime() != null ? getCleanUser.getOverTime() : 0);
        tasks.add(task);
      }
      int insertBatch = cleanRoomTaskMapper.insertBatch(tasks);
      List<Integer> newIds = new ArrayList<>();
      tasks.forEach(x -> {
        newIds.add(x.getId());
      });
      if (!newIds.isEmpty() && newIds.size() > 0) {
        roomServie.saveLogBatch(newIds,
            ThreadLocalHelper.getUser().getName() + "创建了工单派单给:" + req.getCleanUserName() + "。",
            CleanStatusEnum.WAITING_CLEAN.getDesc());
      }
      if (insertBatch > 0) {
        log.info("web端清扫人分配房间分配成功");
        return ResultVO
            .success("添加成功，添加的房间是：" + String.join(",", rooms) + ",共添加" + rooms.size() + "间。");
      } else {
        log.info("web端清扫人分配房间分配失败");
        return ResultVO.failed("请重新分配");
      }
    } else {
      log.info("房间参数 请求为空");
      return ResultVO.failed("房间参数 请求为空");
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public ResultVO addOne(Integer companyId, Integer cleanUser, String cleanUserName,
      Integer assignCleanUser) {
    synchronized (this) {
      try {
        HsCleanRoomConfig cleanRoomConfig = configMapper.queryByCompanyId(companyId);
        if (cleanRoomConfig == null || cleanRoomConfig.getBaseDayNum() == null
            || cleanRoomConfig.getBaseMonthNum() == null) {
          return ResultVO.failed("请先去配置底量");
        }
        List<RoomDetailVo> allRoomList = this.allRoomList(companyId);
        if (allRoomList == null || allRoomList.isEmpty()) {
          return ResultVO.failed("暂无脏房，无法分配");
        }
        AllotCleanUserReq re = new AllotCleanUserReq();
        re.setCleanUser(cleanUser);
        re.setCleanUserName(cleanUserName);
        re.setAssignCleanUser(assignCleanUser);
        List<CleanUserRoomReq> roomReq = new ArrayList<>();
        RoomDetailVo vo = allRoomList.get(0);
        if (vo != null) {
          CleanUserRoomReq roomReq1 = new CleanUserRoomReq();
          roomReq1.setArrivalTime(
              org.springframework.util.StringUtils.hasText(vo.getArrivalTime()) ? DateUtils
                  .strConverDate(vo.getArrivalTime()) : null);
          roomReq1.setLeaveTime(
              org.springframework.util.StringUtils.hasText(vo.getLeaveTime()) ? DateUtils
                  .strConverDate(vo.getLeaveTime()) : null);
          roomReq1.setGuestName(vo.getGuestName());
          roomReq1.setRoomNo(vo.getRoomNumber());
          roomReq1.setRoomType(vo.getRoomType());
          //房态
          roomReq1.setCleanType(vo.getSta());
          roomReq1.setIsDep(vo.getIsDep());
          roomReq.add(roomReq1);
        }
        re.setRoomReq(roomReq);
        return this.allotCleanUser(re, companyId, assignCleanUser);
      } catch (Exception e) {
        log.error(e.getMessage(), e);
      }
    }
    return null;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public ResultVO addBaseNum(Integer companyId, Integer cleanUser, String cleanUserName,
      Integer assignCleanUser) {
    synchronized (this) {
      HsCleanRoomConfig cleanRoomConfig = configMapper.queryByCompanyId(companyId);
      if (cleanRoomConfig == null || cleanRoomConfig.getBaseDayNum() == null
          || cleanRoomConfig.getBaseMonthNum() == null) {
        return ResultVO.failed("请先去配置底量");
      }
      try {
        List<RoomDetailVo> allRoomList = this.allRoomList(companyId);
        if (allRoomList == null || allRoomList.isEmpty()) {
          return ResultVO.failed("暂无脏房，无法分配");
        }
        //组装添加参数
        AllotCleanUserReq re = new AllotCleanUserReq();
        re.setCleanUser(cleanUser);
        re.setCleanUserName(cleanUserName);
        re.setAssignCleanUser(assignCleanUser);
        List<CleanUserRoomReq> roomReq = new ArrayList<>();
        List<RoomDetailVo> vos = new ArrayList<>();
        if(allRoomList.size() < cleanRoomConfig.getBaseDayNum()){
          vos = allRoomList.subList(0, allRoomList.size());
        }else{
           vos = allRoomList.subList(0, cleanRoomConfig.getBaseDayNum());
        }
        for (RoomDetailVo vo : vos) {
          CleanUserRoomReq roomReq1 = new CleanUserRoomReq();
          roomReq1.setArrivalTime(
              org.springframework.util.StringUtils.hasText(vo.getArrivalTime()) ? DateUtils
                  .strConverDate(vo.getArrivalTime()) : null);
          roomReq1.setLeaveTime(
              org.springframework.util.StringUtils.hasText(vo.getLeaveTime()) ? DateUtils
                  .strConverDate(vo.getLeaveTime()) : null);
          roomReq1.setGuestName(vo.getGuestName());
          roomReq1.setRoomNo(vo.getRoomNumber());
          roomReq1.setRoomType(vo.getRoomType());
          //房态
          roomReq1.setCleanType(vo.getSta());
          roomReq1.setIsDep(vo.getIsDep());
          roomReq.add(roomReq1);
        }
        re.setRoomReq(roomReq);
        return this.allotCleanUser(re, companyId, assignCleanUser);

      } catch (Exception e) {
        log.error(e.getMessage(), e);
      }
      return null;
    }
  }

  @Override
  public ModDept deptByCompanyConfig(Integer companyId){
    if (companyId == null) {
      throw new RuntimeException("酒店id不能为空");
    }
    String dept = configMapper.getDept(companyId);
    if (StringUtils.hasText(dept)) {
      List<String> stringList = Arrays.asList(dept.split(","));
      ModDept modDept = deptDao.queryById(Integer.valueOf(stringList.get(0)));
      return modDept;
    } else {
      throw new RuntimeException("酒店未配置");
    }
  }
  //脏房
  List<RoomDetailVo> allRoomList(Integer companyId) throws Exception {
    List<RoomDetailVo> allRoomList = roomServie.getRequiredData(3, companyId);
    //未分配做房清扫任务 过滤非脏房
    allRoomList = allRoomList.stream().filter(
        t -> t.getCleanUser() == null || (t.getCloseReason() != null
            && t.getCloseReason() == 1))//|| (t.getCheckUser() == null)
        .collect(Collectors.toList());
    allRoomList = allRoomList.stream()
        .filter(t -> RoomStatusEnum.FREE_DIRTY.getRoomStatu().equals(t.getSta())
            || RoomStatusEnum.BUSY_DIRTY.getRoomStatu().equals(t.getSta()))
        .collect(Collectors.toList());
    return allRoomList;
  }

  @Override
  public List<Rooms> allocatedPeopleNew(Integer companyId) {
    List<HsCleanRoomTask> hsCleanRoomTaskList = cleanRoomTaskMapper.allocatedPeople(companyId);
    if (CollectionUtils.isEmpty(hsCleanRoomTaskList)) {
      return new ArrayList<>();
    }
    List<HsPlanSanitateTask> hsPlanSanitateTaskList = hsPlanSanitateTaskMapper.allocatedPeople(companyId);

    return hsCleanRoomTaskList.stream().map(hsCleanRoomTask -> {
          Rooms rooms = new Rooms();
          BeanUtils.copyProperties(hsCleanRoomTask, rooms);
          if (hsPlanSanitateTaskList.stream().anyMatch(hsPlanSanitateTask ->
              Objects.equals(hsPlanSanitateTask.getRoomNo(), hsCleanRoomTask.getRoomNo())
                  && Objects.equals(hsPlanSanitateTask.getCleanUser(), hsCleanRoomTask.getCleanUser()))) {
            rooms.setPlan(1);
          } else {
            rooms.setPlan(0);
          }
          return rooms;
        }
    ).collect(Collectors.toList());
  }
}















