package com.shands.mod.main.controller.app;

import com.shands.mod.controller.BaseController;
import com.shands.mod.dao.model.homerevision.AppSysMessage;
import com.shands.mod.dao.model.homerevision.ModSysMessage;
import com.shands.mod.dao.model.homerevision.ModSysMessageDetailVo;
import com.shands.mod.dao.model.homerevision.ModSysMessageVo;
import com.shands.mod.dao.model.homerevision.SysMessageAddDto;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.betterwood.log.core.annotation.ResultLog;
import com.shands.mod.dao.model.homerevision.SysMessageListDto;
import com.shands.mod.dao.model.homerevision.SysMessageUpdDto;
import com.shands.mod.main.service.homerevision.ModSysMessageService;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/sysMessage")
@Api(value = "系统公告",tags = "系统公告")
@Slf4j
public class SysMessageController extends BaseController {

  private final ModSysMessageService messageService;

  public SysMessageController(
      ModSysMessageService messageService) {
    this.messageService = messageService;
  }

  @ApiOperation(value = "系统公告列表信息查询")
  @PostMapping(value = "/findSysMessages")
    @ResultLog(name = "SysMessageController.findSysMessages", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<List<ModSysMessageVo>> findSysMessages(@Valid @RequestBody SysMessageListDto sysMessageListDto) {
    return ResultVO.success(messageService.findSysMessages(sysMessageListDto));
  }

  @ApiOperation(value = "系统公告详情信息查询")
  @PostMapping(value = "/detailSysMessages")
    @ResultLog(name = "SysMessageController.detailSysMessages", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<ModSysMessageDetailVo> detailSysMessages(@RequestBody Map<String,Integer> param) {

    Integer redId = param.get("id");
    if(redId == null){
      throw new RuntimeException("记录ID不能为空");
    }
    return ResultVO.success(messageService.detailSysMessages(redId));
  }

  @ApiOperation(value = "系统公告新增")
  @PostMapping(value = "/addSysMessage")
    @ResultLog(name = "SysMessageController.addSysMessage", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<Boolean> addSysMessage(@Valid @RequestBody SysMessageAddDto sysMessageAddDto){
    return ResultVO.success(messageService.addSysMessage(sysMessageAddDto));
  }

  @ApiOperation(value = "系统公告修改")
  @PostMapping(value = "/updSysMessage")
    @ResultLog(name = "SysMessageController.updSysMessage", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<Boolean> updSysMessage(@Valid @RequestBody SysMessageUpdDto sysMessageUpdDto){
    return ResultVO.success(messageService.updateSysMessage(sysMessageUpdDto));
  }

  @ApiOperation(value = "系统公告删除")
  @PostMapping(value = "/delSysMessage")
    @ResultLog(name = "SysMessageController.delSysMessage", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<Boolean> delSysMessage(@RequestBody Map<String,Integer> param){

    Integer redId = param.get("id");
    if(redId == null){
      throw new RuntimeException("记录ID不能为空");
    }

    return ResultVO.success(messageService.delSysMessage(redId));
  }

  @ApiOperation(value = "APP首页系统公共查询")
  @PostMapping(value = "/findSysMessageForApp")
    @ResultLog(name = "SysMessageController.findSysMessageForApp", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<List<AppSysMessage>> findSysMessageForApp() {
    return ResultVO.success(messageService.findSysMessageForApp());
  }

}
