package com.shands.mod.main.remote.cms.fallback;

import com.betterwood.base.common.model.Result;
import com.betterwood.base.common.util.ResultMessageUtil;

import com.shands.mod.dao.model.proprietor.ProprietorCardRecycleQo;
import com.shands.mod.dao.model.proprietor.ProprietorCardSendQo;
import com.shands.mod.main.remote.cms.RemoteBdwCmsCateCardService;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class RemoteBdwCmsCateCardServiceFallbackFactory implements FallbackFactory<RemoteBdwCmsCateCardService> {
    public RemoteBdwCmsCateCardService create(Throwable cause) {
        return new RemoteBdwCmsCateCardService() {

            public Result<String> sendCard(ProprietorCardSendQo sendQo) {
                return ResultMessageUtil.failResponse("BDW-BASE-SYS0001", "发放业主卡失败");
            }

            public Result<String> recycleCard(ProprietorCardRecycleQo recycleQo) {
                return ResultMessageUtil.failResponse("BDW-BASE-SYS0001", "回收业主卡失败");
            }
        };
    }
}
