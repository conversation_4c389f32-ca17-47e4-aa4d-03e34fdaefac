package com.shands.mod.main.service.proprietor.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.shands.mod.dao.model.MobileAndAreaCode;
import com.shands.mod.dao.model.proprietor.VerifyProprietorMobilesRes;
import com.shands.mod.external.model.dto.UcAuthenticationDto;
import com.shands.mod.external.service.UcAuthenticationService;
import com.shands.mod.main.config.ProprietorConfig;
import com.shands.mod.main.config.UcAppConfig;
import com.shands.mod.main.service.proprietor.ProprietorService;
import com.shands.uc.model.req.issue.IssueUserQueryReq;
import com.shands.uc.model.res.issue.IssueUserResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ProprietorService的实现类
 */
@Service
@Slf4j
public class ProprietorServiceImpl implements ProprietorService {

    @Autowired
    private UcAuthenticationService ucAuthenticationService;

    @Autowired
    private ProprietorConfig proprietorConfig;

    @Autowired
    private UcAppConfig ucAppConfig;

    @Override
    public List<VerifyProprietorMobilesRes> verifyProprietorMobiles(List<MobileAndAreaCode> mobileAndAreaCodeList) {
        if (CollUtil.isEmpty(mobileAndAreaCodeList)) {
            return new ArrayList<>();
        }

        // 初始化结果映射，所有手机号默认设置为false
        Map<MobileAndAreaCode, Boolean> proprietorMap = mobileAndAreaCodeList.stream()
                .collect(Collectors.toMap(Function.identity(), mobile -> false));


        // 从nacos配置中获取业主手机号
        Set<String> configProprietorMobiles = getConfigProprietorMobiles();

        // 将配置中的手机号标记为业主
        for (MobileAndAreaCode mobileAndAreaCode : proprietorMap.keySet()) {
            if (configProprietorMobiles.contains(mobileAndAreaCode.getMobile())) {
                proprietorMap.put(mobileAndAreaCode, true);
            }
        }

        // 从配置中获取业主岗位ID列表
        List<Integer> proprietorPostIds = proprietorConfig.getPostId();
        if (CollUtil.isEmpty(proprietorPostIds)) {
            log.error("业主岗位ID列表未配置");
            throw new RuntimeException("业主岗位ID列表未配置");
        }

        // 查询UC中具有业主岗位ID的用户
        Set<String> ucProprietorMobiles = getUcProprietorMobiles(proprietorPostIds);

        // 将UC中的手机号标记为业主
        for (MobileAndAreaCode mobileAndAreaCode : proprietorMap.keySet()) {
            if (ucProprietorMobiles.contains(mobileAndAreaCode.getMobile())) {
                proprietorMap.put(mobileAndAreaCode, true);
            }
        }

        List<VerifyProprietorMobilesRes> verifyResList = new ArrayList<>();

        proprietorMap.forEach((mobileAndAreaCode, isProprietor) -> {
            VerifyProprietorMobilesRes verifyProprietorMobilesRes = new VerifyProprietorMobilesRes();
            BeanUtil.copyProperties(mobileAndAreaCode, verifyProprietorMobilesRes);
            verifyProprietorMobilesRes.setIsProprietor(isProprietor);
            verifyResList.add(verifyProprietorMobilesRes);
        });

        return verifyResList;
    }

    /**
     * 从nacos配置中获取业主手机号
     *
     * @return 业主手机号集合
     */
    @Override
    public Set<String> getConfigProprietorMobiles() {
        Set<String> configProprietorMobiles = new HashSet<>();
        String proprietorMobileList = proprietorConfig.getMobileList();

        if (StrUtil.isNotBlank(proprietorMobileList)) {
            String[] mobiles = proprietorMobileList.split(",");
            for (String mobile : mobiles) {
                if (StrUtil.isNotBlank(mobile)) {
                    configProprietorMobiles.add(mobile.trim());
                }
            }
        }

        return configProprietorMobiles;
    }

    /**
     * 根据岗位ID列表从UC中获取业主手机号
     *
     * @param proprietorPostIds 业主岗位ID列表
     * @return 业主手机号集合
     */
    @Override
    public Set<String> getUcProprietorMobiles(List<Integer> proprietorPostIds) {
        Set<String> ucProprietorMobiles = new HashSet<>();

        if (CollUtil.isEmpty(proprietorPostIds)) {
            return ucProprietorMobiles;
        }

        try {
            // 创建认证DTO
            UcAuthenticationDto ucAuthenticationDto = new UcAuthenticationDto();
            ucAuthenticationDto.setAppId(ucAppConfig.getUcAppId());
            ucAuthenticationDto.setSecret(ucAppConfig.getUcSercret());
            ucAuthenticationDto.setDomain(ucAppConfig.getDomain());

            // 遍历每个岗位ID，分别查询并合并结果
            for (Integer postId : proprietorPostIds) {
                // 创建用户查询请求
                IssueUserQueryReq issueUserQueryReq = new IssueUserQueryReq();
                issueUserQueryReq.setPostId(postId);
                issueUserQueryReq.setDeleted(0);
                issueUserQueryReq.setStatus(1);

                // 查询UC中具有业主岗位ID的用户
                List<IssueUserResponse> userResponses = ucAuthenticationService.syncUser(ucAuthenticationDto, issueUserQueryReq);

                if (CollUtil.isNotEmpty(userResponses)) {
                    for (IssueUserResponse userResponse : userResponses) {
                        if (userResponse != null && StrUtil.isNotBlank(userResponse.getMobile())) {
                            ucProprietorMobiles.add(userResponse.getMobile());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("从UC获取业主手机号时发生错误", e);
            throw new RuntimeException("从UC获取业主手机号时发生错误", e);
        }

        if (CollUtil.isEmpty(ucProprietorMobiles)) {
            log.error("UC中未查询到具有岗位ID {} 的用户", proprietorPostIds);
            throw new RuntimeException("UC中未查询到具有岗位ID");
        }

        return ucProprietorMobiles;
    }
}
