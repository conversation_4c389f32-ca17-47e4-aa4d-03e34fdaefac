package com.shands.mod.main.service.betterwood.rpc.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 营销中心房价码预定规则详情
 * @author: lihui
 * @create: 2023-12-05 11:10
 **/
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class PromotionResrvRuleInfoVO implements Serializable {

    @ApiModelProperty(value = "支付类型")
    private String payType;
    @ApiModelProperty(value = "支付方式")
    private String payMethod;
    @ApiModelProperty(value = "支付方式名称")
    private String payMethodName;
    @ApiModelProperty(value = "现转预超时阈值（HH:MM、>=）（18:00）")
    private String prepayTimeLimit;
    @ApiModelProperty(value = "担保类型(首晚：FD、全额：FU)")
    private String prepayMethod;
    @ApiModelProperty(value = "预付最大支付保留时间")
    private Integer prepayHoldTimeMax;
    @ApiModelProperty(value = "超时规则")
    private String overtimeRule;
    @ApiModelProperty(value = "超时时间")
    private String overtime;
    @ApiModelProperty(value = "超时预定类型")
    private String overtimeRsvType;
    @ApiModelProperty(value = "超时预定类型名称")
    private String overtimeRsvTypeName;
    @ApiModelProperty(value = "超时担保规则")
    private String overtimeGuarantee;
    @ApiModelProperty(value = "预定类型")
    private String pmsRsvType;
    @ApiModelProperty(value = "预定类型名称")
    private String pmsRsvTypeName;
    @ApiModelProperty(value = "付款码")
    private String pmsTaCode;
    @ApiModelProperty(value = "提前取消时间")
    private Integer cancelPreHour;
    @ApiModelProperty(value = "取消规则")
    private String cancelRule;
    @ApiModelProperty(value = "规则code")
    private String ruleCode;
    @ApiModelProperty(value = "取消规则描述")
    private String cancelDesc;
    @ApiModelProperty(value = "规则code严格权重，值越小越严格")
    private Integer order;

}
