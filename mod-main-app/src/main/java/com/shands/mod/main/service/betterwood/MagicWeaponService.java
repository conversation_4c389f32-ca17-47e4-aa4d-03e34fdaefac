package com.shands.mod.main.service.betterwood;

import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.model.req.voucher.VoucherWriteOffReq;
import com.shands.mod.dao.model.res.voucher.VoucherInfoRes;
import java.util.List;

/**
 * @Description:
 * @Author: Java菜鸟
 * @CreateDate: 2022/9/26 11:01
 */
public interface MagicWeaponService {

  JSONObject betterWoodVerification(Long certificateNo,String hotelCode,Integer hotelId,String hotelName);

  JSONObject detail(String certificateNo);

  JSONObject qurMemberInfo(String hotelCode,String mobile,String certificateNo,Integer pageNo,Integer pageSize);

  /**
   * 百达星会员查询，自带token
   *
   * @param token 百达星token
   * @param hotelCode 酒店code
   * @param mobile 手机号
   * @param certificateNo 身份证号
   * @param pageNo 页码
   * @param pageSize 页大小
   * @return
   */
  JSONObject qurMemberInfoWithToken(String token, String hotelCode, String mobile,
      String certificateNo, Integer pageNo, Integer pageSize);

  /**
   * 手机号批量查询会员信息接口
   *
   * @param mobileList 手机号列表
   * @return 会员信息
   */
  JSONObject batchQueryMemberInfo(List<String> mobileList);

  JSONObject qurCouponList(String hotelCode,String userId,Integer pageNo,Integer pageSize);

  JSONObject bookingDataReport(String hotelCode,String reachTimeStart,String leaveTimeEnd,Integer pageNo,Integer pageSize);

  JSONObject betterWoodNewVerification(VoucherWriteOffReq voucherWriteOffReq);

  VoucherInfoRes queryVoucherByVoucherNum(String voucherNum, Boolean newVersion, String hotelCode);

  JSONObject roomOrderScan(String apiUrl);

  JSONObject roomOrderScanP(String apiUrl,String param,String page);
}
