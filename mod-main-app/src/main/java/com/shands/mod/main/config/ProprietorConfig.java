package com.shands.mod.main.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 业主相关配置
 */
@RefreshScope
@Data
@Component
@ConfigurationProperties(prefix = "proprietor")
public class ProprietorConfig {

    /**
     * 逗号分隔的业主手机号列表
     */
    private String mobileList = "";

    /**
     * 业主岗位ID列表
     */
    private List<Integer> postId = new ArrayList<>();

    /**
     * 业主卡ID
     */
    private Integer cardId = 0;
}
