package com.shands.mod.main.remote.menberRights.fallback;

import com.shands.mod.dao.model.req.hs.MemberRightsWriteOffReq;
import com.shands.mod.dao.model.res.MemberRightsRes;
import com.shands.mod.dao.model.res.Response;
import com.shands.mod.main.remote.menberRights.MemberRightsService;
import com.shands.mod.vo.ResultVO;
import org.springframework.stereotype.Component;

@Component
public class MemberRightsServiceFallBack implements MemberRightsService {

  private static final ResultVO ERROR = ResultVO.failed("服务异常，稍后再试");

  @Override
  public Response<MemberRightsRes> memberRightsWriteOff(
      MemberRightsWriteOffReq req) {
    return null;
  }
}
