package com.shands.mod.main.service.crm;

import com.shands.mod.dao.model.v0701.dto.DiscountInfoDto;
import com.shands.mod.dao.model.v0701.dto.MemberInfoDto;
import com.shands.mod.dao.model.v0701.dto.MemberPayDto;
import com.shands.mod.dao.model.v0701.dto.PayInfoDto;
import com.shands.mod.dao.model.v0701.dto.PosPluDto;
import com.shands.mod.dao.model.v0701.vo.CateringOrderInfoVo;
import com.shands.mod.dao.model.v0701.vo.PosPluVo;
import com.shands.mod.external.model.vo.MemberCardDiscountVo;
import com.shands.mod.external.model.vo.MemberInfoVo;
import com.shands.mod.vo.ResultVO;
import java.util.List;

/**
 * imember服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-05-21
 * @description 会员支付
 */
public interface IMemberService {

  /**
   * 查询会员卡片
   *
   * @param mobile 手机号
   * @return {@link List<MemberInfoVo>}
   */
  List<MemberInfoVo> queryMemberCards(String mobile);

  /**
   * 发送验证码
   *
   * @param mobile 手机号
   * @return boolean
   */
  String sendCode(String mobile);

  /**
   * 校验验证码并重置密码
   *
   * @param dto 实例对象
   * @return {@link ResultVO}
   */
  ResultVO verifyCode(MemberInfoDto dto);


  /**
   * 计算订单折扣信息
   *
   * @param dto dto
   * @return {@link CateringOrderInfoVo}
   */
  CateringOrderInfoVo calcFoodsPrice(DiscountInfoDto dto);

  /**
   * 计算单个食品价格
   *
   * @param dto            菜品对象
   * @param cardDiscountVo 卡折扣信息
   * @return {@link PosPluVo}
   */
  PosPluVo calcSingleFoodPrice(PosPluDto dto, MemberCardDiscountVo cardDiscountVo);

  /**
   * 小程序用户密码支付
   *
   * @param dto 实例对象
   */
  ResultVO miniPayByPassword(PayInfoDto dto);

  /**
   * 使用短信验证码重置密码并支付
   *
   * @param dto 实例对象
   * @return {@link ResultVO}
   */
  ResultVO miniPayByCode(MemberPayDto dto);
}
