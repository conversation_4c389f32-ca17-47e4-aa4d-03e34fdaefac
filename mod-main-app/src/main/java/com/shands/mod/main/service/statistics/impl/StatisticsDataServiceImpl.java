package com.shands.mod.main.service.statistics.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.delonix.bi.dao.mapper.AdsPowerDlEcoAppEmployeePerfDMapper;
import com.google.common.collect.Lists;
import com.shands.mod.dao.mapper.board.ModNewDataBoardMapper;
import com.shands.mod.dao.mapper.datarevision.AdsAppTradeRevenueDMapper;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.datarevision.vo.ModuleMenuVo;
import com.shands.mod.dao.model.enums.DateTypeEnum;
import com.shands.mod.dao.model.enums.board.DataBoardModuleEnum;
import com.shands.mod.dao.model.newDataBoard.vo.HomeDataDetailsVo;
import com.shands.mod.dao.model.res.AdsHotelPerformanceHDTO;
import com.shands.mod.dao.model.statistics.dto.AdsAppTradeRevenueDDto;
import com.shands.mod.dao.model.statistics.dto.CardSellRankingItem;
import com.shands.mod.dao.model.statistics.req.BusinessDataDetailReq;
import com.shands.mod.dao.model.statistics.req.CommonStatisticReq;
import com.shands.mod.dao.model.statistics.vo.*;
import com.shands.mod.dao.model.statistics.vo.SellCardRankingVo;
import com.shands.mod.dao.util.ThousandSeparatorUtil;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.main.enums.TrendTypeEnum;
import com.shands.mod.main.service.statistics.StatisticsDataService;
import com.shands.mod.main.util.DateCalculateUtil;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.service.ModUserCommonService;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.BigDecimalUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 经营数据概况服务实现
 * @Author: mazhiyong
 */
@Service
@Slf4j
public class StatisticsDataServiceImpl implements StatisticsDataService {

    @Autowired
    private AdsAppTradeRevenueDMapper adsAppTradeRevenueDMapper;

    @Autowired
    private ModHotelInfoDao hotelInfoDao;

    @Autowired
    private ModUserCommonService modUserCommonService;

    @Autowired
    private ModNewDataBoardMapper modNewDataBoardMapper;

    @Autowired
    private AdsPowerDlEcoAppEmployeePerfDMapper adsPowerDlEcoAppEmployeePerfDMapper;


    /**
     * 获取经营数据概况
     *
     * @param req 请求参数
     * @return 经营数据概况
     */
    @Override
    public BaseStatisticsDataVo getBriefBusinessData(CommonStatisticReq req) {
        BaseStatisticsDataVo res = new BaseStatisticsDataVo();

        // 1. 获取当前用户id和酒店信息
        Integer userId = ThreadLocalHelper.getUser().getId();
        int hotelId = ThreadLocalHelper.getCompanyId();
        String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();

        // 2. 检查用户权限
        final String businessDataCode = DataBoardModuleEnum.BUSINESS_DATA.name();
        ModuleMenuVo vo = getModuleMenu(businessDataCode);
        // 3. 获取指标列表
        List<HomeDataDetailsVo> dataList = getIndicatorList(businessDataCode);
        if (Objects.isNull(vo) || CollUtil.isEmpty(dataList)) {
            log.error("用户权限不足, userId:{}", userId);
            res.setShowFlag(false);
            return res;
        }

        // 4. 查询当前数据
        AdsAppTradeRevenueDDto currentData = adsAppTradeRevenueDMapper.selectRangeData(
                req.getCurrentStartTime(), req.getCurrentEndTime(), hotelCode);


        // 5. 默认对比去年数据
        Date lastStartTime = DateCalculateUtil.getLastYearDateForBetween(req.getCurrentStartTime(), true);
        Date lastEndTime = DateCalculateUtil.getLastYearDateForBetween(req.getCurrentEndTime(), false);

        AdsAppTradeRevenueDDto lastData = adsAppTradeRevenueDMapper.selectRangeData(
                lastStartTime, lastEndTime, hotelCode);


        for (HomeDataDetailsVo detailsVo : dataList) {
            // 增降幅
            this.setBriefBusinessDataTrendRate(currentData, lastData, detailsVo);
            // 赋值操作
            this.fillAdsAppTradeRevenueDDataValue(currentData, detailsVo, true);
        }
        // 构建返回结果
        res.setDataList(dataList);
        res.setTitle(vo.getModuleName());
        res.setShowFlag(true);
        res.setTitleDesc(vo.getDescName());

        return res;
    }

    @Override
    public List<HomeDataDetailsVo> getBusinessDataIndicator() {

        return getIndicatorList(DataBoardModuleEnum.BUSINESS_DATA.name());
    }

    private ModuleMenuVo getModuleMenu(String moduleCode) {
        // 获取当前用户id
        Integer userId = ThreadLocalHelper.getUser().getId();

        List<String> userRoles = modUserCommonService.getUserRoles(userId);
        List<ModuleMenuVo> dataBoards = modNewDataBoardMapper.findModuleMenuDetailList("HOTEL", userRoles, userId);
        ModuleMenuVo vo = dataBoards.stream()
                .filter(v -> moduleCode.equals(v.getModuleCode()))
                .findFirst().orElse(null);
        return vo;
    }

    public List<HomeDataDetailsVo> getIndicatorList(String moduleCode) {
        // 1. 获取当前用户id
        Integer userId = ThreadLocalHelper.getUser().getId();

        // 2. 检查用户权限
        List<String> userRoles = modUserCommonService.getUserRoles(userId);
        List<ModuleMenuVo> dataBoards = modNewDataBoardMapper.findModuleMenuDetailList("HOTEL", userRoles,userId);
        ModuleMenuVo vo = getModuleMenu(moduleCode);
        if (Objects.isNull(vo)) {
            return Lists.newArrayList();
        }

        // 5. 获取指标列表
        List<ModuleMenuVo> indicatorList = dataBoards.stream()
                .filter(v -> v.getPId().equals(vo.getId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());

        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(vo.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();

            dataList.add(detailsVo);
        }

        return dataList;
    }


    /**
     * 获取经营数据详情
     *
     * @param req 请求参数
     * @return 经营数据详情
     */
    @Override
    public BusinessDataDetailVo getBusinessDataDetail(BusinessDataDetailReq req) {
        // 1. 获取当前用户酒店信息
        int hotelId = ThreadLocalHelper.getCompanyId();
        String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();

        // 2. 检查用户权限
        ModuleMenuVo vo = getModuleMenu(req.getDataType());
        if (Objects.isNull(vo)) {
            throw new ServiceException("用户权限不足,请联系管理员！");
        }

        Date lastStartTime = DateCalculateUtil.getLastYearDateForBetween(req.getCurrentStartTime(), true);
        Date lastEndTime = DateCalculateUtil.getLastYearDateForBetween(req.getCurrentEndTime(), false);

        BusinessDataDetailVo res = new BusinessDataDetailVo();
        res.setShowFlag(true);
        res.setTitle(vo.getModuleName());
        res.setTitleDesc(vo.getDescName());
        res.setUnit(vo.getUnit());

        List<BusinessDataDetail> dateDetailList = new ArrayList<>();

        if (StrUtil.equals(req.getTimeType(), DateTypeEnum.DAY.name()) || StrUtil.equals(req.getTimeType(), DateTypeEnum.MONTH.name())) {
            // 查询每日数据
            List<AdsAppTradeRevenueDDto> currentDataList = adsAppTradeRevenueDMapper.selectRangeDataList(req.getCurrentStartTime(), req.getCurrentEndTime(), hotelCode);
            List<AdsAppTradeRevenueDDto> lastDataList = adsAppTradeRevenueDMapper.selectRangeDataList(lastStartTime, lastEndTime, hotelCode);
            // 转化成map
            Map<Date, AdsAppTradeRevenueDDto> currentDataMap = currentDataList.stream()
                    .collect(Collectors.toMap(AdsAppTradeRevenueDDto::getBizDate, Function.identity(), (k1, k2) -> k1));
            Map<Date, AdsAppTradeRevenueDDto> lastDataMap = lastDataList.stream()
                    .collect(Collectors.toMap(AdsAppTradeRevenueDDto::getBizDate, Function.identity(), (k1, k2) -> k1));

            // 日期递增
            Date currentMoveDate = new Date(req.getCurrentStartTime().getTime());
            while (DateUtil.compare(currentMoveDate, req.getCurrentEndTime()) <= 0) {

                Date lastMoveDate = DateUtil.offset(currentMoveDate, DateField.YEAR, -1);

                BusinessDataDetail dataDetail = new BusinessDataDetail();
                dataDetail.setCurrentDate(DateUtil.format(currentMoveDate, DateTimeFormatter.ofPattern(BaseConstants.FORMAT_DATE4)));
                dataDetail.setLastDate(DateUtil.format(lastMoveDate, DateTimeFormatter.ofPattern(BaseConstants.FORMAT_DATE4)));
                // 设置本期值
                AdsAppTradeRevenueDDto currentAdsAppTradeRevenueDDto = currentDataMap.get(currentMoveDate);
                dataDetail.setCurrentValue(getBusinessDataDetailValue(currentAdsAppTradeRevenueDDto, req.getDataType(), res.getUnit()));
                // 设置往期值
                AdsAppTradeRevenueDDto lastAdsAppTradeRevenueDDto = lastDataMap.get(lastMoveDate);
                dataDetail.setLastValue(getBusinessDataDetailValue(lastAdsAppTradeRevenueDDto, req.getDataType(), res.getUnit()));

                // 设置增降幅
                setBusinessDataDetailTrendRate(currentAdsAppTradeRevenueDDto, lastAdsAppTradeRevenueDDto, dataDetail, req.getDataType());

                currentMoveDate = DateUtil.offsetDay(currentMoveDate, 1);
                dateDetailList.add(dataDetail);
            }

        } else {
            // 月度数据，单位改为元
            if (StrUtil.equals("元", vo.getUnit()) &&
                    !("invest_adr".equals(vo.getModuleCode()) || "invest_revpar".equals(vo.getModuleCode()) ) ) {
                res.setUnit("万");
            }
            // 月份递增
            Date currentMoveMonth = DateUtil.beginOfMonth(req.getCurrentStartTime());
            while (DateUtil.compare(currentMoveMonth, DateUtil.beginOfMonth(req.getCurrentEndTime())) <= 0) {

                Date lastMoveMonth = DateUtil.offset(currentMoveMonth, DateField.YEAR, -1);

                AdsAppTradeRevenueDDto currentMonthData = adsAppTradeRevenueDMapper.selectRangeData(currentMoveMonth, DateUtil.endOfMonth(currentMoveMonth), hotelCode);
                AdsAppTradeRevenueDDto lastMonthData = adsAppTradeRevenueDMapper.selectRangeData(lastMoveMonth, DateUtil.endOfMonth(lastMoveMonth), hotelCode);

                BusinessDataDetail detail = new BusinessDataDetail();
                detail.setCurrentDate(DateUtil.format(currentMoveMonth, DateTimeFormatter.ofPattern(BaseConstants.FORMAT_DATE5)));
                detail.setLastDate(DateUtil.format(lastMoveMonth, DateTimeFormatter.ofPattern(BaseConstants.FORMAT_DATE5)));
                // 设置本期值
                detail.setCurrentValue(getBusinessDataDetailValue(currentMonthData, req.getDataType(), res.getUnit()));
                // 设置往期值
                detail.setLastValue(getBusinessDataDetailValue(lastMonthData, req.getDataType(), res.getUnit()));

                // 设置增降幅
                setBusinessDataDetailTrendRate(currentMonthData, lastMonthData, detail, req.getDataType());

                currentMoveMonth = DateUtil.offsetMonth(currentMoveMonth, 1);
                dateDetailList.add(detail);
            }
        }
        res.setDateDetail(dateDetailList);

        return res;

    }


    @Override
    public RoomNightDetailVo getRoomNightDetailData(CommonStatisticReq req) {
        // 1. 获取当前用户id和酒店信息
        Integer userId = ThreadLocalHelper.getUser().getId();
        int hotelId = ThreadLocalHelper.getCompanyId();
        String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();
        req.setHotelCode(hotelCode);

        // 2. 检查用户权限
        List<String> userRoles = modUserCommonService.getUserRoles(userId);
        List<ModuleMenuVo> moduleMenuVos = modNewDataBoardMapper.findModuleMenuDetailList("HOTEL", userRoles, userId);

        // 3. 提取数据查询到外层，避免重复查询
        AdsAppTradeRevenueDDto roomNightData = adsAppTradeRevenueDMapper.selectRangeData(
                req.getCurrentStartTime(), req.getCurrentEndTime(), req.getHotelCode());

        RoomNightDetailVo res = new RoomNightDetailVo();

        // 4. 使用通用方法设置各种模块数据
        setModuleStatisticsData(DataBoardModuleEnum.SOLD_OUT_ROOM_NIGHT_DATA, res.getSoldOutRoomNight(),
                moduleMenuVos, roomNightData);
        setModuleStatisticsData(DataBoardModuleEnum.BETTERWODD_ROOM_NIGHT_DATA, res.getBetterWoodRoomNight(),
                moduleMenuVos, roomNightData);
        setModuleStatisticsData(DataBoardModuleEnum.COOPERATION_CHANNEL_ROOM_NIGHT_DATA, res.getCooperationChannelRoomNight(),
                moduleMenuVos, roomNightData);
        setModuleStatisticsData(DataBoardModuleEnum.OFFLINE_ROOM_NIGHT_DATA, res.getOfflineRoomNight(),
                moduleMenuVos, roomNightData);

        return res;
    }



    /**
     * 设置模块统计数据
     * @param moduleEnum 模块枚举
     * @param targetDataVo 目标数据对象
     * @param moduleMenuVos 模块菜单列表
     * @param statisticsData 统计数据
     */
    private void setModuleStatisticsData(DataBoardModuleEnum moduleEnum, BaseStatisticsDataVo targetDataVo,
                                  List<ModuleMenuVo> moduleMenuVos, AdsAppTradeRevenueDDto statisticsData) {
        // 查找对应的模块菜单
        ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
                .filter(v -> moduleEnum.name().equals(v.getModuleCode()))
                .findFirst().orElse(null);

        // 如果没有权限，设置为不显示
        if (bigModuleMenu == null) {
            targetDataVo.setShowFlag(false);
            return;
        }

        // 获取指标列表
        List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
                .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());

        // 设置基本信息
        targetDataVo.setShowFlag(true);
        targetDataVo.setTitle(bigModuleMenu.getModuleName());
        targetDataVo.setTitleDesc(bigModuleMenu.getDescName());

        // 构建数据列表
        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(bigModuleMenu.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();
            // 赋值操作
            this.fillAdsAppTradeRevenueDDataValue(statisticsData, detailsVo, false);
            dataList.add(detailsVo);
        }

        targetDataVo.setDataList(dataList);
    }



    /**
     * 获取会员数据详情
     *
     * @param req 请求参数
     * @return 会员数据详情
     */
    @Override
    public BetterWoodMemberDataVo getBetterWoodMemberData(CommonStatisticReq req) {
        // 1. 获取当前用户id和酒店信息
        Integer userId = ThreadLocalHelper.getUser().getId();
        int hotelId = ThreadLocalHelper.getCompanyId();
        String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();
        req.setHotelCode(hotelCode);

        // 2. 检查用户权限
        List<String> userRoles = modUserCommonService.getUserRoles(userId);
        List<ModuleMenuVo> moduleMenuVos = modNewDataBoardMapper.findModuleMenuDetailList("HOTEL", userRoles, userId);

        BetterWoodMemberDataVo res = new BetterWoodMemberDataVo();

        // 3. 检查各模块权限并执行相应的数据查询
        
        // 检查是否需要查询AdsAppTradeRevenueDDto数据（APP消费会员数据 + 门店法宝数据）
        boolean needAppTradeData = moduleMenuVos.stream()
                .anyMatch(v -> DataBoardModuleEnum.APP_CONSUME_MEMBER_DATA.name().equals(v.getModuleCode()) );
        
        AdsAppTradeRevenueDDto memberData = null;
        if (needAppTradeData) {
            memberData = adsAppTradeRevenueDMapper.selectRangeData(
                    req.getCurrentStartTime(), req.getCurrentEndTime(), req.getHotelCode());
        }

        // 检查是否需要查询AdsHotelPerformanceHDTO数据（会员业绩归属数据 + 百达卡销售数据）
        boolean needPerformanceData = moduleMenuVos.stream()
                .anyMatch(v -> DataBoardModuleEnum.MEMBER_PERFORMANCE_BELONG_DATA.name().equals(v.getModuleCode()) ||
                              DataBoardModuleEnum.BETTERWOOD_CARD_SELL_DATA.name().equals(v.getModuleCode()) ||
                        DataBoardModuleEnum.HOTEL_VOUCHER_DATA.name().equals(v.getModuleCode()));
        
        AdsHotelPerformanceHDTO performanceData = null;
        if (needPerformanceData) {
            performanceData = adsPowerDlEcoAppEmployeePerfDMapper.sumPerformanceByHotel(
                    req.getHotelCode(), req.getCurrentStartTime(), req.getCurrentEndTime());
        }

        
        // 全网APP消费会员数据
        setModuleStatisticsData(DataBoardModuleEnum.APP_CONSUME_MEMBER_DATA, res.getAppConsumeMemberData(), moduleMenuVos, memberData);

        // 会员业绩归属数据
        setMemberPerformanceData(DataBoardModuleEnum.MEMBER_PERFORMANCE_BELONG_DATA, res.getMemberPerformanceBelongData(), moduleMenuVos, performanceData);

        // 门店法宝数据
        setMemberPerformanceData(DataBoardModuleEnum.HOTEL_VOUCHER_DATA, res.getHotelVoucherData(), moduleMenuVos, performanceData);

        // 百达卡销售数据
        setBetterWoodCardSellData(DataBoardModuleEnum.BETTERWOOD_CARD_SELL_DATA, res.getBetterWoodCardSellData(), moduleMenuVos, performanceData);

        return res;
    }

    /**
     * 设置会员业绩相关数据-（业绩归属、门店法宝）
     * @param moduleEnum 模块枚举
     * @param targetDataVo 目标数据对象
     * @param moduleMenuVos 模块菜单列表
     * @param data 数据
     */
    private void setMemberPerformanceData(DataBoardModuleEnum moduleEnum, BaseStatisticsDataVo targetDataVo,
                                          List<ModuleMenuVo> moduleMenuVos, AdsHotelPerformanceHDTO data) {
        // 查找对应的模块菜单
        ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
                .filter(v -> moduleEnum.name().equals(v.getModuleCode()))
                .findFirst().orElse(null);

        // 如果没有权限，设置为不显示
        if (bigModuleMenu == null) {
            targetDataVo.setShowFlag(false);
            return;
        }

        // 获取指标列表
        List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
                .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());

        // 设置基本信息
        targetDataVo.setShowFlag(true);
        targetDataVo.setTitle(bigModuleMenu.getModuleName());
        targetDataVo.setTitleDesc(bigModuleMenu.getDescName());

        List<HomeDataDetailsVo> dataList = new ArrayList<>();

        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(bigModuleMenu.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();
            // 赋值操作
            this.fillAdsHotelPerformanceHDTODataValue(data, detailsVo);

            dataList.add(detailsVo);
        }

        targetDataVo.setDataList(dataList);
    }

    /**
     * 设置百达卡销售数据
     * @param moduleEnum 模块枚举
     * @param targetDataVo 目标数据对象
     * @param moduleMenuVos 模块菜单列表
     * @param data 数据
     */
    private void setBetterWoodCardSellData(DataBoardModuleEnum moduleEnum, BetterWoodCardSellDataVo targetDataVo,
                                           List<ModuleMenuVo> moduleMenuVos, AdsHotelPerformanceHDTO data) {
        // 查找对应的模块菜单
        ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
                .filter(v -> moduleEnum.name().equals(v.getModuleCode()))
                .findFirst().orElse(null);

        // 如果没有权限，设置为不显示
        if (bigModuleMenu == null) {
            targetDataVo.setShowFlag(false);
            return;
        }

        // 获取指标列表
        List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
                .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
                .sorted(Comparator.comparing(ModuleMenuVo::getSort))
                .collect(Collectors.toList());

        // 设置基本信息
        targetDataVo.setShowFlag(true);
        targetDataVo.setTitle(bigModuleMenu.getModuleName());
        targetDataVo.setTitleDesc(bigModuleMenu.getDescName());

        // 设置指标数据列表
        List<HomeDataDetailsVo> dataList = new ArrayList<>();
        for (ModuleMenuVo indicator : indicatorList) {
            HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
                    .name(indicator.getModuleName())
                    .desc(indicator.getDescName())
                    .code(indicator.getModuleCode())
                    .pCode(bigModuleMenu.getModuleCode())
                    .sort(indicator.getSort())
                    .unit(indicator.getUnit())
                    .build();
            // 赋值操作
            this.fillAdsHotelPerformanceHDTODataValue(data, detailsVo);

            dataList.add(detailsVo);
        }
        targetDataVo.setDataList(dataList);

        // 设置百达卡销售占比数据
        List<BetterWoodCardDetailVo> cardDetailList = new ArrayList<>();
        // 启航卡
        cardDetailList.add(BetterWoodCardDetailVo.builder()
                .cardName("启航卡")
                .cardNum(data == null || data.getMemCardOneN() == null ? "0" :
                        ThousandSeparatorUtil.format(data.getMemCardOneN().intValue()) )
                .cardBonus(data == null || data.getMemCardOneBonusAmt() == null ? "0" :
                        ThousandSeparatorUtil.format(data.getMemCardOneBonusAmt()))
                .build());

        // 漫旅卡
        cardDetailList.add(BetterWoodCardDetailVo.builder()
                .cardName("漫旅卡")
                .cardNum(data == null || data.getMemCardSecondN() == null ? "0" :
                        ThousandSeparatorUtil.format(data.getMemCardSecondN().intValue()) )
                .cardBonus(data == null || data.getMemCardSecondBonusAmt() == null ? "0" :
                        ThousandSeparatorUtil.format(data.getMemCardSecondBonusAmt()))
                .build());

        // 开拓卡
        cardDetailList.add(BetterWoodCardDetailVo.builder()
                .cardName("开拓卡")
                .cardNum(data == null || data.getMemCardThirdN() == null ? "0" :
                        ThousandSeparatorUtil.format(data.getMemCardThirdN().intValue()) )
                .cardBonus(data == null || data.getMemCardThirdBonusAmt() == null ? "0" :
                        ThousandSeparatorUtil.format(data.getMemCardThirdBonusAmt()))
                .build());

        // 设置百达卡销售占比数据
        targetDataVo.setCardDetailList(cardDetailList);
    }

    /**
     * 获取门店售卡奖金排行榜
     *
     * @param req 请求参数
     * @return 门店售卡奖金排行榜数据
     */
    @Override
    public SellCardRankingVo getSellCardRanking(CommonStatisticReq req) {
        // 1. 获取当前用户id
        Integer userId = ThreadLocalHelper.getUser().getId();

        SellCardRankingVo result = new SellCardRankingVo();

        // 2. 检查用户权限
        final String cardRankDataCode = DataBoardModuleEnum.BETTERWOOD_CARD_RANK_DATA.name();
        ModuleMenuVo vo = getModuleMenu(cardRankDataCode);
        if (Objects.isNull(vo)) {
            log.error("用户权限不足, userId:{}", userId);
            result.setShowFlag(false);
            return result;
        }

        // 3. 构建返回结果
        result.setTitle(vo.getModuleName());
        result.setShowFlag(true);

        // 4. 查询数据
        List<AdsHotelPerformanceHDTO> hotelRankingData = adsPowerDlEcoAppEmployeePerfDMapper.getTopHotelCardSellRanking(req.getCurrentStartTime(), req.getCurrentEndTime());
        List<AdsHotelPerformanceHDTO> employeeRankingData = adsPowerDlEcoAppEmployeePerfDMapper.getTopEmployeeCardSellRanking(req.getCurrentStartTime(), req.getCurrentEndTime());

        List<CardSellRankingItem> hotelRanking = Lists.newArrayList();
        List<CardSellRankingItem> employeeRanking = Lists.newArrayList();

        // 格式化门店排行榜数据
        for (AdsHotelPerformanceHDTO dto : hotelRankingData) {
            hotelRanking.add(CardSellRankingItem.builder()
                    .hotelName(dto.getHotelName())
                    .cardNum(dto.getBdCardCnt() != null ? ThousandSeparatorUtil.format(dto.getBdCardCnt().intValue()) : "0")
                    .cardBonus(dto.getBonusAmt() != null ? ThousandSeparatorUtil.format(dto.getBonusAmt()) : "0")
                    .build());
        }
        
        // 格式化员工排行榜数据
        for (AdsHotelPerformanceHDTO dto : employeeRankingData) {
            employeeRanking.add(CardSellRankingItem.builder()
                    .hotelName(dto.getHotelName())
                    .employeeName(dto.getEmployeeName())
                    .cardNum(dto.getBdCardCnt() != null ? ThousandSeparatorUtil.format(dto.getBdCardCnt().intValue()) : "0")
                    .cardBonus(dto.getBonusAmt() != null ? ThousandSeparatorUtil.format(dto.getBonusAmt()) : "0")
                    .build());
        }

        result.setHotelRanking(hotelRanking);
        result.setEmployeeRanking(employeeRanking);

        return result;
    }



    private void setBusinessDataDetailTrendRate(AdsAppTradeRevenueDDto currentData, AdsAppTradeRevenueDDto lastData,
                                                BusinessDataDetail detail, String code) {
        if (Objects.isNull(lastData) || Objects.isNull(currentData)) {
            detail.setTrendType(TrendTypeEnum.UNCOMPARABLE.getCode());
            detail.setTrendRate("-");
            return;
        }

        BigDecimal currentValue = (BigDecimal) currentData.getValueByFieldName(code);
        BigDecimal lastValue = (BigDecimal) lastData.getValueByFieldName(code);

        if (Objects.isNull(currentValue) || Objects.isNull(lastValue)) {
            detail.setTrendType(TrendTypeEnum.UNCOMPARABLE.getCode());
            detail.setTrendRate("-");
            return;
        }

        if (currentValue.compareTo(lastValue) == 0 || BigDecimal.ZERO.compareTo(currentValue) == 0 || BigDecimal.ZERO.compareTo(lastValue) == 0) {
            detail.setTrendType(TrendTypeEnum.EQUAL.getCode());
            detail.setTrendRate("0");
        } else if (currentValue.compareTo(lastValue) > 0) {
            detail.setTrendType(TrendTypeEnum.UP.getCode());
            detail.setTrendRate("+" + ThousandSeparatorUtil.format(BigDecimalUtils.calculateIncreaseRate(currentValue, lastValue)) + "%");
        } else {
            detail.setTrendType(TrendTypeEnum.DOWN.getCode());
            detail.setTrendRate(ThousandSeparatorUtil.format(BigDecimalUtils.calculateIncreaseRate(currentValue, lastValue)) + "%");
        }

    }



    private String getBusinessDataDetailValue(AdsAppTradeRevenueDDto data, String code, String unit) {
        if (Objects.isNull(data)) {
            return null;
        }
        Object value = data.getValueByFieldName(code);
        String valueStr = StrUtil.EMPTY;
        if (Objects.nonNull(value)) {
            if (StrUtil.equals("万", unit)) {
                BigDecimal bigDecimalValue = (BigDecimal) value;
                BigDecimal TEN_THOUSAND = new BigDecimal("10000");

                valueStr = ThousandSeparatorUtil.format(bigDecimalValue.divide(TEN_THOUSAND, 2, RoundingMode.HALF_UP));
            } else {
                valueStr = ThousandSeparatorUtil.format(String.valueOf(value));
            }
        }
        return valueStr;
    }

    private void fillAdsAppTradeRevenueDDataValue(AdsAppTradeRevenueDDto data, HomeDataDetailsVo detailsVo, boolean tenThousandCalculate) {
        if (data == null) {
            return;
        }

        String code = detailsVo.getCode();
        Object value = data.getValueByFieldName(code);
        if (Objects.nonNull(value)) {
            BigDecimal bigDecimalValue = BigDecimal.ZERO;
            if (value instanceof BigDecimal) {
                bigDecimalValue = (BigDecimal) value;
            } else if (value instanceof Long) {
                bigDecimalValue = BigDecimal.valueOf((long) value);
            } else {
                // 对于其他类型，直接使用字符串值
                detailsVo.setValue(String.valueOf(value));
                return;
            }

            BigDecimal TEN_THOUSAND = new BigDecimal("10000");

            if (TEN_THOUSAND.compareTo(bigDecimalValue) <= 0 && tenThousandCalculate) {
                // 千分位
                detailsVo.setValue(ThousandSeparatorUtil.format(bigDecimalValue.divide(TEN_THOUSAND, 2, RoundingMode.HALF_UP)));
                detailsVo.setUnit("万");
            } else {
                detailsVo.setValue(ThousandSeparatorUtil.format(bigDecimalValue));
            }
        }
    }

    private void fillAdsHotelPerformanceHDTODataValue(AdsHotelPerformanceHDTO data, HomeDataDetailsVo detailsVo) {
        if (data == null) {
            return;
        }

        String code = detailsVo.getCode();
        Object value = data.getValueByFieldName(code);
        if (Objects.nonNull(value)) {
            BigDecimal bigDecimalValue = BigDecimal.ZERO;
            if (value instanceof Double) {
                bigDecimalValue = BigDecimal.valueOf((double) value);
            } else if (value instanceof Long){
                bigDecimalValue = BigDecimal.valueOf((long) value);
            } else {
                // 对于其他类型，直接使用字符串值
                detailsVo.setValue(String.valueOf(value));
                return;
            }
            // 千分位
            detailsVo.setValue(ThousandSeparatorUtil.format(bigDecimalValue));
        }
    }

    /**
     * 设置趋势率
     * trendType Integer 增或降（-1 ： 降 0：平 1：增 2：无法比较 ）
     * trendRate String  增降幅(-4.01%)
     * 1. 若往期无数据，增降幅显示"-"并置灰。接口返回currentValue或lastValue：null，trendType：2 trendRate：-
     * 2. 若本期/往期值为零，增降幅显示"0"。接口返回currentValue或lastValue：0，trendType：0 trendRate：0
     * @param currentData 当前数据
     * @param lastData    上期数据
     * @param detailsVo   指标详情
     */
    private void setBriefBusinessDataTrendRate(AdsAppTradeRevenueDDto currentData, AdsAppTradeRevenueDDto lastData, HomeDataDetailsVo detailsVo) {
        if (Objects.isNull(currentData) || Objects.isNull(lastData)) {
            detailsVo.setTrendType(TrendTypeEnum.UNCOMPARABLE.getCode());
            detailsVo.setTrendRate("-");
            return;
        }

        BigDecimal currentValue = (BigDecimal) currentData.getValueByFieldName(detailsVo.getCode());
        BigDecimal lastValue = (BigDecimal) lastData.getValueByFieldName(detailsVo.getCode());

        if (Objects.isNull(currentValue) || Objects.isNull(lastValue)) {
            detailsVo.setTrendType(TrendTypeEnum.UNCOMPARABLE.getCode());
            detailsVo.setTrendRate("-");
            return;
        }
        if (currentValue.compareTo(lastValue) == 0 || BigDecimal.ZERO.compareTo(currentValue) == 0 || BigDecimal.ZERO.compareTo(lastValue) == 0) {
            detailsVo.setTrendType(TrendTypeEnum.EQUAL.getCode());
            detailsVo.setTrendRate("0");
        } else if (currentValue.compareTo(lastValue) > 0) {
            detailsVo.setTrendType(TrendTypeEnum.UP.getCode());
            BigDecimal rate = BigDecimalUtils.calculateIncreaseRate(currentValue, lastValue);
            // 设置千分位
            detailsVo.setTrendRate("+" + ThousandSeparatorUtil.format(rate) + "%");
        } else {
            detailsVo.setTrendType(TrendTypeEnum.DOWN.getCode());
            BigDecimal rate = BigDecimalUtils.calculateIncreaseRate(currentValue, lastValue);
            detailsVo.setTrendRate(ThousandSeparatorUtil.format(rate) + "%");
        }


    }
}
