package com.shands.mod.main.controller.proprietor;

import com.shands.mod.dao.model.proprietor.VerifyProprietorMobilesReq;
import com.shands.mod.dao.model.proprietor.VerifyProprietorMobilesRes;
import com.shands.mod.main.service.proprietor.ProprietorService;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 业主接口
 */
@RestController
@RequestMapping(value = "/proprietor")
@Api(value = "业主接口")
@Slf4j
public class ProprietorController {

    @Autowired
    private ProprietorService proprietorService;

    /**
     * 批量校验手机号是否属于业主
     *
     * @param req 包含需要校验的手机号列表的请求
     * @return 包含手机号与业主状态映射的响应
     */
    @PostMapping("/verifyByMobiles")
    @ApiOperation(value = "批量检查手机号是否属于业主", notes = "返回手机号与业主状态的映射（true表示是业主，false表示不是业主）")
    public ResultVO<List<VerifyProprietorMobilesRes>> checkProprietorMobiles(@Valid @RequestBody VerifyProprietorMobilesReq req) {

        List<VerifyProprietorMobilesRes> verifyResList = proprietorService.verifyProprietorMobiles(req.getMobileAndAreaCodeList());
        return ResultVO.success(verifyResList);
    }
}
