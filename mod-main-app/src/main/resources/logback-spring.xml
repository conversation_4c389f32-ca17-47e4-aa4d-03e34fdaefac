<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
  <springProperty name="LOG_PATH" source="trace.filter.logPath" defaultValue="/var/delonix/logs/local/"/>
  <springProperty name="PROJECT_NAME" source="spring.application.name" defaultValue="mod-main-app"/>

  <!-- 控制台输出 -->
  <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <withJansi>true</withJansi>
    <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [${PROJECT_NAME}] [%X{trace-id}] [%X{spanId}] [%thread] %highlight([%-5level] %logger{50}:%L - %msg%n)</pattern>
      <charset>UTF-8</charset>
    </encoder>
  </appender>

  <!-- 系统日志文件 -->
  <appender name="LOG-APPLICATION" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_PATH}/${PROJECT_NAME}.sout.log</file>
    <append>true</append>
    <prudent>false</prudent>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <FileNamePattern>${LOG_PATH}/${PROJECT_NAME}.sout.%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
      <MaxHistory>3</MaxHistory>
      <MaxFileSize>500MB</MaxFileSize>
    </rollingPolicy>
    <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [${PROJECT_NAME}] [%X{trace-id}] [%X{spanId}] [%thread] [%-5level] %logger{50}:%L - %msg%n</pattern>
      <charset>UTF-8</charset>
    </encoder>
    <!-- 记录INFO及以上级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>INFO</level>
    </filter>
  </appender>
  <!--配置异步打印日志-->
  <appender name="ASYNC_LOG-APPLICATION" class="ch.qos.logback.classic.AsyncAppender">
    <discardingThreshold>0</discardingThreshold>
    <queueSize>1024</queueSize>
    <neverBlock>true</neverBlock>
    <appender-ref ref="LOG-APPLICATION"/>
    <includeCallerData>true</includeCallerData>
  </appender>

  <!-- access日志文件 -->
  <appender name="LOG-ACCESS" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_PATH}/${PROJECT_NAME}.access.log</file>
    <prudent>false</prudent>
    <append>true</append>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <FileNamePattern>${LOG_PATH}/${PROJECT_NAME}.access.%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
      <MaxHistory>3</MaxHistory>
      <MaxFileSize>500MB</MaxFileSize>
    </rollingPolicy>
    <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
      <pattern>%msg%n</pattern>
      <charset>UTF-8</charset>
    </encoder>
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>INFO</level>
    </filter>
  </appender>
  <!--配置异步打印日志-->
  <appender name="ASYNC_LOG-ACCESS" class="ch.qos.logback.classic.AsyncAppender">
    <discardingThreshold>0</discardingThreshold>
    <queueSize>1024</queueSize>
    <neverBlock>true</neverBlock>
    <appender-ref ref="LOG-ACCESS"/>
    <includeCallerData>true</includeCallerData>
  </appender>

  <!-- @ResultLog 日志文件输出 -->
  <appender name="RESULT_LOG_BASE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_PATH}/${PROJECT_NAME}.result.log</file>
    <!--只输出INFO-->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>INFO</level>
    </filter>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <!--日志文件输出的文件名-->
      <fileNamePattern>${LOG_PATH}/${PROJECT_NAME}.%d{yyyy-MM-dd}.%i.result.log.gz</fileNamePattern>
      <!-- 服务器保存日志最大的历史 -->
      <maxHistory>3</maxHistory>
      <!--日志文件最大的大小-->
      <MaxFileSize>500MB</MaxFileSize>
    </rollingPolicy>
    <!--日志输出的文件的格式 -->
    <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
      <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
      <pattern>%msg%n</pattern>
      <charset>UTF-8</charset>
    </encoder>
  </appender>
  <!--配置异步打印日志-->
  <appender name="ASYNC_RESULT_LOG" class="ch.qos.logback.classic.AsyncAppender">
    <discardingThreshold>0</discardingThreshold>
    <queueSize>1024</queueSize>
    <neverBlock>true</neverBlock>
    <appender-ref ref="RESULT_LOG_BASE"/>
    <includeCallerData>true</includeCallerData>
  </appender>


  <!-- 关闭日志 -->
  <logger name="com.alibaba.nacos.client.naming" level="OFF"/>

  <!-- 只记录access日志 -->
  <logger name="com.betterwood.log.core.util.AccessLogUtil" level="INFO" additivity="false">
    <appender-ref ref="LOG-ACCESS" />
  </logger>
  <!-- @ResultLog日志指定输出 -->
<!--  <logger name="RESULT_LOG" level="INFO" additivity="false">-->
<!--    <appender-ref ref="ASYNC_RESULT_LOG"/>-->
<!--  </logger>-->

  <root level="INFO">
    <appender-ref ref="CONSOLE" />
    <appender-ref ref="LOG-APPLICATION" />
  </root>

</configuration>
