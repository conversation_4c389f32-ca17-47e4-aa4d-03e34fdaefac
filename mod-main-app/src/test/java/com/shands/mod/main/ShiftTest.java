package com.shands.mod.main;

import com.shands.mod.dao.model.shift.ShiftAcceptUserReq;
import com.shands.mod.main.service.shift.ModShiftAllServiceRoomService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/10 16:53
 */

@SpringBootTest
@RunWith(SpringRunner.class)
public class ShiftTest {

  @Resource
  private ModShiftAllServiceRoomService modShiftAllServiceRoomService;
  @Test
  public void testGetTenantAccessToken() {
    ShiftAcceptUserReq shiftAcceptUserReq = new ShiftAcceptUserReq();
    shiftAcceptUserReq.setShiftId(37);
    List<Integer> list = modShiftAllServiceRoomService.getShiftAcceptUserInfoList(
        shiftAcceptUserReq);
    System.out.println(list);
  }
}
