package com.delonix.bi.dao.model;

import java.io.Serializable;
import java.util.Date;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 德胧生态APP-员工注册日报
 */
@Data
@NoArgsConstructor
public class AdsMemDevelopDetails implements Serializable {

  private String memberId;

  private Date bizDate;

  private String hotelCode;

  private String hotelName;

  private String ucId;

  private String saleId;

  private String memberPhone;

  private Date developTime;
  /**
   * 小程序/APP
   */
  private String clientType;

  private Date updateTime;

  private static final long serialVersionUID = 1L;
}