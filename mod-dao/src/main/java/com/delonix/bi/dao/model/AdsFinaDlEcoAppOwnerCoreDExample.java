package com.delonix.bi.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class AdsFinaDlEcoAppOwnerCoreDExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public AdsFinaDlEcoAppOwnerCoreDExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andHotelCodeIsNull() {
            addCriterion("hotel_code is null");
            return (Criteria) this;
        }

        public Criteria andHotelCodeIsNotNull() {
            addCriterion("hotel_code is not null");
            return (Criteria) this;
        }

        public Criteria andHotelCodeEqualTo(String value) {
            addCriterion("hotel_code =", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeNotEqualTo(String value) {
            addCriterion("hotel_code <>", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeGreaterThan(String value) {
            addCriterion("hotel_code >", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeGreaterThanOrEqualTo(String value) {
            addCriterion("hotel_code >=", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeLessThan(String value) {
            addCriterion("hotel_code <", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeLessThanOrEqualTo(String value) {
            addCriterion("hotel_code <=", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeLike(String value) {
            addCriterion("hotel_code like", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeNotLike(String value) {
            addCriterion("hotel_code not like", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeIn(List<String> values) {
            addCriterion("hotel_code in", values, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeNotIn(List<String> values) {
            addCriterion("hotel_code not in", values, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeBetween(String value1, String value2) {
            addCriterion("hotel_code between", value1, value2, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeNotBetween(String value1, String value2) {
            addCriterion("hotel_code not between", value1, value2, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andBizDateIsNull() {
            addCriterion("biz_date is null");
            return (Criteria) this;
        }

        public Criteria andBizDateIsNotNull() {
            addCriterion("biz_date is not null");
            return (Criteria) this;
        }

        public Criteria andBizDateEqualTo(Date value) {
            addCriterionForJDBCDate("biz_date =", value, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("biz_date <>", value, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateGreaterThan(Date value) {
            addCriterionForJDBCDate("biz_date >", value, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("biz_date >=", value, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateLessThan(Date value) {
            addCriterionForJDBCDate("biz_date <", value, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("biz_date <=", value, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateIn(List<Date> values) {
            addCriterionForJDBCDate("biz_date in", values, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("biz_date not in", values, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("biz_date between", value1, value2, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("biz_date not between", value1, value2, "bizDate");
            return (Criteria) this;
        }

        public Criteria andRoomAmtIsNull() {
            addCriterion("room_amt is null");
            return (Criteria) this;
        }

        public Criteria andRoomAmtIsNotNull() {
            addCriterion("room_amt is not null");
            return (Criteria) this;
        }

        public Criteria andRoomAmtEqualTo(Double value) {
            addCriterion("room_amt =", value, "roomAmt");
            return (Criteria) this;
        }

        public Criteria andRoomAmtNotEqualTo(Double value) {
            addCriterion("room_amt <>", value, "roomAmt");
            return (Criteria) this;
        }

        public Criteria andRoomAmtGreaterThan(Double value) {
            addCriterion("room_amt >", value, "roomAmt");
            return (Criteria) this;
        }

        public Criteria andRoomAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("room_amt >=", value, "roomAmt");
            return (Criteria) this;
        }

        public Criteria andRoomAmtLessThan(Double value) {
            addCriterion("room_amt <", value, "roomAmt");
            return (Criteria) this;
        }

        public Criteria andRoomAmtLessThanOrEqualTo(Double value) {
            addCriterion("room_amt <=", value, "roomAmt");
            return (Criteria) this;
        }

        public Criteria andRoomAmtIn(List<Double> values) {
            addCriterion("room_amt in", values, "roomAmt");
            return (Criteria) this;
        }

        public Criteria andRoomAmtNotIn(List<Double> values) {
            addCriterion("room_amt not in", values, "roomAmt");
            return (Criteria) this;
        }

        public Criteria andRoomAmtBetween(Double value1, Double value2) {
            addCriterion("room_amt between", value1, value2, "roomAmt");
            return (Criteria) this;
        }

        public Criteria andRoomAmtNotBetween(Double value1, Double value2) {
            addCriterion("room_amt not between", value1, value2, "roomAmt");
            return (Criteria) this;
        }

        public Criteria andRoomNightsNIsNull() {
            addCriterion("room_nights_n is null");
            return (Criteria) this;
        }

        public Criteria andRoomNightsNIsNotNull() {
            addCriterion("room_nights_n is not null");
            return (Criteria) this;
        }

        public Criteria andRoomNightsNEqualTo(Double value) {
            addCriterion("room_nights_n =", value, "roomNightsN");
            return (Criteria) this;
        }

        public Criteria andRoomNightsNNotEqualTo(Double value) {
            addCriterion("room_nights_n <>", value, "roomNightsN");
            return (Criteria) this;
        }

        public Criteria andRoomNightsNGreaterThan(Double value) {
            addCriterion("room_nights_n >", value, "roomNightsN");
            return (Criteria) this;
        }

        public Criteria andRoomNightsNGreaterThanOrEqualTo(Double value) {
            addCriterion("room_nights_n >=", value, "roomNightsN");
            return (Criteria) this;
        }

        public Criteria andRoomNightsNLessThan(Double value) {
            addCriterion("room_nights_n <", value, "roomNightsN");
            return (Criteria) this;
        }

        public Criteria andRoomNightsNLessThanOrEqualTo(Double value) {
            addCriterion("room_nights_n <=", value, "roomNightsN");
            return (Criteria) this;
        }

        public Criteria andRoomNightsNIn(List<Double> values) {
            addCriterion("room_nights_n in", values, "roomNightsN");
            return (Criteria) this;
        }

        public Criteria andRoomNightsNNotIn(List<Double> values) {
            addCriterion("room_nights_n not in", values, "roomNightsN");
            return (Criteria) this;
        }

        public Criteria andRoomNightsNBetween(Double value1, Double value2) {
            addCriterion("room_nights_n between", value1, value2, "roomNightsN");
            return (Criteria) this;
        }

        public Criteria andRoomNightsNNotBetween(Double value1, Double value2) {
            addCriterion("room_nights_n not between", value1, value2, "roomNightsN");
            return (Criteria) this;
        }

        public Criteria andSaleRoomNIsNull() {
            addCriterion("sale_room_n is null");
            return (Criteria) this;
        }

        public Criteria andSaleRoomNIsNotNull() {
            addCriterion("sale_room_n is not null");
            return (Criteria) this;
        }

        public Criteria andSaleRoomNEqualTo(Long value) {
            addCriterion("sale_room_n =", value, "saleRoomN");
            return (Criteria) this;
        }

        public Criteria andSaleRoomNNotEqualTo(Long value) {
            addCriterion("sale_room_n <>", value, "saleRoomN");
            return (Criteria) this;
        }

        public Criteria andSaleRoomNGreaterThan(Long value) {
            addCriterion("sale_room_n >", value, "saleRoomN");
            return (Criteria) this;
        }

        public Criteria andSaleRoomNGreaterThanOrEqualTo(Long value) {
            addCriterion("sale_room_n >=", value, "saleRoomN");
            return (Criteria) this;
        }

        public Criteria andSaleRoomNLessThan(Long value) {
            addCriterion("sale_room_n <", value, "saleRoomN");
            return (Criteria) this;
        }

        public Criteria andSaleRoomNLessThanOrEqualTo(Long value) {
            addCriterion("sale_room_n <=", value, "saleRoomN");
            return (Criteria) this;
        }

        public Criteria andSaleRoomNIn(List<Long> values) {
            addCriterion("sale_room_n in", values, "saleRoomN");
            return (Criteria) this;
        }

        public Criteria andSaleRoomNNotIn(List<Long> values) {
            addCriterion("sale_room_n not in", values, "saleRoomN");
            return (Criteria) this;
        }

        public Criteria andSaleRoomNBetween(Long value1, Long value2) {
            addCriterion("sale_room_n between", value1, value2, "saleRoomN");
            return (Criteria) this;
        }

        public Criteria andSaleRoomNNotBetween(Long value1, Long value2) {
            addCriterion("sale_room_n not between", value1, value2, "saleRoomN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNIsNull() {
            addCriterion("hotel_capacity_n is null");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNIsNotNull() {
            addCriterion("hotel_capacity_n is not null");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNEqualTo(Long value) {
            addCriterion("hotel_capacity_n =", value, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNNotEqualTo(Long value) {
            addCriterion("hotel_capacity_n <>", value, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNGreaterThan(Long value) {
            addCriterion("hotel_capacity_n >", value, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNGreaterThanOrEqualTo(Long value) {
            addCriterion("hotel_capacity_n >=", value, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNLessThan(Long value) {
            addCriterion("hotel_capacity_n <", value, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNLessThanOrEqualTo(Long value) {
            addCriterion("hotel_capacity_n <=", value, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNIn(List<Long> values) {
            addCriterion("hotel_capacity_n in", values, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNNotIn(List<Long> values) {
            addCriterion("hotel_capacity_n not in", values, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNBetween(Long value1, Long value2) {
            addCriterion("hotel_capacity_n between", value1, value2, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNNotBetween(Long value1, Long value2) {
            addCriterion("hotel_capacity_n not between", value1, value2, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andTradeOccIsNull() {
            addCriterion("trade_occ is null");
            return (Criteria) this;
        }

        public Criteria andTradeOccIsNotNull() {
            addCriterion("trade_occ is not null");
            return (Criteria) this;
        }

        public Criteria andTradeOccEqualTo(Double value) {
            addCriterion("trade_occ =", value, "tradeOcc");
            return (Criteria) this;
        }

        public Criteria andTradeOccNotEqualTo(Double value) {
            addCriterion("trade_occ <>", value, "tradeOcc");
            return (Criteria) this;
        }

        public Criteria andTradeOccGreaterThan(Double value) {
            addCriterion("trade_occ >", value, "tradeOcc");
            return (Criteria) this;
        }

        public Criteria andTradeOccGreaterThanOrEqualTo(Double value) {
            addCriterion("trade_occ >=", value, "tradeOcc");
            return (Criteria) this;
        }

        public Criteria andTradeOccLessThan(Double value) {
            addCriterion("trade_occ <", value, "tradeOcc");
            return (Criteria) this;
        }

        public Criteria andTradeOccLessThanOrEqualTo(Double value) {
            addCriterion("trade_occ <=", value, "tradeOcc");
            return (Criteria) this;
        }

        public Criteria andTradeOccIn(List<Double> values) {
            addCriterion("trade_occ in", values, "tradeOcc");
            return (Criteria) this;
        }

        public Criteria andTradeOccNotIn(List<Double> values) {
            addCriterion("trade_occ not in", values, "tradeOcc");
            return (Criteria) this;
        }

        public Criteria andTradeOccBetween(Double value1, Double value2) {
            addCriterion("trade_occ between", value1, value2, "tradeOcc");
            return (Criteria) this;
        }

        public Criteria andTradeOccNotBetween(Double value1, Double value2) {
            addCriterion("trade_occ not between", value1, value2, "tradeOcc");
            return (Criteria) this;
        }

        public Criteria andTradeAdrIsNull() {
            addCriterion("trade_adr is null");
            return (Criteria) this;
        }

        public Criteria andTradeAdrIsNotNull() {
            addCriterion("trade_adr is not null");
            return (Criteria) this;
        }

        public Criteria andTradeAdrEqualTo(Double value) {
            addCriterion("trade_adr =", value, "tradeAdr");
            return (Criteria) this;
        }

        public Criteria andTradeAdrNotEqualTo(Double value) {
            addCriterion("trade_adr <>", value, "tradeAdr");
            return (Criteria) this;
        }

        public Criteria andTradeAdrGreaterThan(Double value) {
            addCriterion("trade_adr >", value, "tradeAdr");
            return (Criteria) this;
        }

        public Criteria andTradeAdrGreaterThanOrEqualTo(Double value) {
            addCriterion("trade_adr >=", value, "tradeAdr");
            return (Criteria) this;
        }

        public Criteria andTradeAdrLessThan(Double value) {
            addCriterion("trade_adr <", value, "tradeAdr");
            return (Criteria) this;
        }

        public Criteria andTradeAdrLessThanOrEqualTo(Double value) {
            addCriterion("trade_adr <=", value, "tradeAdr");
            return (Criteria) this;
        }

        public Criteria andTradeAdrIn(List<Double> values) {
            addCriterion("trade_adr in", values, "tradeAdr");
            return (Criteria) this;
        }

        public Criteria andTradeAdrNotIn(List<Double> values) {
            addCriterion("trade_adr not in", values, "tradeAdr");
            return (Criteria) this;
        }

        public Criteria andTradeAdrBetween(Double value1, Double value2) {
            addCriterion("trade_adr between", value1, value2, "tradeAdr");
            return (Criteria) this;
        }

        public Criteria andTradeAdrNotBetween(Double value1, Double value2) {
            addCriterion("trade_adr not between", value1, value2, "tradeAdr");
            return (Criteria) this;
        }

        public Criteria andTradeRevparIsNull() {
            addCriterion("trade_revpar is null");
            return (Criteria) this;
        }

        public Criteria andTradeRevparIsNotNull() {
            addCriterion("trade_revpar is not null");
            return (Criteria) this;
        }

        public Criteria andTradeRevparEqualTo(Double value) {
            addCriterion("trade_revpar =", value, "tradeRevpar");
            return (Criteria) this;
        }

        public Criteria andTradeRevparNotEqualTo(Double value) {
            addCriterion("trade_revpar <>", value, "tradeRevpar");
            return (Criteria) this;
        }

        public Criteria andTradeRevparGreaterThan(Double value) {
            addCriterion("trade_revpar >", value, "tradeRevpar");
            return (Criteria) this;
        }

        public Criteria andTradeRevparGreaterThanOrEqualTo(Double value) {
            addCriterion("trade_revpar >=", value, "tradeRevpar");
            return (Criteria) this;
        }

        public Criteria andTradeRevparLessThan(Double value) {
            addCriterion("trade_revpar <", value, "tradeRevpar");
            return (Criteria) this;
        }

        public Criteria andTradeRevparLessThanOrEqualTo(Double value) {
            addCriterion("trade_revpar <=", value, "tradeRevpar");
            return (Criteria) this;
        }

        public Criteria andTradeRevparIn(List<Double> values) {
            addCriterion("trade_revpar in", values, "tradeRevpar");
            return (Criteria) this;
        }

        public Criteria andTradeRevparNotIn(List<Double> values) {
            addCriterion("trade_revpar not in", values, "tradeRevpar");
            return (Criteria) this;
        }

        public Criteria andTradeRevparBetween(Double value1, Double value2) {
            addCriterion("trade_revpar between", value1, value2, "tradeRevpar");
            return (Criteria) this;
        }

        public Criteria andTradeRevparNotBetween(Double value1, Double value2) {
            addCriterion("trade_revpar not between", value1, value2, "tradeRevpar");
            return (Criteria) this;
        }

        public Criteria andCrsRoomNightsNIsNull() {
            addCriterion("crs_room_nights_n is null");
            return (Criteria) this;
        }

        public Criteria andCrsRoomNightsNIsNotNull() {
            addCriterion("crs_room_nights_n is not null");
            return (Criteria) this;
        }

        public Criteria andCrsRoomNightsNEqualTo(Double value) {
            addCriterion("crs_room_nights_n =", value, "crsRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andCrsRoomNightsNNotEqualTo(Double value) {
            addCriterion("crs_room_nights_n <>", value, "crsRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andCrsRoomNightsNGreaterThan(Double value) {
            addCriterion("crs_room_nights_n >", value, "crsRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andCrsRoomNightsNGreaterThanOrEqualTo(Double value) {
            addCriterion("crs_room_nights_n >=", value, "crsRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andCrsRoomNightsNLessThan(Double value) {
            addCriterion("crs_room_nights_n <", value, "crsRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andCrsRoomNightsNLessThanOrEqualTo(Double value) {
            addCriterion("crs_room_nights_n <=", value, "crsRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andCrsRoomNightsNIn(List<Double> values) {
            addCriterion("crs_room_nights_n in", values, "crsRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andCrsRoomNightsNNotIn(List<Double> values) {
            addCriterion("crs_room_nights_n not in", values, "crsRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andCrsRoomNightsNBetween(Double value1, Double value2) {
            addCriterion("crs_room_nights_n between", value1, value2, "crsRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andCrsRoomNightsNNotBetween(Double value1, Double value2) {
            addCriterion("crs_room_nights_n not between", value1, value2, "crsRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andOfflineRoomNightsNIsNull() {
            addCriterion("offline_room_nights_n is null");
            return (Criteria) this;
        }

        public Criteria andOfflineRoomNightsNIsNotNull() {
            addCriterion("offline_room_nights_n is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineRoomNightsNEqualTo(Double value) {
            addCriterion("offline_room_nights_n =", value, "offlineRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andOfflineRoomNightsNNotEqualTo(Double value) {
            addCriterion("offline_room_nights_n <>", value, "offlineRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andOfflineRoomNightsNGreaterThan(Double value) {
            addCriterion("offline_room_nights_n >", value, "offlineRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andOfflineRoomNightsNGreaterThanOrEqualTo(Double value) {
            addCriterion("offline_room_nights_n >=", value, "offlineRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andOfflineRoomNightsNLessThan(Double value) {
            addCriterion("offline_room_nights_n <", value, "offlineRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andOfflineRoomNightsNLessThanOrEqualTo(Double value) {
            addCriterion("offline_room_nights_n <=", value, "offlineRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andOfflineRoomNightsNIn(List<Double> values) {
            addCriterion("offline_room_nights_n in", values, "offlineRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andOfflineRoomNightsNNotIn(List<Double> values) {
            addCriterion("offline_room_nights_n not in", values, "offlineRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andOfflineRoomNightsNBetween(Double value1, Double value2) {
            addCriterion("offline_room_nights_n between", value1, value2, "offlineRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andOfflineRoomNightsNNotBetween(Double value1, Double value2) {
            addCriterion("offline_room_nights_n not between", value1, value2, "offlineRoomNightsN");
            return (Criteria) this;
        }

        public Criteria andTotalAmtIsNull() {
            addCriterion("total_amt is null");
            return (Criteria) this;
        }

        public Criteria andTotalAmtIsNotNull() {
            addCriterion("total_amt is not null");
            return (Criteria) this;
        }

        public Criteria andTotalAmtEqualTo(Double value) {
            addCriterion("total_amt =", value, "totalAmt");
            return (Criteria) this;
        }

        public Criteria andTotalAmtNotEqualTo(Double value) {
            addCriterion("total_amt <>", value, "totalAmt");
            return (Criteria) this;
        }

        public Criteria andTotalAmtGreaterThan(Double value) {
            addCriterion("total_amt >", value, "totalAmt");
            return (Criteria) this;
        }

        public Criteria andTotalAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("total_amt >=", value, "totalAmt");
            return (Criteria) this;
        }

        public Criteria andTotalAmtLessThan(Double value) {
            addCriterion("total_amt <", value, "totalAmt");
            return (Criteria) this;
        }

        public Criteria andTotalAmtLessThanOrEqualTo(Double value) {
            addCriterion("total_amt <=", value, "totalAmt");
            return (Criteria) this;
        }

        public Criteria andTotalAmtIn(List<Double> values) {
            addCriterion("total_amt in", values, "totalAmt");
            return (Criteria) this;
        }

        public Criteria andTotalAmtNotIn(List<Double> values) {
            addCriterion("total_amt not in", values, "totalAmt");
            return (Criteria) this;
        }

        public Criteria andTotalAmtBetween(Double value1, Double value2) {
            addCriterion("total_amt between", value1, value2, "totalAmt");
            return (Criteria) this;
        }

        public Criteria andTotalAmtNotBetween(Double value1, Double value2) {
            addCriterion("total_amt not between", value1, value2, "totalAmt");
            return (Criteria) this;
        }

        public Criteria andTotalRoomAmtIsNull() {
            addCriterion("total_room_amt is null");
            return (Criteria) this;
        }

        public Criteria andTotalRoomAmtIsNotNull() {
            addCriterion("total_room_amt is not null");
            return (Criteria) this;
        }

        public Criteria andTotalRoomAmtEqualTo(Double value) {
            addCriterion("total_room_amt =", value, "totalRoomAmt");
            return (Criteria) this;
        }

        public Criteria andTotalRoomAmtNotEqualTo(Double value) {
            addCriterion("total_room_amt <>", value, "totalRoomAmt");
            return (Criteria) this;
        }

        public Criteria andTotalRoomAmtGreaterThan(Double value) {
            addCriterion("total_room_amt >", value, "totalRoomAmt");
            return (Criteria) this;
        }

        public Criteria andTotalRoomAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("total_room_amt >=", value, "totalRoomAmt");
            return (Criteria) this;
        }

        public Criteria andTotalRoomAmtLessThan(Double value) {
            addCriterion("total_room_amt <", value, "totalRoomAmt");
            return (Criteria) this;
        }

        public Criteria andTotalRoomAmtLessThanOrEqualTo(Double value) {
            addCriterion("total_room_amt <=", value, "totalRoomAmt");
            return (Criteria) this;
        }

        public Criteria andTotalRoomAmtIn(List<Double> values) {
            addCriterion("total_room_amt in", values, "totalRoomAmt");
            return (Criteria) this;
        }

        public Criteria andTotalRoomAmtNotIn(List<Double> values) {
            addCriterion("total_room_amt not in", values, "totalRoomAmt");
            return (Criteria) this;
        }

        public Criteria andTotalRoomAmtBetween(Double value1, Double value2) {
            addCriterion("total_room_amt between", value1, value2, "totalRoomAmt");
            return (Criteria) this;
        }

        public Criteria andTotalRoomAmtNotBetween(Double value1, Double value2) {
            addCriterion("total_room_amt not between", value1, value2, "totalRoomAmt");
            return (Criteria) this;
        }

        public Criteria andCateringAmtIsNull() {
            addCriterion("catering_amt is null");
            return (Criteria) this;
        }

        public Criteria andCateringAmtIsNotNull() {
            addCriterion("catering_amt is not null");
            return (Criteria) this;
        }

        public Criteria andCateringAmtEqualTo(Double value) {
            addCriterion("catering_amt =", value, "cateringAmt");
            return (Criteria) this;
        }

        public Criteria andCateringAmtNotEqualTo(Double value) {
            addCriterion("catering_amt <>", value, "cateringAmt");
            return (Criteria) this;
        }

        public Criteria andCateringAmtGreaterThan(Double value) {
            addCriterion("catering_amt >", value, "cateringAmt");
            return (Criteria) this;
        }

        public Criteria andCateringAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("catering_amt >=", value, "cateringAmt");
            return (Criteria) this;
        }

        public Criteria andCateringAmtLessThan(Double value) {
            addCriterion("catering_amt <", value, "cateringAmt");
            return (Criteria) this;
        }

        public Criteria andCateringAmtLessThanOrEqualTo(Double value) {
            addCriterion("catering_amt <=", value, "cateringAmt");
            return (Criteria) this;
        }

        public Criteria andCateringAmtIn(List<Double> values) {
            addCriterion("catering_amt in", values, "cateringAmt");
            return (Criteria) this;
        }

        public Criteria andCateringAmtNotIn(List<Double> values) {
            addCriterion("catering_amt not in", values, "cateringAmt");
            return (Criteria) this;
        }

        public Criteria andCateringAmtBetween(Double value1, Double value2) {
            addCriterion("catering_amt between", value1, value2, "cateringAmt");
            return (Criteria) this;
        }

        public Criteria andCateringAmtNotBetween(Double value1, Double value2) {
            addCriterion("catering_amt not between", value1, value2, "cateringAmt");
            return (Criteria) this;
        }

        public Criteria andOtherAmtIsNull() {
            addCriterion("other_amt is null");
            return (Criteria) this;
        }

        public Criteria andOtherAmtIsNotNull() {
            addCriterion("other_amt is not null");
            return (Criteria) this;
        }

        public Criteria andOtherAmtEqualTo(Double value) {
            addCriterion("other_amt =", value, "otherAmt");
            return (Criteria) this;
        }

        public Criteria andOtherAmtNotEqualTo(Double value) {
            addCriterion("other_amt <>", value, "otherAmt");
            return (Criteria) this;
        }

        public Criteria andOtherAmtGreaterThan(Double value) {
            addCriterion("other_amt >", value, "otherAmt");
            return (Criteria) this;
        }

        public Criteria andOtherAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("other_amt >=", value, "otherAmt");
            return (Criteria) this;
        }

        public Criteria andOtherAmtLessThan(Double value) {
            addCriterion("other_amt <", value, "otherAmt");
            return (Criteria) this;
        }

        public Criteria andOtherAmtLessThanOrEqualTo(Double value) {
            addCriterion("other_amt <=", value, "otherAmt");
            return (Criteria) this;
        }

        public Criteria andOtherAmtIn(List<Double> values) {
            addCriterion("other_amt in", values, "otherAmt");
            return (Criteria) this;
        }

        public Criteria andOtherAmtNotIn(List<Double> values) {
            addCriterion("other_amt not in", values, "otherAmt");
            return (Criteria) this;
        }

        public Criteria andOtherAmtBetween(Double value1, Double value2) {
            addCriterion("other_amt between", value1, value2, "otherAmt");
            return (Criteria) this;
        }

        public Criteria andOtherAmtNotBetween(Double value1, Double value2) {
            addCriterion("other_amt not between", value1, value2, "otherAmt");
            return (Criteria) this;
        }

        public Criteria andBudgetTotalAmtIsNull() {
            addCriterion("budget_total_amt is null");
            return (Criteria) this;
        }

        public Criteria andBudgetTotalAmtIsNotNull() {
            addCriterion("budget_total_amt is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetTotalAmtEqualTo(Double value) {
            addCriterion("budget_total_amt =", value, "budgetTotalAmt");
            return (Criteria) this;
        }

        public Criteria andBudgetTotalAmtNotEqualTo(Double value) {
            addCriterion("budget_total_amt <>", value, "budgetTotalAmt");
            return (Criteria) this;
        }

        public Criteria andBudgetTotalAmtGreaterThan(Double value) {
            addCriterion("budget_total_amt >", value, "budgetTotalAmt");
            return (Criteria) this;
        }

        public Criteria andBudgetTotalAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("budget_total_amt >=", value, "budgetTotalAmt");
            return (Criteria) this;
        }

        public Criteria andBudgetTotalAmtLessThan(Double value) {
            addCriterion("budget_total_amt <", value, "budgetTotalAmt");
            return (Criteria) this;
        }

        public Criteria andBudgetTotalAmtLessThanOrEqualTo(Double value) {
            addCriterion("budget_total_amt <=", value, "budgetTotalAmt");
            return (Criteria) this;
        }

        public Criteria andBudgetTotalAmtIn(List<Double> values) {
            addCriterion("budget_total_amt in", values, "budgetTotalAmt");
            return (Criteria) this;
        }

        public Criteria andBudgetTotalAmtNotIn(List<Double> values) {
            addCriterion("budget_total_amt not in", values, "budgetTotalAmt");
            return (Criteria) this;
        }

        public Criteria andBudgetTotalAmtBetween(Double value1, Double value2) {
            addCriterion("budget_total_amt between", value1, value2, "budgetTotalAmt");
            return (Criteria) this;
        }

        public Criteria andBudgetTotalAmtNotBetween(Double value1, Double value2) {
            addCriterion("budget_total_amt not between", value1, value2, "budgetTotalAmt");
            return (Criteria) this;
        }

        public Criteria andBdwCardDivideAmtIsNull() {
            addCriterion("bdw_card_divide_amt is null");
            return (Criteria) this;
        }

        public Criteria andBdwCardDivideAmtIsNotNull() {
            addCriterion("bdw_card_divide_amt is not null");
            return (Criteria) this;
        }

        public Criteria andBdwCardDivideAmtEqualTo(Double value) {
            addCriterion("bdw_card_divide_amt =", value, "bdwCardDivideAmt");
            return (Criteria) this;
        }

        public Criteria andBdwCardDivideAmtNotEqualTo(Double value) {
            addCriterion("bdw_card_divide_amt <>", value, "bdwCardDivideAmt");
            return (Criteria) this;
        }

        public Criteria andBdwCardDivideAmtGreaterThan(Double value) {
            addCriterion("bdw_card_divide_amt >", value, "bdwCardDivideAmt");
            return (Criteria) this;
        }

        public Criteria andBdwCardDivideAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("bdw_card_divide_amt >=", value, "bdwCardDivideAmt");
            return (Criteria) this;
        }

        public Criteria andBdwCardDivideAmtLessThan(Double value) {
            addCriterion("bdw_card_divide_amt <", value, "bdwCardDivideAmt");
            return (Criteria) this;
        }

        public Criteria andBdwCardDivideAmtLessThanOrEqualTo(Double value) {
            addCriterion("bdw_card_divide_amt <=", value, "bdwCardDivideAmt");
            return (Criteria) this;
        }

        public Criteria andBdwCardDivideAmtIn(List<Double> values) {
            addCriterion("bdw_card_divide_amt in", values, "bdwCardDivideAmt");
            return (Criteria) this;
        }

        public Criteria andBdwCardDivideAmtNotIn(List<Double> values) {
            addCriterion("bdw_card_divide_amt not in", values, "bdwCardDivideAmt");
            return (Criteria) this;
        }

        public Criteria andBdwCardDivideAmtBetween(Double value1, Double value2) {
            addCriterion("bdw_card_divide_amt between", value1, value2, "bdwCardDivideAmt");
            return (Criteria) this;
        }

        public Criteria andBdwCardDivideAmtNotBetween(Double value1, Double value2) {
            addCriterion("bdw_card_divide_amt not between", value1, value2, "bdwCardDivideAmt");
            return (Criteria) this;
        }

        public Criteria andArrMemNightsNIsNull() {
            addCriterion("arr_mem_nights_n is null");
            return (Criteria) this;
        }

        public Criteria andArrMemNightsNIsNotNull() {
            addCriterion("arr_mem_nights_n is not null");
            return (Criteria) this;
        }

        public Criteria andArrMemNightsNEqualTo(Double value) {
            addCriterion("arr_mem_nights_n =", value, "arrMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrMemNightsNNotEqualTo(Double value) {
            addCriterion("arr_mem_nights_n <>", value, "arrMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrMemNightsNGreaterThan(Double value) {
            addCriterion("arr_mem_nights_n >", value, "arrMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrMemNightsNGreaterThanOrEqualTo(Double value) {
            addCriterion("arr_mem_nights_n >=", value, "arrMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrMemNightsNLessThan(Double value) {
            addCriterion("arr_mem_nights_n <", value, "arrMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrMemNightsNLessThanOrEqualTo(Double value) {
            addCriterion("arr_mem_nights_n <=", value, "arrMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrMemNightsNIn(List<Double> values) {
            addCriterion("arr_mem_nights_n in", values, "arrMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrMemNightsNNotIn(List<Double> values) {
            addCriterion("arr_mem_nights_n not in", values, "arrMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrMemNightsNBetween(Double value1, Double value2) {
            addCriterion("arr_mem_nights_n between", value1, value2, "arrMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrMemNightsNNotBetween(Double value1, Double value2) {
            addCriterion("arr_mem_nights_n not between", value1, value2, "arrMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrNonMemNightsNIsNull() {
            addCriterion("arr_non_mem_nights_n is null");
            return (Criteria) this;
        }

        public Criteria andArrNonMemNightsNIsNotNull() {
            addCriterion("arr_non_mem_nights_n is not null");
            return (Criteria) this;
        }

        public Criteria andArrNonMemNightsNEqualTo(Double value) {
            addCriterion("arr_non_mem_nights_n =", value, "arrNonMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrNonMemNightsNNotEqualTo(Double value) {
            addCriterion("arr_non_mem_nights_n <>", value, "arrNonMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrNonMemNightsNGreaterThan(Double value) {
            addCriterion("arr_non_mem_nights_n >", value, "arrNonMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrNonMemNightsNGreaterThanOrEqualTo(Double value) {
            addCriterion("arr_non_mem_nights_n >=", value, "arrNonMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrNonMemNightsNLessThan(Double value) {
            addCriterion("arr_non_mem_nights_n <", value, "arrNonMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrNonMemNightsNLessThanOrEqualTo(Double value) {
            addCriterion("arr_non_mem_nights_n <=", value, "arrNonMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrNonMemNightsNIn(List<Double> values) {
            addCriterion("arr_non_mem_nights_n in", values, "arrNonMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrNonMemNightsNNotIn(List<Double> values) {
            addCriterion("arr_non_mem_nights_n not in", values, "arrNonMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrNonMemNightsNBetween(Double value1, Double value2) {
            addCriterion("arr_non_mem_nights_n between", value1, value2, "arrNonMemNightsN");
            return (Criteria) this;
        }

        public Criteria andArrNonMemNightsNNotBetween(Double value1, Double value2) {
            addCriterion("arr_non_mem_nights_n not between", value1, value2, "arrNonMemNightsN");
            return (Criteria) this;
        }

        public Criteria andFirstConsumeMemNIsNull() {
            addCriterion("first_consume_mem_n is null");
            return (Criteria) this;
        }

        public Criteria andFirstConsumeMemNIsNotNull() {
            addCriterion("first_consume_mem_n is not null");
            return (Criteria) this;
        }

        public Criteria andFirstConsumeMemNEqualTo(Long value) {
            addCriterion("first_consume_mem_n =", value, "firstConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andFirstConsumeMemNNotEqualTo(Long value) {
            addCriterion("first_consume_mem_n <>", value, "firstConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andFirstConsumeMemNGreaterThan(Long value) {
            addCriterion("first_consume_mem_n >", value, "firstConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andFirstConsumeMemNGreaterThanOrEqualTo(Long value) {
            addCriterion("first_consume_mem_n >=", value, "firstConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andFirstConsumeMemNLessThan(Long value) {
            addCriterion("first_consume_mem_n <", value, "firstConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andFirstConsumeMemNLessThanOrEqualTo(Long value) {
            addCriterion("first_consume_mem_n <=", value, "firstConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andFirstConsumeMemNIn(List<Long> values) {
            addCriterion("first_consume_mem_n in", values, "firstConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andFirstConsumeMemNNotIn(List<Long> values) {
            addCriterion("first_consume_mem_n not in", values, "firstConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andFirstConsumeMemNBetween(Long value1, Long value2) {
            addCriterion("first_consume_mem_n between", value1, value2, "firstConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andFirstConsumeMemNNotBetween(Long value1, Long value2) {
            addCriterion("first_consume_mem_n not between", value1, value2, "firstConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andReConsumeMemNIsNull() {
            addCriterion("re_consume_mem_n is null");
            return (Criteria) this;
        }

        public Criteria andReConsumeMemNIsNotNull() {
            addCriterion("re_consume_mem_n is not null");
            return (Criteria) this;
        }

        public Criteria andReConsumeMemNEqualTo(Long value) {
            addCriterion("re_consume_mem_n =", value, "reConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andReConsumeMemNNotEqualTo(Long value) {
            addCriterion("re_consume_mem_n <>", value, "reConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andReConsumeMemNGreaterThan(Long value) {
            addCriterion("re_consume_mem_n >", value, "reConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andReConsumeMemNGreaterThanOrEqualTo(Long value) {
            addCriterion("re_consume_mem_n >=", value, "reConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andReConsumeMemNLessThan(Long value) {
            addCriterion("re_consume_mem_n <", value, "reConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andReConsumeMemNLessThanOrEqualTo(Long value) {
            addCriterion("re_consume_mem_n <=", value, "reConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andReConsumeMemNIn(List<Long> values) {
            addCriterion("re_consume_mem_n in", values, "reConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andReConsumeMemNNotIn(List<Long> values) {
            addCriterion("re_consume_mem_n not in", values, "reConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andReConsumeMemNBetween(Long value1, Long value2) {
            addCriterion("re_consume_mem_n between", value1, value2, "reConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andReConsumeMemNNotBetween(Long value1, Long value2) {
            addCriterion("re_consume_mem_n not between", value1, value2, "reConsumeMemN");
            return (Criteria) this;
        }

        public Criteria andDataTimeIsNull() {
            addCriterion("data_time is null");
            return (Criteria) this;
        }

        public Criteria andDataTimeIsNotNull() {
            addCriterion("data_time is not null");
            return (Criteria) this;
        }

        public Criteria andDataTimeEqualTo(Date value) {
            addCriterion("data_time =", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotEqualTo(Date value) {
            addCriterion("data_time <>", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThan(Date value) {
            addCriterion("data_time >", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("data_time >=", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThan(Date value) {
            addCriterion("data_time <", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThanOrEqualTo(Date value) {
            addCriterion("data_time <=", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeIn(List<Date> values) {
            addCriterion("data_time in", values, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotIn(List<Date> values) {
            addCriterion("data_time not in", values, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeBetween(Date value1, Date value2) {
            addCriterion("data_time between", value1, value2, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotBetween(Date value1, Date value2) {
            addCriterion("data_time not between", value1, value2, "dataTime");
            return (Criteria) this;
        }

        public Criteria andHotelCodeLikeInsensitive(String value) {
            addCriterion("upper(hotel_code) like", value.toUpperCase(), "hotelCode");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}