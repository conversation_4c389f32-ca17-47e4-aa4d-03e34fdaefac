package com.delonix.bi.dao.mapper;

import com.delonix.bi.dao.model.AdsTradeDlEcoAppHotelWideD;
import com.delonix.bi.dao.model.AdsTradeDlEcoAppHotelWideDExample;
import com.delonix.bi.dao.model.AdsTradeDlEcoAppHotelWideDKey;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface AdsTradeDlEcoAppHotelWideDMapper {
    long countByExample(AdsTradeDlEcoAppHotelWideDExample example);

    int deleteByExample(AdsTradeDlEcoAppHotelWideDExample example);

    int deleteByPrimaryKey(AdsTradeDlEcoAppHotelWideDKey key);

    int insert(AdsTradeDlEcoAppHotelWideD record);

    int insertSelective(AdsTradeDlEcoAppHotelWideD record);

    List<AdsTradeDlEcoAppHotelWideD> selectByExampleWithRowbounds(AdsTradeDlEcoAppHotelWideDExample example, RowBounds rowBounds);

    List<AdsTradeDlEcoAppHotelWideD> selectByExample(AdsTradeDlEcoAppHotelWideDExample example);

    AdsTradeDlEcoAppHotelWideD selectByPrimaryKey(AdsTradeDlEcoAppHotelWideDKey key);

    int updateByExampleSelective(@Param("record") AdsTradeDlEcoAppHotelWideD record, @Param("example") AdsTradeDlEcoAppHotelWideDExample example);

    int updateByExample(@Param("record") AdsTradeDlEcoAppHotelWideD record, @Param("example") AdsTradeDlEcoAppHotelWideDExample example);

    int updateByPrimaryKeySelective(AdsTradeDlEcoAppHotelWideD record);

    int updateByPrimaryKey(AdsTradeDlEcoAppHotelWideD record);

    void batchInsert(@Param("items") List<AdsTradeDlEcoAppHotelWideD> items);
}