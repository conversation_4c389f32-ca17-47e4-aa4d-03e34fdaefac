package com.shands.mod.dao.model.workorder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
@ApiModel("工单模板数据统计")
public class NwTemplateDataVo {

  @ApiModelProperty(value="模板id",required = true)
  private int templateId;

  @ApiModelProperty(value="模板名称",required = true)
  private String templateName;

  @ApiModelProperty(value="总数",required = true)
  private int total;

  @ApiModelProperty(value="完成数",required = true)
  private int completeNum;

  @ApiModelProperty(value="关闭数",required = true)
  private int closeNum;

  @ApiModelProperty(value="处理中数",required = true)
  private int processNum;

  @ApiModelProperty(value="已处理数",required = true)
  private int inProcessNum;

  @ApiModelProperty(value="解决率",required = true)
  private Double solveRate;
}
