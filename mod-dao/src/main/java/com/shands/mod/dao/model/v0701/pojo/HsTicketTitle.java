package com.shands.mod.dao.model.v0701.pojo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * hs_ticket_title
 * <AUTHOR>
@Data
public class HsTicketTitle implements Serializable {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 标签名称
     */
    private String titleName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 删除标记 1：已删除 0：未删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 酒店编码
     */
    private Integer companyId;

    private static final long serialVersionUID = 1L;
}