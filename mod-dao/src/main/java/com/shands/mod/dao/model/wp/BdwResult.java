package com.shands.mod.dao.model.wp;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class BdwResult<T> {

  private Integer code;

  private String errorCode;

  private String errorDesc;

  private String msg;

  private boolean ok;

  private boolean successWithData;

  private T data;
}
