package com.shands.mod.dao.mapper.hs;

import com.shands.mod.dao.model.hs.WorkOrder;
import com.shands.mod.dao.model.hs.enums.WorkOrderStateEnum;
import com.shands.mod.dao.model.req.Board.CleanRoomDataReq;
import com.shands.mod.dao.model.req.hs.TaskQuery;
import com.shands.mod.dao.model.req.hs.report.CountWorkRes;
import com.shands.mod.dao.model.req.hs.report.StatisticsAppReq;
import com.shands.mod.dao.model.req.hs.report.StatisticsAppRes;
import com.shands.mod.dao.model.req.hs.workorder.DefaultMobileIndexWorkOrderReq;
import com.shands.mod.dao.model.req.hs.workorder.WorkOrderAppQuery;
import com.shands.mod.dao.model.req.hs.workorder.WorkOrderQueryReq;
import com.shands.mod.dao.model.res.hs.report.StatisticBySourceRes;
import com.shands.mod.dao.model.res.hs.workorder.ComlateWorkOrderVo;
import com.shands.mod.dao.model.res.hs.workorder.WorkOrderByIdRes;
import com.shands.mod.dao.model.res.hs.workorder.WorkOrderBySnRes;
import com.shands.mod.dao.model.v0701.vo.WorkDiscountVo;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface WorkOrderMapper {

  int deleteByPrimaryKey(Integer id);

  int insert(WorkOrder record);

  int insertSelective(WorkOrder record);

  WorkOrder selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(WorkOrder record);

  int updateByPrimaryKeySelectiveV2(WorkOrder record);

  int updateByPrimaryKey(WorkOrder record);

  /**
   * 根据来源进行统计
   *
   * @param query
   * @return
   */
  List<StatisticBySourceRes> statisticsBySource(@Param("date") LocalDate date);

  /**
   * 根据订单查询所有工单
   *
   * @param customerOrderId
   * @return
   */
  WorkOrder findByOrderId(@Param("customerOrderId") Integer customerOrderId);

  /**
   * 今日工单数量
   *
   * @param userId
   * @param companyId
   * @param groupId
   * @return
   */
  Integer todayWorkOrderStatistics(
      @Param("userId") Integer userId,
      @Param("companyId") Integer companyId,
      @Param("dept") Integer dept);

  /**
   * 已完成工单, 工单状态{@link WorkOrderStateEnum} 待评价 已完成作为工单完成的条件
   *
   * @param userId
   * @param companyId
   * @param groupId
   * @return
   */
  Integer workOrderCompletedStatistics(
      @Param("userId") Integer userId,
      @Param("companyId") Integer companyId,
      @Param("states") Integer[] states,
      @Param("dept") Integer dept);

  Integer earlyWarringWorkOrderStatistics(
      @Param("userId") Integer userId,
      @Param("companyId") Integer companyId,
      @Param("groupId") Integer groupId,
      @Param("earlyWorkOrderId") Integer[] earlyWorkOrderId,
      @Param("dept") Integer dept);

  /**
   * 后台查询工单
   *
   * @param workOrderQueryReq
   * @return
   */
  List<WorkOrderByIdRes> query(@Param("workOrderQueryReq") WorkOrderQueryReq workOrderQueryReq);


  List<WorkOrderByIdRes> todayWorkOrder(
      @Param("today") LocalDate today,
      @Param("tomorrow") LocalDate tomorrow,
      @Param("status") List<Integer> status,
      @Param("userId") Integer userId,
      @Param("deptId") Integer deptId,
      @Param("companyId") Integer companyId);

  List<WorkOrderByIdRes> todayCompeletWorkOrder(
      @Param("today") LocalDate today,
      @Param("tomorrow") LocalDate tomorrow,
      @Param("status") List<Integer> status,
      @Param("userId") Integer userId,
      @Param("deptId") Integer deptId,
      @Param("companyId") Integer companyId);


  List<WorkOrderByIdRes> earlyWorkOrder(
      @Param("id") List<Integer> id,
      @Param("userId") Integer userId,
      @Param("companyId") Integer companyId,
      @Param("dept") Integer dept);

  /**
   * 工单状态 = 待评价 && END_TIME - 当前时间 == 3天 更新成已完成
   *
   * @param taskQuery
   * @return
   */
  List<WorkOrder> findByStateAndDas(@Param("taskQuery") TaskQuery taskQuery);

  List<WorkOrder> compWorkOrder(
      @Param("today") LocalDate today,
      @Param("tomorrow") LocalDate tomorrow,
      @Param("status") List<Integer> status,
      @Param("userId") Integer userId,
      @Param("deptId") Integer deptId);

  List<WorkOrder> create(
      @Param("today") LocalDate today,
      @Param("tomorrow") LocalDate tomorrow,
      @Param("deptId") Integer deptId,
      @Param("companyId") Integer companyId,
      @Param("userId") Integer userId);

  int findByCompIdCount(@Param("companyId") int companyId);


  List<WorkOrder> end(
      @Param("today") LocalDate today,
      @Param("tomorrow") LocalDate tomorrow,
      @Param("deptId") Integer deptId,
      @Param("companyId") Integer companyId);

  List<WorkOrder> remind(
      @Param("workOrderId") List<Integer> workOrderId,
      @Param("today") LocalDate today,
      @Param("tomorrow") LocalDate tomorrow,
      @Param("deptId") Integer deptId,
      @Param("companyId") Integer companyId,
      @Param("userId") Integer userId);



  List<Map> findWorkOrderCount(
      @Param("status") List<Integer> status, @Param("deptId") Integer deptId);

  long untreatedWorkOrderCount(
      @Param("companyId") Integer companyId,
      @Param("dept") Integer dept,
      @Param("userId") Integer userId);

  WorkOrderByIdRes newById(@Param("workOrderId") Integer workOrderId);

  WorkOrderByIdRes selectWorkOrderInfoById(@Param("workOrderId") Integer workOrderId);

  List<WorkOrderByIdRes> appQuery(@Param("WorkOrderAppQuery") WorkOrderAppQuery workOrderAppQuery);

  List<WorkOrderByIdRes> findByStatus(@Param("defaultMobileIndexWorkOrderReq")
      DefaultMobileIndexWorkOrderReq defaultMobileIndexWorkOrderReq);

  List<WorkOrderByIdRes> findTodoPend(@Param("defaultMobileIndexWorkOrderReq")
      DefaultMobileIndexWorkOrderReq defaultMobileIndexWorkOrderReq);

  List<WorkOrderByIdRes> findTodoFinish(@Param("defaultMobileIndexWorkOrderReq")
      DefaultMobileIndexWorkOrderReq defaultMobileIndexWorkOrderReq);

  /** 首页角标数量*/
  Integer findByStatusCount(@Param("status") List<Integer> status,@Param("companyId") Integer companyId,@Param("deptId") Integer deptId,@Param("userId") Integer userId);

  WorkOrder findByCustomerOrderId(@Param("customerOrderId")Integer customerOrderId);

  ComlateWorkOrderVo findComplateVo(@Param("workOrderId") Integer workOrderId);

  List<Integer> getAllIdByStatus(@Param("companyId") Integer companyId,@Param("status") Integer status,@Param("userId") Integer userId);

  List<CountWorkRes> countWork(@Param("statisticsAppReq") StatisticsAppReq statisticsAppReq);

  List<StatisticsAppRes> workByTime(@Param("statisticsAppReq") StatisticsAppReq statisticsAppReq);

  WorkDiscountVo queryWorkDiscount(Integer id);

  Integer orderService(CleanRoomDataReq req);

  void updatePromoteState(@Param("workId") Integer workId, @Param("promoteState") int promoteState);

  /**
   * 通过工单Id更新工单的预计完成超时状态
   * @param workId 工单Id
   * @return 更新数量
   */
  Integer updatePredictTimeOverById(@Param("workId") Integer workId,@Param("over") Integer over);
  List<WorkOrderBySnRes> queryDetailByWorkOrderSn(@Param("workOrderSnList") List<String> workOrderSnList);

}
