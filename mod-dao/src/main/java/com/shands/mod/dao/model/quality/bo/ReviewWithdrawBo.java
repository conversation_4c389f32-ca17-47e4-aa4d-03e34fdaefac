package com.shands.mod.dao.model.quality.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName ReviewWithdrawBo
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/4/15 14:15
 * @Version 1.0
 */
@Data
@ApiModel("审核退回数")
public class ReviewWithdrawBo {

  @ApiModelProperty(value = "巡检任务id", required = true)
  private Integer taskId;

  @ApiModelProperty(value = "退回说明", required = true)
  private String directions;
}
