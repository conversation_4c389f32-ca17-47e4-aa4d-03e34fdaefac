<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.BiMemberPromotionPrizeMapper">

    <resultMap type="com.shands.mod.dao.model.BiMemberPromotionPrize" id="BiMemberPromotionPrizeMap">
        <result property="bizDate" column="biz_date" jdbcType="TIMESTAMP"/>
        <result property="hotelCode" column="hotel_code" jdbcType="VARCHAR"/>
        <result property="ownDaysIn" column="own_days_in" jdbcType="NUMERIC"/>
        <result property="ownProductionRm" column="own_production_rm" jdbcType="NUMERIC"/>
        <result property="otherDaysIn" column="other_days_in" jdbcType="NUMERIC"/>
        <result property="otherProductionRm" column="other_production_rm" jdbcType="NUMERIC"/>
        <result property="gmvCurrentQuarter" column="GMV_current_quarter" jdbcType="VARCHAR"/>
        <result property="gmvCompareQuarter" column="GMV_compare_quarter" jdbcType="VARCHAR"/>
        <result property="estPrize" column="est_prize" jdbcType="VARCHAR"/>
    </resultMap>
  <select id="selectMemberPromotionPrizeQuarterList"
    resultType="com.shands.mod.dao.model.res.gwincentive.GwIncentiveRes" parameterType="com.shands.mod.dao.model.req.gwincentive.GwIncentiveReq">
    SELECT a.*,@rank:=@rank + 1 AS rankNo from (
    SELECT tb3.hotel_brand_code as brandCode,tb3.hotel_ownership_code as ownershipCode, tb3.hotel_name as hotelName,tb1.hotel_code as hotelCode,tb1.ownDaysIn,tb1.otherDaysIn,tb1.totalDaysIn,tb1.totalProductionRm from
    (SELECT SUM(bi.own_days_in) as ownDaysIn ,sum(bi.other_days_in) as otherDaysIn ,sum(bi.own_days_in+bi.other_days_in) as totalDaysIn,bi.hotel_code,IFNULL(sum(own_production_rm + other_production_rm),0) as totalProductionRm  FROM bi_member_promotion_prize bi
    where biz_date &gt;= #{gwIncentiveReq.startTime} and biz_date &lt;= #{gwIncentiveReq.endTime}
<!--     and is_control = 1 -->
    GROUP BY bi.hotel_code ) tb1
    INNER JOIN (SELECT hotel_code,hotel_name,hotel_brand_code,hotel_cooperation_code,hotel_ownership_code from mod_hotel_info
    <where>
      hotel_status = 1
      <if test="gwIncentiveReq.hotelCodes != null and gwIncentiveReq.hotelCodes != ''">
        AND hotel_code in (${gwIncentiveReq.hotelCodes})
      </if>
    </where>
    GROUP BY hotel_code
    ) tb3 on tb3.hotel_code=tb1.hotel_code
    <where>
      <if test="gwIncentiveReq.brandCode != null and gwIncentiveReq.brandCode != ''">
        AND FIND_IN_SET(tb3.hotel_brand_code,#{gwIncentiveReq.brandCode})
      </if>
      <if test="gwIncentiveReq.ownershipCode != null and gwIncentiveReq.ownershipCode != ''">
        AND FIND_IN_SET(tb3.hotel_ownership_code,#{gwIncentiveReq.ownershipCode})
      </if>
      <if test="gwIncentiveReq.cooperationCode != null and gwIncentiveReq.cooperationCode != ''">
        AND FIND_IN_SET(tb3.hotel_cooperation_code,#{gwIncentiveReq.cooperationCode})
      </if>
    </where>
    order by tb1.totalDaysIn desc
    ) a,(SELECT @rank:= 0) b
  </select>


  <select id="selectMemberPromotionByHotelCode"
    resultType="com.shands.mod.dao.model.res.gwincentive.IncentiveDetailsRes" parameterType="com.shands.mod.dao.model.req.gwincentive.IncentiveDetailsReq">

    SELECT tb1.totalProductionRm,tb3.hotel_name as hotelName,tb1.hotel_code as hotelCode,tb1.ownDaysIn,tb1.otherDaysIn,tb1.totalDaysIn from
    (SELECT SUM(bi.other_production_rm+bi.own_production_rm) as totalProductionRm,SUM(bi.own_days_in) as ownDaysIn ,sum(bi.other_days_in) as otherDaysIn ,sum(bi.own_days_in+bi.other_days_in) as totalDaysIn,bi.hotel_code FROM bi_member_promotion_prize bi
    where biz_date &gt;= #{startTime} and biz_date &lt;= #{endTime}
    GROUP BY bi.hotel_code ) tb1
    LEFT JOIN (SELECT hotel_code,hotel_name from mod_hotel_info WHERE hotel_code= #{hotelCode}  GROUP BY hotel_code) tb3 on tb3.hotel_code=tb1.hotel_code
      WHERE tb1.hotel_code=#{hotelCode}

  </select>



  <select id="findMaxDate" resultType="java.util.Date">
    SELECT MAX(biz_date) FROM bi_member_promotion_prize
  </select>

  <select id="findGroupTradeRevenueCount" resultType="java.lang.Integer">
    SELECT count(1) as cc FROM bi_member_promotion_prize WHERE biz_date = #{bizDate}
    <if test="codes != null and codes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="codes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

</mapper>

