package com.shands.mod.dao.model.v0701.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/17
 * @desc 用户端订单确认请求Dto
*/
@Data
@ApiModel(value = "用户端订单确认请求Dto")
public class OrderConfirmDto {

  @ApiModelProperty(value = "订单编号")
  private Integer orderId;

  @ApiModelProperty(value = "服务类型")
  private String serviceType;

  @ApiModelProperty(value = "支付方式")
  private String payType;

  @ApiModelProperty("来源于小程序 1-是")
  private Integer sourceMiniprogram;


}
