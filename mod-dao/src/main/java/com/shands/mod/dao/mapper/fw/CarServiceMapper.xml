<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.fw.CarServiceMapper">
    <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.fw.CarService">
        <!--@mbg.generated-->
        <!--@Table fw_car_service-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="CAR_TYPE" jdbcType="VARCHAR" property="carType"/>
        <result column="PEOPLE" jdbcType="INTEGER" property="people"/>
        <result column="DESTINATION" jdbcType="VARCHAR" property="destination"/>
        <result column="DEPART" jdbcType="VARCHAR" property="depart"/>
        <result column="COST" jdbcType="DECIMAL" property="cost"/>
        <result column="VERSION" jdbcType="INTEGER" property="version"/>
        <result column="CREATE_USER" jdbcType="INTEGER" property="createUser"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_USER" jdbcType="INTEGER" property="updateUser"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="DEL" jdbcType="TINYINT" property="del"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="GROUP_ID" jdbcType="INTEGER" property="groupId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, CAR_TYPE, PEOPLE, DESTINATION, DEPART, COST, VERSION, CREATE_USER, CREATE_TIME,
        UPDATE_USER, UPDATE_TIME, DEL, COMPANY_ID, GROUP_ID
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from fw_car_service
        where ID = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from fw_car_service
        where ID = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.shands.mod.dao.model.fw.CarService"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into fw_car_service (CAR_TYPE, PEOPLE, DESTINATION,
        DEPART, COST, VERSION,
        CREATE_USER, CREATE_TIME, UPDATE_USER,
        UPDATE_TIME, DEL, COMPANY_ID,
        GROUP_ID)
        values (#{carType,jdbcType=VARCHAR}, #{people,jdbcType=INTEGER}, #{destination,jdbcType=VARCHAR},
        #{depart,jdbcType=VARCHAR}, #{cost,jdbcType=DECIMAL}, #{version,jdbcType=INTEGER},
        #{createUser,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP}, #{del,jdbcType=TINYINT}, #{companyId,jdbcType=INTEGER},
        #{groupId,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.shands.mod.dao.model.fw.CarService"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into fw_car_service
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="carType != null and carType != ''">
                CAR_TYPE,
            </if>
            <if test="people != null">
                PEOPLE,
            </if>
            <if test="destination != null and destination != ''">
                DESTINATION,
            </if>
            <if test="depart != null and depart != ''">
                DEPART,
            </if>
            <if test="cost != null">
                COST,
            </if>
            <if test="version != null">
                VERSION,
            </if>
            <if test="createUser != null">
                CREATE_USER,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateUser != null">
                UPDATE_USER,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="del != null">
                DEL,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="groupId != null">
                GROUP_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="carType != null and carType != ''">
                #{carType,jdbcType=VARCHAR},
            </if>
            <if test="people != null">
                #{people,jdbcType=INTEGER},
            </if>
            <if test="destination != null and destination != ''">
                #{destination,jdbcType=VARCHAR},
            </if>
            <if test="depart != null and depart != ''">
                #{depart,jdbcType=VARCHAR},
            </if>
            <if test="cost != null">
                #{cost,jdbcType=DECIMAL},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="del != null">
                #{del,jdbcType=TINYINT},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.fw.CarService">
        <!--@mbg.generated-->
        update fw_car_service
        <set>
            <if test="carType != null and carType != ''">
                CAR_TYPE = #{carType,jdbcType=VARCHAR},
            </if>
            <if test="people != null">
                PEOPLE = #{people,jdbcType=INTEGER},
            </if>
            <if test="destination != null and destination != ''">
                DESTINATION = #{destination,jdbcType=VARCHAR},
            </if>
            <if test="depart != null and depart != ''">
                DEPART = #{depart,jdbcType=VARCHAR},
            </if>
            <if test="cost != null">
                COST = #{cost,jdbcType=DECIMAL},
            </if>
            <if test="version != null">
                VERSION = #{version,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{updateUser,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="del != null">
                DEL = #{del,jdbcType=TINYINT},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="groupId != null">
                GROUP_ID = #{groupId,jdbcType=INTEGER},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.fw.CarService">
        <!--@mbg.generated-->
        update fw_car_service
        set CAR_TYPE = #{carType,jdbcType=VARCHAR},
        PEOPLE = #{people,jdbcType=INTEGER},
        DESTINATION = #{destination,jdbcType=VARCHAR},
        DEPART = #{depart,jdbcType=VARCHAR},
        COST = #{cost,jdbcType=DECIMAL},
        VERSION = #{version,jdbcType=INTEGER},
        CREATE_USER = #{createUser,jdbcType=INTEGER},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_USER = #{updateUser,jdbcType=INTEGER},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        DEL = #{del,jdbcType=TINYINT},
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
        GROUP_ID = #{groupId,jdbcType=INTEGER}
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <select id="All" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fw_car_service
        where del = 0
        <if test="carQueryReq.carType != null">
            and CAR_TYPE = #{carQueryReq.carType}
        </if>
        <if test="carQueryReq.companyId != null">
            and COMPANY_ID = #{carQueryReq.companyId}
        </if>
    </select>
</mapper>