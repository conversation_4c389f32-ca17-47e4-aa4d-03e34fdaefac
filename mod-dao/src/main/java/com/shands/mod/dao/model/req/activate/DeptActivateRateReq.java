package com.shands.mod.dao.model.req.activate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("激活详情请求参数")
public class DeptActivateRateReq {

  @NotNull(message = "酒店id不能为空")
  @ApiModelProperty("酒店id")
  private Integer company;

  @ApiModelProperty("部门id")
  private Integer deptId;

  @NotBlank(message = "类型不能为空")
  @ApiModelProperty("明细类型 staff员工  dept部门")
  private String type;

  @ApiModelProperty("状态 1激活 0未激活")
  private Integer status;
}
