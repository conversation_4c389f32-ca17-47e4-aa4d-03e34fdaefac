package com.shands.mod.dao.model.hs;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
 * 物品详情表(HsGoodsBorrowDetail)实体类
 *
 * <AUTHOR>
 * @since 2021-04-14 11:30:45
 */
@Data
public class HsGoodsBorrowDetail implements Serializable {
    private static final long serialVersionUID = -57617665107402033L;
    /**
    * 表主键
    */
    private Integer id;
    /**
    * 物品ID
    */
    private Integer goodsId;
    /**
    * 物品名称
    */
    private String goodsName;
    /**
    * 物品数量
    */
    private Integer goodsNum;
    /**
    * 物品状态  0未归还  1已归还  2预约中
    */
    private String goodsStatus;
    /**
    * 房间号
    */
    private String roomCode;
    /**
    * 所属公司ID
    */
    private Integer companyId;
    /**
    * 离店日期
    */
    private Date leaveTime;
    /**
    * 归还时间
    */
    private Date remandTime;
    /**
    * 归还员工ID
    */
    private String remandUserid;
    /**
    * 归还员工名称
    */
    private String remandUsername;
    /**
    * 订单创建人ID
    */
    private String orderUserid;
    /**
    * 订单创建人名称
    */
    private String orderUsername;
    /**
    * 订单编号
    */
    private String orderId;
    /**
    * 工单编号
    */
    private String workorderId;
    /**
    * 删除标记
    */
    private Integer deleted;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 修改时间
    */
    private Date updateTime;
    /**
    * 备注
    */
    private String remark;
    /**
    * 价格
    */
    private BigDecimal price;
    /**
     * 关键字
     */
    private String keyWord;

}