package com.shands.mod.dao.model.quality.vo.v2;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

/**
 * @Description:
 * @Author: Java菜鸟
 * @CreateDate: 2022/4/18 11:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionV2Vo {

  @ApiModelProperty("部门名称")
  private String deptName;
  private String deptId;

  @ApiModelProperty("维度名称")
  private String dimensionName;
  private String dimensionId;

  @ApiModelProperty("资源类型")
  private String resourceType;

  @ApiModelProperty("资源地址")
  private String url;

  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("id")
  private Integer id;

  @ApiModelProperty("整改人")
  private String rectificationUserName;

  @ApiModelProperty("复查人")
  private String reviewUserName;

  @ApiModelProperty("整改人")
  private String rectificationUserId;

  @ApiModelProperty("复查人")
  private String reviewUserId;

  @ApiModelProperty("优先级")
  private String priority;

  @ApiModelProperty("整改状态")
  private String status;

  @JSONField(format = "yyyy-MM-dd")
  @ApiModelProperty("任务开始时间")
  private Date startTime;

  @JSONField(format = "yyyy-MM-dd")
  @ApiModelProperty("任务结束时间")
  private Date endTime;

}
