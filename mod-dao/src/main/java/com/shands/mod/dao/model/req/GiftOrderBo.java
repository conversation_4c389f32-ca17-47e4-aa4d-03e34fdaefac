package com.shands.mod.dao.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @Date 2021/11/11
 **/
@Data
@ApiModel("订单信息请求")
public class GiftOrderBo {

  @ApiModelProperty("员工手机号")
  @NotBlank(message = "员工手机号不能为空")
  private String employeeMobile;

  @ApiModelProperty("订单状态")
  private String orderPayStatus;

  @DateTimeFormat(pattern = "yyyy-MM-dd 00:00:00")
  @ApiModelProperty("开始时间")
  private Date startTime;

  @DateTimeFormat(pattern = "yyyy-MM-dd 23:59:59")
  @ApiModelProperty("结束时间")
  private Date endTime;

  private Integer pageNo;

  private Integer pageSize;
}
