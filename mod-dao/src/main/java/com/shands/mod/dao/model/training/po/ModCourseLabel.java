package com.shands.mod.dao.model.training.po;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标签表(ModCourseLabel)实体类
 *
 * <AUTHOR>
 * @since 2022-08-19 10:58:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModCourseLabel implements Serializable {
    private static final long serialVersionUID = -88595994838802742L;
    
    private Integer id;
    /**
     * 标签名
     */
    private String labelName;
    /**
     * 类型（区分酒店、集团）
     */
    private String labelType;

    private String hotelCode;
}