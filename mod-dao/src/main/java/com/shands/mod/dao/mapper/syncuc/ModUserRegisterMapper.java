package com.shands.mod.dao.mapper.syncuc;

import com.shands.mod.dao.model.req.activate.ActivationRateReq;
import com.shands.mod.dao.model.req.activate.DeptActivateRateReq;
import com.shands.mod.dao.model.res.activate.ActivationRateRes;
import com.shands.mod.dao.model.res.activate.DeptActivateRateRes;
import com.shands.mod.dao.model.syncuc.ModUserRegister;
import com.shands.mod.dao.model.v0701.dto.UserVisitActiveDto;
import com.shands.mod.dao.model.v0701.vo.UserLoginDataVo;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ModUserRegisterMapper {

  int deleteByPrimaryKey(Integer id);

  int insert(ModUserRegister record);

  int insertBatch(@Param("entities") List<ModUserRegister> entities);

  int insertSelective(ModUserRegister record);

  ModUserRegister selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(ModUserRegister record);

  int updateByPrimaryKey(ModUserRegister record);

  /**
   * app登录数据查询
   * @param companyId
   * @return
   */
  List<UserLoginDataVo> loginDataInfo(@Param("companyId") Integer companyId);

  /**
   * app登录数据查询
   * @param companyId
   * @return
   */
  List<UserLoginDataVo> notLoginDataInfo(@Param("companyId") Integer companyId);

  /**
   * app活跃数据
   * @param dto
   * @return
   */
  List<UserLoginDataVo> activeDataInfo(UserVisitActiveDto dto);

  /**
   * 查询酒店激活率汇总报表
   * @param req
   * @return
   */
  List<ActivationRateRes> activationRate(@Param("req") ActivationRateReq req);

  /**
   * 激活率汇总报表(大数据获取信息)
   * @param req
   * @return
   */
  List<ActivationRateRes> activationRateForBigData(@Param("req") ActivationRateReq req);

  /**
   * 部门下激活率汇总
   * @return
   */
  List<DeptActivateRateRes> deptActivateRate(DeptActivateRateReq req);

  /**
   * 员工激活率汇总
   * @param req
   * @return
   */
  List<DeptActivateRateRes> staffActivateRate(DeptActivateRateReq req);

  ActivationRateRes totalRate(ActivationRateReq req);

  ActivationRateRes totalRateForBigData(ActivationRateReq req);


  int findGroupTradeRevenueCount(@Param("bizDate") Date bizDate,@Param("codes") List<String> codes);

  /**
   * 查询最新数据日期
   * @return
   */
  Date findMaxDate();

}