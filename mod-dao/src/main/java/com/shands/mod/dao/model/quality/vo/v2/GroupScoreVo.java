package com.shands.mod.dao.model.quality.vo.v2;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

/**
 * @ClassName AreaScoreVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/6/2 13:48
 * @Version 1.0
 */
@Data
@ApiModel("分类")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GroupScoreVo {

  @ApiModelProperty("序号")
  private String id;

  @ApiModelProperty("管理方式")
  private String cooperationName;

  @ApiModelProperty("巡检员")
  private String checkPeople;

  @ApiModelProperty("检查开始时间")
  private String taskStartTime;

  @ApiModelProperty("酒店名称")
  private String hotelName;

  @ApiModelProperty("酒店编码")
  private String hotelCode;

  @ApiModelProperty("归属事业部")
  private String ownershipName;

  @ApiModelProperty("各个子系统")
  private String g;

  @ApiModelProperty("消防应急物资配置标准")
  private String h;

  @ApiModelProperty("防暴恐物资配置标准")
  private String i;

  @ApiModelProperty("钥匙管理")
  private String j;

  @ApiModelProperty("消防设备设施")
  private String k;

  @ApiModelProperty("环境安全（安全区域）")
  private String l;

  @ApiModelProperty("突发事件和应急物资（安全区域）")
  private String m;


  @ApiModelProperty("安全培训")
  private String n;

  @ApiModelProperty("电瓶车安全管理")
  private String o;

  @ApiModelProperty("消防相关证照及报告")
  private String p;

  @ApiModelProperty("安全管理委员会")
  private String q;

  @ApiModelProperty("管理台账及报告")
  private String r;

  @ApiModelProperty("外来人员和物资出门")
  private String s;

  @ApiModelProperty("重大事件报告")
  private String t;

  @ApiModelProperty("歇业检查")
  private String u;

  @ApiModelProperty("厨房安全检查")
  private String v;

  @ApiModelProperty("突发事件和应急物资（工程设备区域）")
  private String w;

  @ApiModelProperty("高空作业/动火作业审批许可")
  private String x;

  @ApiModelProperty("康乐安全检查")
  private String y;

  @ApiModelProperty("室内外无动力设备设施")
  private String z;

  @ApiModelProperty("环境安全（康乐区域）")
  private String aa;

  @ApiModelProperty("环境安全（客房区域）")
  private String ab;

  @ApiModelProperty("残疾人设施")
  private String ac;

  @ApiModelProperty("客房设施安全检查")
  private String ad;

  @ApiModelProperty("防疫物资")
  private String ae;

  @ApiModelProperty("虫害消杀")
  private String af;


}
