package com.shands.mod.dao.model.excitation;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * mod_message_excitation
 * <AUTHOR>
@Data
public class ModMessageExcitation implements Serializable {
    private Integer id;

    /**
     * 账单类型
     */
    private String billeType;

    /**
     * 商户订单号
     */
    private String sn;

    /**
     * 奖励金额
     */
    private BigDecimal excitationAmount;

    /**
     * 订单金额
     */
    private BigDecimal amount;

    /**
     * 商品说明
     */
    private String describeStr;

    /**
     * 订单创建时间
     */
    private Date orderTime;

    private Date createTime;

    private Date modifyTime;

    private String remark;

    private static final long serialVersionUID = 1L;
}