package com.shands.mod.dao.model.quality.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
@ApiModel("我的待办请求参数")
public class MyCheckBo {

  @ApiModelProperty("检查类型")
  @NotBlank(message = "检查类型不能为空")
  private String checkType;

  @ApiModelProperty("id")
  private Integer userId;

  @ApiModelProperty("我的类型（不传）")
  private String myType;


}
