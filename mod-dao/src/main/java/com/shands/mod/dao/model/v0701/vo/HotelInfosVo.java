package com.shands.mod.dao.model.v0701.vo;

import com.shands.mod.dao.model.enums.HotelStatusEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/8/11
 * @desc 酒店列表返回对象
*/

@Data
public class HotelInfosVo {

  /** 用户机构编码 */
  private Integer company;

  /** 用户机构名称 */
  private String hotelName;

  /**
   * 酒店code
   */
  private String hotelCode;

  /**
   * 通宝酒店id
   */
  private Integer ucCompanyId;

  private Integer hotelInfoType;

  /**
   * 酒店运营状态
   */
  private String contract;

  /**
   * 酒店运营状态code
   */
  private String contractCode;

  public String getContract() {
    return HotelStatusEnum.getValue(contractCode);
  }
}
