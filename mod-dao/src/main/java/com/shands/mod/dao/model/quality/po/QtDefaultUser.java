package com.shands.mod.dao.model.quality.po;

import lombok.Data;
import java.util.Date;
import java.io.Serializable;
/**
 * 默认接收人中间表(QtDefaultUser)实体类
 *
 * <AUTHOR>
 * @since 2022-04-15 14:41:44
 */
@Data
public class QtDefaultUser implements Serializable {
  private static final long serialVersionUID = 361770982285467955L;

  private Integer id;

  private Date createTime;

  private Date updateTime;
  /**
   * 备注
   */
  private String remark;

  private Integer createUser;

  private Integer updateUser;

  private Boolean ifDelete;
  /**
   * 员工id
   */
  private Integer userId;
  /**
   * 类型（复查人/整改人）
   */
  private String type;
  /**
   * 部门id
   */
  private Integer deptId;
  /**
   * 酒店id
   */
  private Integer hotelId;
}
