package com.shands.mod.dao.model.quality.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("预览详情")
public class PreviewDetailedVo {

  @ApiModelProperty("填空题/选择题")
  private String topicType;

  @ApiModelProperty("是否必填")
  private Boolean ifRequired;

  @ApiModelProperty("问题")
  private String projectName;

  @ApiModelProperty("检查方法")
  private String checkMethod;

  @ApiModelProperty("检查标准")
  private String checkStandard;


}
