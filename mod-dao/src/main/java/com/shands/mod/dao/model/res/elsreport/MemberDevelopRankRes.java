package com.shands.mod.dao.model.res.elsreport;

import com.shands.mod.dao.model.enums.BrandEnum;
import com.shands.mod.dao.model.enums.OwnershipEnum;
import com.shands.mod.dao.util.ThousandSeparatorUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("会员发展排行榜接口返回参数")
@Data
public class MemberDevelopRankRes {

  @ApiModelProperty("酒店名称")
  private String hotelName;

  @ApiModelProperty("酒店code")
  private String hotelCode;

  @ApiModelProperty("发展人数")
  private Integer memberCnt;

  @ApiModelProperty("千分位发展人数")
  private String memberCntStr;

  @ApiModelProperty("姓名")
  private String staffName;

  @ApiModelProperty("手机号")
  private String mobile;

  @ApiModelProperty("品牌code")
  private String brandCode;
  @ApiModelProperty("事业部code")
  private String divisionCode;

  @ApiModelProperty("部门名称")
  private String dptName;

  @ApiModelProperty("品牌名称")
  private String brandName;



  @ApiModelProperty("事业部名称")
  private String divisionName;

  @ApiModelProperty("uuid")
  private String uuidStr;

  @ApiModelProperty("是否属于当前酒店")
  private Integer ifHotel=0;

  @ApiModelProperty("排名")
  private Integer rankNo;

  @ApiModelProperty("APP下载量")
  private Integer appDownload = 0;

  @ApiModelProperty("千分位APP下载量")
  private String appDownloadStr = "0";

  //员工id
  private String staffId;
  public String getBrandName() {
    return  BrandEnum.getValue(brandCode);
  }

  public void setBrandName(String brandName) {
    this.brandName = brandName;
  }

  public String getDivisionName() {
    return  OwnershipEnum.getValue(divisionCode);
  }

  public void setDivisionName(String divisionName) {
    this.divisionName = divisionName;
  }


  public String getAppDownloadStr() {
    appDownloadStr = ThousandSeparatorUtil.format(appDownload);
    return appDownloadStr;
  }

  public String getMemberCntStr() {
    memberCntStr = ThousandSeparatorUtil.format(memberCnt);
    return memberCntStr;
  }

}
