package com.shands.mod.dao.model.quality.vo.v2;

import com.shands.mod.dao.model.enums.OwnershipEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("整改问题列表返回参数")
public class RectificationProblemVo {
  @ApiModelProperty("酒店名称")
  private String hotelName;

  @ApiModelProperty("事业部code")
  private String ownershipCode;

  @ApiModelProperty("事业部名称")
  private String ownershipName;

  @ApiModelProperty("问题点数量")
  private Integer troubleSpotsNum;

  @ApiModelProperty("安全红线数量")
  private Integer securityLineNum;

  @ApiModelProperty("已完成数量")
  private Integer doneNum;

  @ApiModelProperty("未完成数量")
  private Integer undoneNum;

  public String getOwnershipName() {
    return  OwnershipEnum.getValue(ownershipCode);
  }

  public void setOwnershipName(String ownershipName) {
    this.ownershipName = ownershipName;
  }

}
