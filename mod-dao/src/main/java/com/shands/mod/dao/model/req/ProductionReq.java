package com.shands.mod.dao.model.req;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

public class ProductionReq {
  private Integer id;
  // 产品类型
  @NotBlank(message = "套餐类型不能为空")
  private String category;

  @NotBlank(message = "名称不能为空")
  private String name;
  // 价格类型 0长期免费 1阶梯价格
  @NotNull(message = "价格类型不能为空")
  private Integer priceType;
  // 套餐内容
  // permissions
  private String content;
  // 套餐描述
  private String description;

  // 状态 1上架，0下架
  private Integer status;

  private Integer version;

  private Integer deleted;

  private Integer createUser;

  private Date createTime;

  private Integer updateUser;

  private Date updateTime;
  // 价格信息  strip条    year和month     [{"amount": 1,"unit": "year","price": 123}]
  private String priceInfo;

  private String priceList;

  // 免费时长字段
  private Integer probation;

  public Integer getProbation() {
    return probation;
  }

  public void setProbation(Integer probation) {
    this.probation = probation;
  }

  public String getPriceList() {
    return priceList;
  }

  public void setPriceList(String priceList) {
    this.priceList = priceList;
  }

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public String getCategory() {
    return category;
  }

  public void setCategory(String category) {
    this.category = category == null ? null : category.trim();
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name == null ? null : name.trim();
  }

  public Integer getPriceType() {
    return priceType;
  }

  public void setPriceType(Integer priceType) {
    this.priceType = priceType;
  }

  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public Integer getVersion() {
    return version;
  }

  public void setVersion(Integer version) {
    this.version = version;
  }

  public Integer getDeleted() {
    return deleted;
  }

  public void setDeleted(Integer deleted) {
    this.deleted = deleted;
  }

  public Integer getCreateUser() {
    return createUser;
  }

  public void setCreateUser(Integer createUser) {
    this.createUser = createUser;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Integer getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(Integer updateUser) {
    this.updateUser = updateUser;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public String getPriceInfo() {
    return priceInfo;
  }

  public void setPriceInfo(String priceInfo) {
    this.priceInfo = priceInfo == null ? null : priceInfo.trim();
  }
}
