package com.shands.mod.dao.model.catecard.resp;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("当前登陆的用户信息")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserInfoResp {

  @ApiModelProperty("uc用户id")
  private Integer ucId;

  @ApiModelProperty("酒店id")
  private String hotelCode;

  @ApiModelProperty("销售渠道")
  private String salesChannel;

  @ApiModelProperty("手机号")
  private String mobile;


  @ApiModelProperty("是为酒店总")
  private boolean isHotelManager;

}
