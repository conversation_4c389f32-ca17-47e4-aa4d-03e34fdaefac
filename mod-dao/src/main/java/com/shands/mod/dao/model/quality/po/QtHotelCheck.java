package com.shands.mod.dao.model.quality.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
/**
 * (QtHotelCheck)实体类
 *
 * <AUTHOR>
 * @since 2022-03-23 18:37:25
 */
@Data
public class QtHotelCheck implements Serializable {
    private static final long serialVersionUID = 903189333539461701L;
    /**
     * 主键id
     */
    private Integer id;
    /**
     * 检查表标题
     */
    private String checkTitle;
    /**
     * 酒店信息表ID
     */
    private Integer hotelId;
    /**
     * 酒店名称
     */
    private String hotelName;
    /**
     * 考核开始日期
     */
    private Date assessmentStartTime;
    /**
     * 考核结束日期
     */
    private Date assessmentEndTime;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private Integer createUser;
    /**
     * 修改人
     */
    private Integer updateUser;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标记
     */
    private Boolean ifDelete;
    /**
     * 检查类型（集团考核、酒店自检）
     */
    private String inspectType;
    /**
     * 考核方式编码（日、周、月）
     */
    private String assessmentType;
    /**
     * 状态
     */
    private String status;

    /**
     * 文件下载地址
     */
    private String url;

}
