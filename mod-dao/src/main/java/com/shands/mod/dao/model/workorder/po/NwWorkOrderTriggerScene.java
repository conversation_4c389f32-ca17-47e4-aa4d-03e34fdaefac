package com.shands.mod.dao.model.workorder.po;

import java.io.Serializable;
import java.util.Date;

/**
 * 工单触发场景表(NwWorkOrderTriggerScene)实体类
 *
 * <AUTHOR>
 * @since 2022-05-06 13:47:35
 */
public class NwWorkOrderTriggerScene implements Serializable {

  private static final long serialVersionUID = 756423694168787104L;

  private Integer id;
  /**
   * 版本号 版本号
   */
  private Integer version;
  /**
   * 创建时间
   */
  private Date createTime;
  /**
   * 创建人
   */
  private Integer createUser;
  /**
   * 修改时间
   */
  private Date updateTime;
  /**
   * 修改人
   */
  private Integer updateUser;
  /**
   * 删除标识
   */
  private Integer deleted;
  /**
   * 触发类型
   */
  private String triggerType;
  /**
   * 业务名称
   */
  private String businessName;
  /**
   * 提醒对象
   */
  private String reminderUser;
  /**
   * 消息类型
   */
  private String messageType;
  /**
   * 消息id
   */
  private String messageId;
  /**
   * 触发id
   */
  private Integer triggerId;
  /**
   * 超时时间
   */
  private Integer outTime;

  /**
   * 触发模式
   */
  private String triggerMode;
  /**
   * 循环时间
   */
  private Integer forTime;
  /**
   * 循环次数
   */
  private Integer forNum;


  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getVersion() {
    return version;
  }

  public void setVersion(Integer version) {
    this.version = version;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Integer getCreateUser() {
    return createUser;
  }

  public void setCreateUser(Integer createUser) {
    this.createUser = createUser;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public Integer getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(Integer updateUser) {
    this.updateUser = updateUser;
  }

  public Integer getDeleted() {
    return deleted;
  }

  public void setDeleted(Integer deleted) {
    this.deleted = deleted;
  }

  public String getTriggerType() {
    return triggerType;
  }

  public void setTriggerType(String triggerType) {
    this.triggerType = triggerType;
  }

  public String getBusinessName() {
    return businessName;
  }

  public void setBusinessName(String businessName) {
    this.businessName = businessName;
  }

  public String getReminderUser() {
    return reminderUser;
  }

  public void setReminderUser(String reminderUser) {
    this.reminderUser = reminderUser;
  }

  public String getMessageType() {
    return messageType;
  }

  public void setMessageType(String messageType) {
    this.messageType = messageType;
  }

  public String getMessageId() {
    return messageId;
  }

  public void setMessageId(String messageId) {
    this.messageId = messageId;
  }

  public Integer getTriggerId() {
    return triggerId;
  }

  public void setTriggerId(Integer triggerId) {
    this.triggerId = triggerId;
  }

  public Integer getOutTime() {
    return outTime;
  }

  public void setOutTime(Integer outTime) {
    this.outTime = outTime;
  }

  public String getTriggerMode() {
    return triggerMode;
  }

  public void setTriggerMode(String triggerMode) {
    this.triggerMode = triggerMode;
  }

  public Integer getForTime() {
    return forTime;
  }

  public void setForTime(Integer forTime) {
    this.forTime = forTime;
  }

  public Integer getForNum() {
    return forNum;
  }

  public void setForNum(Integer forNum) {
    this.forNum = forNum;
  }
}

