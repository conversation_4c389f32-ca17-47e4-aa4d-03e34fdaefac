package com.shands.mod.dao.model.newDataBoard.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel("我的待办列表 出参")
public class MyBacklogVo  {

  @ApiModelProperty("模块名称")
  private String moduleName;

  @ApiModelProperty("标题")
  private String title;
  @ApiModelProperty("具体问题")
  private String specificProblems;

  @ApiModelProperty("具体时间")
  private Date specificTime;

  @ApiModelProperty("具体时间格式化")
  private String strTime;
}
