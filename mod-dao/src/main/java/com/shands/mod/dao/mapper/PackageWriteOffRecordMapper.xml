<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shands.mod.dao.mapper.PackageWriteOffRecordMapper" >
  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.PackageWriteOffRecord" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="ticket_number" property="ticketNumber" jdbcType="VARCHAR" />
    <result column="good_code" property="goodCode" jdbcType="VARCHAR" />
    <result column="validity_period" property="validityPeriod" jdbcType="VARCHAR" />
    <result column="mobile" property="mobile" jdbcType="VARCHAR" />
    <result column="company_id" property="companyId" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="operate_man" property="operateMan" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="ticket_name" property="ticketName" jdbcType="VARCHAR"/>
    <result column="type" property="type" jdbcType="VARCHAR"/>
    <result column="room_no" property="roomNo" jdbcType="VARCHAR"/>
    <result column="staff_id" property="staffId" jdbcType="INTEGER"/>
    <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
    <result column="crs_no" property="crsNo" jdbcType="VARCHAR"/>
    <result column="if_voucher_num" property="ifVoucherNum" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, ticket_number, good_code, validity_period, mobile, company_id, create_time, operate_man, 
    remark,ticket_name,type,room_no,staff_id,customer_name,crs_no,if_voucher_num
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.shands.mod.dao.model.PackageWriteOffRecordExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mod_package_write_off_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from mod_package_write_off_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from mod_package_write_off_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.shands.mod.dao.model.PackageWriteOffRecordExample" >
    delete from mod_package_write_off_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.shands.mod.dao.model.PackageWriteOffRecord" >
    insert into mod_package_write_off_record (id, ticket_number, good_code, 
      validity_period, mobile, company_id, 
      create_time, operate_man, remark,ticket_name,type,room_no,staff_id,customer_name,crs_no,if_voucher_num
      )
    values (#{id,jdbcType=INTEGER}, #{ticketNumber,jdbcType=VARCHAR}, #{goodCode,jdbcType=VARCHAR}, 
      #{validityPeriod,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{operateMan,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
     #{ticketName,jdbcType=VARCHAR},#{type,jdbcType=VARCHAR},#{roomNo,jdbcType=VARCHAR},#{staffId,jdbcType=INTEGER},#{customerName,jdbcType=VARCHAR}
     ,#{crsNo,jdbcType=VARCHAR},#{ifvouchernum,jdbcType=INTEGER} )
  </insert>
  <insert id="insertSelective" parameterType="com.shands.mod.dao.model.PackageWriteOffRecord" >
    insert into mod_package_write_off_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="ticketNumber != null" >
        ticket_number,
      </if>
      <if test="goodCode != null" >
        good_code,
      </if>
      <if test="validityPeriod != null" >
        validity_period,
      </if>
      <if test="mobile != null" >
        mobile,
      </if>
      <if test="companyId != null" >
        company_id,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="operateMan != null" >
        operate_man,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="ticketName !=null">
        ticket_name,
      </if>
      <if test="type !=null">
        type,
      </if>
      <if test="roomNo !=null">
        room_no,
      </if>
      <if test="staffId !=null">
        staff_id,
      </if>
      <if test="customerName !=null">
        customer_name,
      </if>
      <if test="crsNo !=null">
        crs_no,
      </if>
      <if test="ifVoucherNum !=null">
        if_voucher_num,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="ticketNumber != null" >
        #{ticketNumber,jdbcType=VARCHAR},
      </if>
      <if test="goodCode != null" >
        #{goodCode,jdbcType=VARCHAR},
      </if>
      <if test="validityPeriod != null" >
        #{validityPeriod,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operateMan != null" >
        #{operateMan,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ticketName !=null">
        #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="type !=null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="roomNo !=null">
        #{roomNo,jdbcType=VARCHAR},
      </if>
      <if test="staffId !=null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="customerName !=null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="crsNo !=null">
        #{crsNo,jdbcType=VARCHAR},
      </if>
      <if test="ifVoucherNum !=null">
        #{ifVoucherNum,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.shands.mod.dao.model.PackageWriteOffRecordExample" resultType="java.lang.Integer" >
    select count(*) from mod_package_write_off_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update mod_package_write_off_record
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.ticketNumber != null" >
        ticket_number = #{record.ticketNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.goodCode != null" >
        good_code = #{record.goodCode,jdbcType=VARCHAR},
      </if>
      <if test="record.validityPeriod != null" >
        validity_period = #{record.validityPeriod,jdbcType=VARCHAR},
      </if>
      <if test="record.mobile != null" >
        mobile = #{record.mobile,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null" >
        company_id = #{record.companyId,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operateMan != null" >
        operate_man = #{record.operateMan,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.ticketName !=null">
        ticket_name = #{record.ticketName,jdbcType=VARCHAR},
      </if>
      <if test="record.type !=null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.roomNo !=null">
        room_no = #{record.roomNo,jdbcType=VARCHAR},
      </if>
      <if test="record.staffId !=null">
        staff_id =  #{record.staffId,jdbcType=INTEGER},
      </if>
      <if test="record.customerName !=null">
        customer_name = #{record.customerName,jdbcType=VARCHAR},
      </if>
      <if test="record.crsNo !=null">
        crs_no = #{record.crsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.ifVoucherNum !=null">
        if_voucher_num = #{record.ifVoucherNum,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update mod_package_write_off_record
    set id = #{record.id,jdbcType=INTEGER},
    ticket_number = #{record.ticketNumber,jdbcType=VARCHAR},
    good_code = #{record.goodCode,jdbcType=VARCHAR},
    validity_period = #{record.validityPeriod,jdbcType=VARCHAR},
    mobile = #{record.mobile,jdbcType=VARCHAR},
    company_id = #{record.companyId,jdbcType=INTEGER},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    operate_man = #{record.operateMan,jdbcType=VARCHAR},
    remark = #{record.remark,jdbcType=VARCHAR},
    type = #{record.type,jdbcType=VARCHAR},
    room_no = #{record.roomNo,jdbcType=VARCHAR},
    staff_id =  #{record.staffId,jdbcType=INTEGER},
    crs_no = #{record.crsNo,jdbcType=VARCHAR},
    if_voucher_num = #{record.ifVoucherNum,jdbcType=VARCHAR},
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.PackageWriteOffRecord" >
    update mod_package_write_off_record
    <set >
      <if test="ticketNumber != null" >
        ticket_number = #{ticketNumber,jdbcType=VARCHAR},
      </if>
      <if test="goodCode != null" >
        good_code = #{goodCode,jdbcType=VARCHAR},
      </if>
      <if test="validityPeriod != null" >
        validity_period = #{validityPeriod,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operateMan != null" >
        operate_man = #{operateMan,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ticketName !=null">
        ticket_name = #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="type !=null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="roomNo !=null">
        room_no = #{record.roomNo,jdbcType=VARCHAR},
      </if>
      <if test="staffId !=null">
        staff_id =  #{record.staffId,jdbcType=INTEGER},
      </if>
      <if test="customerName !=null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="crsNo !=null">
        crs_no = #{crsNo,jdbcType=VARCHAR},
      </if>
      <if test="ifVoucherNum !=null">
        if_voucher_num = #{ifVoucherNum,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.PackageWriteOffRecord" >
    update mod_package_write_off_record
    set ticket_number = #{ticketNumber,jdbcType=VARCHAR},
      good_code = #{goodCode,jdbcType=VARCHAR},
      validity_period = #{validityPeriod,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      operate_man = #{operateMan,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      room_no = #{jdbcType=VARCHAR},
      staff_id =  #{staffId,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      crs_no = #{record.crsNo,jdbcType=VARCHAR},
        if_voucher_num = #{ifVoucherNum,jdbcType=VARCHAR},
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!--每个商品 在所选择日期内 核销的张数-->
  <select id="groupByName" resultType="com.shands.mod.dao.model.req.hs.report.GroupByName">
    SELECT
    ticket_name ticketName,
    count( id ) countId
    FROM
    mod_package_write_off_record
    where company_id = #{statisticsAppReq.companyId,jdbcType=INTEGER}
    <if test="statisticsAppReq.type ==1">
      and CREATE_TIME >= CURDATE()
      AND CREATE_TIME &lt; date_add(CURDATE(), interval 1 day)
    </if>
    <if test="statisticsAppReq.type ==2">
      and CREATE_TIME >= date_add(CURDATE(), interval -6 day)
      AND CREATE_TIME &lt; date_add(CURDATE(), interval 1 day)
    </if>
    <if test="statisticsAppReq.type ==3">
      and CREATE_TIME >= date_add(CURDATE(), interval -29 day)
      AND CREATE_TIME &lt; date_add(CURDATE(), interval 1 day)
    </if>
    <if test="statisticsAppReq.type ==-1">
      and create_time >= #{statisticsAppReq.startDate,jdbcType=TIMESTAMP}
      and create_time &lt; date_add(#{statisticsAppReq.endDate,jdbcType=TIMESTAMP}, interval 1 day)
    </if>
    and type = #{statisticsAppReq.offType}
    <if test="statisticsAppReq.staffId !=null">
      and  staff_id =  #{statisticsAppReq.staffId,jdbcType=INTEGER}
    </if>
    GROUP BY ticket_name
    order by create_time desc
  </select>

  <!--核销明细-->
  <select id="packageDetail" resultType="com.shands.mod.dao.model.req.hs.report.PackageDeatilRes">
    SELECT
    a.ticket_name ticketName,
    a.ticket_number ticketNumber,
    a.good_code goodCode,
    a.operate_man name,
    a.create_time  createTime,
    a.room_no roomNo
    FROM
    mod_package_write_off_record a
    WHERE
    1=1
    <if test="statisticsAppReq.ticketName != null and statisticsAppReq.ticketName != ''">
      and a.ticket_name = #{statisticsAppReq.ticketName}
    </if>
    and a.company_id = #{statisticsAppReq.companyId,jdbcType=INTEGER}
    <if test="statisticsAppReq.type ==1">
      and a.CREATE_TIME >= CURDATE()
      AND a.CREATE_TIME &lt; date_add(CURDATE(), interval 1 day)
    </if>
    <if test="statisticsAppReq.type ==2">
      and a.CREATE_TIME >= date_add(CURDATE(), interval -6 day)
      AND a.CREATE_TIME &lt; date_add(CURDATE(), interval 1 day)
    </if>
    <if test="statisticsAppReq.type ==3">
      and a.CREATE_TIME >= date_add(CURDATE(), interval -29 day)
      AND a.CREATE_TIME &lt; date_add(CURDATE(), interval 1 day)
    </if>
    <if test="statisticsAppReq.type ==-1">
      and a.create_time >= #{statisticsAppReq.startDate,jdbcType=TIMESTAMP}
      and a.create_time &lt; date_add(#{statisticsAppReq.endDate,jdbcType=TIMESTAMP}, interval 1 day)
    </if>
    and type = #{statisticsAppReq.offType}
    <if test="statisticsAppReq.staffId !=null">
      and  staff_id =  #{statisticsAppReq.staffId,jdbcType=INTEGER}
    </if>
  </select>
</mapper>