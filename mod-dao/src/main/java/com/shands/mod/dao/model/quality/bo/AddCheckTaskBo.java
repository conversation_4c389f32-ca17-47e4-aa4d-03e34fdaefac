package com.shands.mod.dao.model.quality.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("添加任务表请求参数")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddCheckTaskBo {

  @NotBlank(message = "检查类型不能为空")
  @ApiModelProperty("酒店类型（HOTEL:酒店 BLOC:集团")
  private String property;

  @ApiModelProperty("检查表id")
  private Integer checkId;

  @ApiModelProperty("酒店id")
  private Integer hotelId;

  @ApiModelProperty("巡查员员工id")
  private String userIds;

  @ApiModelProperty("任务标题")
  private String taskTitle;

  @ApiModelProperty("任务开始时间")
  private Date taskStartTime;

  @ApiModelProperty("任务结束时间")
  private Date taskEndTime;

  @ApiModelProperty("酒店消息接收人")
  private String hotelMsgUserId;

  @ApiModelProperty("巡查人时间类型")
  private String msgCheckerTimeType;

  @ApiModelProperty("酒店时间类型")
  private String hotelUserTimeType;

  @ApiModelProperty("酒店通知时间")
  private Date hotelMsgTime;

  @ApiModelProperty("通知巡查人时间")
  private Date msgCheckerTime;

  @ApiModelProperty("任务类型（自检/巡检）")
  private String taskType;

  @ApiModelProperty("创建人id")
  private Integer createUser;



  //任务id
  private Integer taskId;

  //时间type
  private String timeType;

}
