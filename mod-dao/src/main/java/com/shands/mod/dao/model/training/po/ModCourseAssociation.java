package com.shands.mod.dao.model.training.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;

/**
 * 分组排序关联表(ModCourseAssociation)实体类
 *
 * <AUTHOR>
 * @since 2022-08-17 15:09:47
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModCourseAssociation implements Serializable {
    private static final long serialVersionUID = -36293075117204761L;
    /**
     * 关联表id
     */
    private Integer id;
    /**
     * 课程表id
     */
    private Integer courseId;
    /**
     * 合集表id
     */
    private Integer courseCollectId;
    /**
     * 分组id
     */
    private Integer courseGroupId;
    /**
     * 分组排序序号
     */
    private Integer sort;
    /**
     * 添加分组时间
     */
    private Date addGroupTime;
    /**
     * 判断分组类型
     */
    private String type;
    
    private Integer ifDelete;
    
    private String remark;
    
    private Date createTime;
    
    private Date updateTime;
    
    private Integer createUser;
    
    private Integer updateUser;
    
    private Integer version;
}

