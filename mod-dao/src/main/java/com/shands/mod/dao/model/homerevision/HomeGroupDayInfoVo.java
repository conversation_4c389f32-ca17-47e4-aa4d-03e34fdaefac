package com.shands.mod.dao.model.homerevision;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class HomeGroupDayInfoVo {

  @ApiModelProperty(value = "品牌")
  private String hotelBrand;

  @ApiModelProperty(value = "事业部")
  private String hotelBizDepartment;

  @ApiModelProperty(value = "酒店名称")
  private String hotelName;

  @ApiModelProperty(value = "OCC")
  private Double resOcc;

  @ApiModelProperty(value = "ADR")
  private Double resAdr;

  @ApiModelProperty(value = "PEV")
  private Double resPev;

  @ApiModelProperty(value = "营业收入")
  private Double resRys;

  @ApiModelProperty(value = "餐饮收入")
  private Double resCy;

  @ApiModelProperty(value = "客房收入")
  private Double resRoom;

  @ApiModelProperty(value = "其他收入")
  private Double resOther;

  private Double monthOcc;

  private Double monthAdr;

  private Double monthPev;

  private Double monthRys;

  private Double monthCy;

  private Double monthRoom;

  private Double monthOther;

  private Double yearOcc;

  private Double yearAdr;

  private Double yearPev;

  private Double yearRys;

  private Double yearCy;

  private Double yearRoom;

  private Double yearOther;
}
