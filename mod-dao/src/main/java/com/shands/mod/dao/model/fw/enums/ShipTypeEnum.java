package com.shands.mod.dao.model.fw.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ShipTypeEnum {
  CHECKOUT(1, "离开酒店"),
  CHECKIN(2, "进入酒店"),
  ;

  private int code;

  private String type;

  public static ShipTypeEnum intToEnum(int i) {
    switch (i) {
      case 1:
        return CHECKOUT;
      case 2:
        return CHECKIN;
    }
    return null;
  }
}
