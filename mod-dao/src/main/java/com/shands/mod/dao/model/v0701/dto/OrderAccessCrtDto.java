package com.shands.mod.dao.model.v0701.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/7/15
 * @desc 出入服务订单创建Dto
*/
@Data
@ApiModel(value = "出入服务创建Dto")
public class OrderAccessCrtDto {

  /**
   * 预约时间
   */
  @ApiModelProperty(value = "预约时间")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date appointmentTime;

  /**
   * 交通工具
   */
  @ApiModelProperty(value = "交通工具")
  private String vehicle;

  /**
   * 交通工具类型
   */
  @ApiModelProperty(value = "交通工具类型")
  private Integer tool;

  /**
   * 用途
   */
  @ApiModelProperty(value = "用途")
  private String purpose;

  /**
   * 用途 1:离店  2：入住
   */
  @ApiModelProperty(value = "用途")
  private Integer purposeType;

  /**
   * 出发地
   */
  @ApiModelProperty(value = "出发地")
  private String departure;

  /**
   * 目的地
   */
  @ApiModelProperty(value = "目的地")
  private String destination;

  /**
   * 预约人数
   */
  @ApiModelProperty(value = "预约人数")
  private Integer peopleNum;

  /**
   * 班次id
   */
  @ApiModelProperty(value = "班次id")
  private Integer accessId;

}
