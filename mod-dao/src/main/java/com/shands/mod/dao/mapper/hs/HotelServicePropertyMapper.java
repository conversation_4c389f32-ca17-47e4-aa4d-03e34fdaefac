package com.shands.mod.dao.mapper.hs;

import com.shands.mod.dao.model.hs.HotelServiceProperty;
import com.shands.mod.dao.model.req.hs.goods.ListReq;
import com.shands.mod.dao.model.res.hs.HotelServicePropertyRes;
import com.shands.mod.dao.model.res.hs.good.GoodsRes;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface HotelServicePropertyMapper {
  int deleteByPrimaryKey(Integer id);

  int insert(HotelServiceProperty record);

  int insertSelective(HotelServiceProperty record);

  HotelServiceProperty selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(HotelServiceProperty record);

  int updateByPrimaryKey(HotelServiceProperty record);

  List<HotelServicePropertyRes> getByComId(Map map);

  Integer getId(
      @Param("horelServiceId") Integer horelServiceId,
      @Param("groupId") Integer groupId,
      @Param("companyId") Integer companyId,
      @Param("property") String property);

  List<GoodsRes> listByServiceId(ListReq queryReq);
}
