<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.hs.HsWorkOrderEatInfoMapper">
  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.hs.HsWorkOrderEatInfo">
    <!--@mbg.generated-->
    <!--@Table hs_work_order_eat_info-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="good_id" jdbcType="INTEGER" property="goodId" />
    <result column="good_name" jdbcType="VARCHAR" property="goodName" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="size" jdbcType="SMALLINT" property="size" />
    <result column="people" jdbcType="SMALLINT" property="people" />
    <result column="work_order_id" jdbcType="INTEGER" property="workOrderId" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, good_id, good_name, `type`, price, total_price, `size`, people, work_order_id,specifications
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hs_work_order_eat_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from hs_work_order_eat_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.shands.mod.dao.model.hs.HsWorkOrderEatInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hs_work_order_eat_info (good_id, good_name, `type`, 
      price, total_price, `size`, 
      people, work_order_id)
    values (#{goodId,jdbcType=INTEGER}, #{goodName,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{price,jdbcType=DECIMAL}, #{totalPrice,jdbcType=DECIMAL}, #{size,jdbcType=SMALLINT}, 
      #{people,jdbcType=SMALLINT}, #{workOrderId,jdbcType=INTEGER},#{specifications,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shands.mod.dao.model.hs.HsWorkOrderEatInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hs_work_order_eat_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="goodId != null">
        good_id,
      </if>
      <if test="goodName != null and goodName != ''">
        good_name,
      </if>
      <if test="type != null and type != ''">
        `type`,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="size != null">
        `size`,
      </if>
      <if test="people != null">
        people,
      </if>
      <if test="workOrderId != null">
        work_order_id,
      </if>
      <if test="specifications != null and specifications != ''">
        Specifications
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="goodId != null">
        #{goodId,jdbcType=INTEGER},
      </if>
      <if test="goodName != null and goodName != ''">
        #{goodName,jdbcType=VARCHAR},
      </if>
      <if test="type != null and type != ''">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="size != null">
        #{size,jdbcType=SMALLINT},
      </if>
      <if test="people != null">
        #{people,jdbcType=SMALLINT},
      </if>
      <if test="workOrderId != null">
        #{workOrderId,jdbcType=INTEGER},
      </if>
      <if test="specifications != null and specifications != ''">
        #{specifications,jdbcType=VARCHAR}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.hs.HsWorkOrderEatInfo">
    <!--@mbg.generated-->
    update hs_work_order_eat_info
    <set>
      <if test="goodId != null">
        good_id = #{goodId,jdbcType=INTEGER},
      </if>
      <if test="goodName != null and goodName != ''">
        good_name = #{goodName,jdbcType=VARCHAR},
      </if>
      <if test="type != null and type != ''">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="size != null">
        `size` = #{size,jdbcType=SMALLINT},
      </if>
      <if test="people != null">
        people = #{people,jdbcType=SMALLINT},
      </if>
      <if test="workOrderId != null">
        work_order_id = #{workOrderId,jdbcType=INTEGER},
      </if>
      <if test="specifications != null and specifications != ''">
        Specifications = #{specifications,jdbcType=VARCHAR}
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.hs.HsWorkOrderEatInfo">
    <!--@mbg.generated-->
    update hs_work_order_eat_info
    set good_id = #{goodId,jdbcType=INTEGER},
      good_name = #{goodName,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      `size` = #{size,jdbcType=SMALLINT},
      people = #{people,jdbcType=SMALLINT},
      work_order_id = #{workOrderId,jdbcType=INTEGER},
      Specifications = #{specifications,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hs_work_order_eat_info
    (good_id, good_name, `type`, price, total_price, `size`, people, work_order_id,Specifications)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.goodId,jdbcType=INTEGER}, #{item.goodName,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, 
        #{item.price,jdbcType=DECIMAL}, #{item.totalPrice,jdbcType=DECIMAL}, #{item.size,jdbcType=SMALLINT}, 
        #{item.people,jdbcType=SMALLINT}, #{item.workOrderId,jdbcType=INTEGER},#{item.specifications,jdbcType=VARCHAR})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2020-07-20-->
  <select id="findAllByWorkOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hs_work_order_eat_info
        where work_order_id=#{workOrderId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2020-07-24-->
  <select id="findByWorkOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hs_work_order_eat_info
        where work_order_id=#{workOrderId,jdbcType=INTEGER}
    </select>
</mapper>