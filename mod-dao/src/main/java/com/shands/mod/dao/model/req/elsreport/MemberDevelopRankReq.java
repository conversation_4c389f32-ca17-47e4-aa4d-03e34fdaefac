package com.shands.mod.dao.model.req.elsreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("会员发展排行榜接口请求参数")
@Data
public class MemberDevelopRankReq {

  @ApiModelProperty("酒店code")
  private String hotelCode;

  @ApiModelProperty("业务日期")
  private String dateStr;

  @ApiModelProperty("时间维度")
  private String rankDateEnum;

  @ApiModelProperty("排行维度")
  private String rankWayEnum;

  @ApiModelProperty("分类维度")
  private String statisticalEnum;

  @ApiModelProperty("品牌code")
  private String brandCode;
  @ApiModelProperty("事业部code")
  private String divisionCode;

  @ApiModelProperty("管理方式code")
  private String cooperationCode;
  private Integer pageNo;
  private Integer pageSize;

  @ApiModelProperty("排行页面区分状态 TOP ; ALL")
  private String rankType;

  @ApiModelProperty("开始时间 yyyy-MM-dd")
  private String startTime;

  @ApiModelProperty("结束时间 yyyy-MM-dd")
  private String endTime;
}
