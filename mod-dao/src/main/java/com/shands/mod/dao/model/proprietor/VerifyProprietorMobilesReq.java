package com.shands.mod.dao.model.proprietor;

import com.shands.mod.dao.model.MobileAndAreaCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 用于校验手机号是否属于业主的请求Req
 */
@Data
@ApiModel(description = "检查手机号是否属于业主的请求")
public class VerifyProprietorMobilesReq {

    @NotEmpty(message = "手机号列表不能为空")
    @ApiModelProperty(value = "需要校验的手机号列表", required = true)
    private List<MobileAndAreaCode> mobileAndAreaCodeList;
}
