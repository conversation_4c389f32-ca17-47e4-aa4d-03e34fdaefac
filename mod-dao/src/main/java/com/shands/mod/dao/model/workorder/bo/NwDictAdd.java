package com.shands.mod.dao.model.workorder.bo;

import com.shands.mod.dao.model.workorder.enums.DictTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class NwDictAdd {

  @NotNull(message = "名称不能为空")
  @ApiModelProperty(value="名称",required = true)
  private String name;

  @NotNull(message = "类型不能为空")
  @ApiModelProperty(value="类型",required = true)
  private DictTypeEnum type;

  @ApiModelProperty(value="展示方式")
  private String showType;

  @ApiModelProperty(value="字段详情")
  private List<NwDictInfoAdd> infoList;

}
