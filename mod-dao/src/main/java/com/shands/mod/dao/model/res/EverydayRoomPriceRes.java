package com.shands.mod.dao.model.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @createDate 2020/12/12 4:13 下午 <br>
 * @see com.shands.mod.dao.model.res <br>
 */
@ApiModel("入住订单每日房价返回参数")
public class EverydayRoomPriceRes {

  @ApiModelProperty("pmsId")
  private String pmsId;

  @ApiModelProperty("单价")
  private Double price;

  @ApiModelProperty("日期")
  private String time;

  @ApiModelProperty("单日总价")
  private Double totalAmount;

  public String getPmsId() {
    return pmsId;
  }

  public void setPmsId(String pmsId) {
    this.pmsId = pmsId;
  }

  public Double getPrice() {
    return price;
  }

  public void setPrice(Double price) {
    this.price = price;
  }

  public String getTime() {
    return time;
  }

  public void setTime(String time) {
    this.time = time;
  }

  public Double getTotalAmount() {
    return totalAmount;
  }

  public void setTotalAmount(Double totalAmount) {
    this.totalAmount = totalAmount;
  }
}
