package com.shands.mod.dao.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum MessageNoticeEnum {

  NEW_WORK_ORDER("新工单提醒", "{0}需要{1}，请及时处理", "{0}需要{1}，请速至德胧生态APP处理","0"),
  SEND_TO("工单抄送提醒", "{0}需要{1}，工单已抄送给你，请及时关注","{0}需要{1}，工单已抄送给你，请速至德胧生态APP处理","1"),
  TRANSFER("工单转单提醒", "{0}需要{1}，工单已转交给你，请及时处理", "{0}需要{1}，工单已转交给你，请速至德胧生态APP处理","2"),
  UPDATE("工单升级提醒", "{0}需要的{1}已超时升级，请速处理", "{0}需要的{1}已超时升级，请速至德胧生态APP处理","3"),
  CREATE_NOTIFICATION("工单消息提醒", "{0}需要的{1}已创建，请及时关注", "{0}需要的{1}已创建，请速至德胧生态APP处理","4"),
  COMPLETE_NOTIFICATION("工单消息提醒", "{0}需要的{1}已完成，请及时关注", "{0}需要的{1}已完成，请速至德胧生态APP处理","5"),
  ;

  private final String title;
  private final String context;
  private final String smsContext;
  private final String code;

  /**
   * 通过code查询枚举类型
   *
   * @param code 枚举code
   * @return 枚举类型
   */
  public static MessageNoticeEnum findEnumByCode(String code) {
    return Arrays.stream(MessageNoticeEnum.values()).filter(notice -> notice.getCode().equals(code))
        .findFirst().orElse(null);
  }

}
