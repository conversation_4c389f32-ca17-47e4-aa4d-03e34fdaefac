package com.shands.mod.dao.model.req.hs.consumer;

import com.shands.mod.dao.model.fw.other.CarServiceReq;
import com.shands.mod.dao.model.fw.other.EatServiceReq;
import com.shands.mod.dao.model.fw.other.GoodServiceReq;
import com.shands.mod.dao.model.fw.other.ShipServiceReq;
import com.shands.mod.dao.model.hs.other.ConsumerNoteWithOrder;
import com.shands.mod.dao.model.req.hs.HsBase;
import com.shands.mod.dao.model.res.hs.sell.SellRes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 订单创建参数 */
@Data
@NoArgsConstructor
@ApiModel
public class ConsumerOrderAddReq extends HsBase {

  /** 登录人id */
  @NotNull(message = "登陆人id不能为空")
  @ApiModelProperty(required = true, value = "登陆人id")
  private String loginId;

  /** 服务类型ID */
  @NotNull(message = "服务类型id不能为空")
  @ApiModelProperty(required = true, value = "服务类型ID")
  private Integer hotelServiceId;

  /** 客户备注 */
  @ApiModelProperty(value = "客户备注")
  private String remark;
  /** 酒店名称 */
  @NotEmpty(message = "酒店名称不能为空")
  @ApiModelProperty(required = true, value = "酒店名称")
  private String hotelName;
  /** 房号 */
  //  @NotNull(message = "房间号不能为空")
  @ApiModelProperty(value = "房间号")
  private String roomNum;
  /** 提报人姓名 */
  @ApiModelProperty(value = "提报人姓名")
  private String consumerName;
  /** 联系电话 */
  @ApiModelProperty(value = "联系电话")
  private String consumerPhone;
  /** 图片 */
  @ApiModelProperty(value = "图片")
  private String picUrls;

  /** 订单内容 */
  @Valid
  private ConsumerNoteWithOrder content;

  /** 酒店服务拓展信息ids */
  @ApiModelProperty(value = "酒店服务拓展信息ids")
  private String hotelServiceExtendIds;

  /** 是否预约,0false,1true */
  @ApiModelProperty(value = "是否预约")
  private Integer subscribe;

  /** 预约时间 */
  @ApiModelProperty(value = "预约时间")
  private Date subTime;

  /** 2020年3月19日 订单类型, 官网,绿云等 */
  @ApiModelProperty(value = "订单类型")
  private String orderType;

  private String orderId;

  private List<SellRes> resList = new ArrayList<>();

  /** 酒店id */
  @ApiModelProperty(value = "酒店id")
  private Integer companyId;

  @ApiModelProperty(value = "集团id")
  private Integer groupId;

  @ApiModelProperty(value = "用车请求参数")
  private CarServiceReq carServiceReq = new CarServiceReq();

  @ApiModelProperty(value = "餐饮请求参数")
  private EatServiceReq eatServiceReq = new EatServiceReq();

  @ApiModelProperty(value = "快送,租赁请求参数")
  private GoodServiceReq goodServiceReq = new GoodServiceReq();


  @ApiModelProperty(value = "用船请求参数")
  private ShipServiceReq shipServiceReq = new ShipServiceReq();

  /** 1. Deliver_to_room 送到房间 1. Hall_Food 堂食 用餐方式 */
  private String diningStyle;

}
