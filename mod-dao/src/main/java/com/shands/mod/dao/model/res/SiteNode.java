package com.shands.mod.dao.model.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/** <AUTHOR> */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel
public class SiteNode {
  /** id */
  @ApiModelProperty("id")
  private Integer id;
  /** 站点id */
  @ApiModelProperty("站点id")
  private Integer siteId;
  /** 节点标题 */
  @ApiModelProperty("节点标题")
  private String nodeTitle;
  /** 节点描述 */
  @ApiModelProperty("节点描述")
  private String nodeDesc;
  /** 节点类型 */
  @ApiModelProperty("节点类型")
  private String nodeType;
  /** 节点排序 */
  @ApiModelProperty("节点排序")
  private Integer nodeOrder;
  /** 是否必须 */
  @ApiModelProperty("是否必须")
  private boolean nodeNecessary;
  /** 选项值 */
  @ApiModelProperty("选项值")
  private List<NodeOption> options;
  /** 节点值 */
  @ApiModelProperty("节点值")
  private String nodeValue;

  /** 节点id */
  @ApiModelProperty("节点id")
  private Integer valueId;
}
