package com.shands.mod.dao.model.newDataBoard.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/3
 * @desc 明星店设置Bo
*/
@Data
@ApiModel(value = "明星店设置Bo")
public class HotelStarSetBo {

  @ApiModelProperty(value = "明星店标识 1：明星店 0：普通店")
  @NotNull(message = "明星店标识不能为空")
  private Integer starFlag;

  @ApiModelProperty(value = "酒店ID")
  @NotNull(message = "酒店ID不能为空")
  private Integer hotelId;

}
