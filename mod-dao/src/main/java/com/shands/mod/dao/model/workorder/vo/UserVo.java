package com.shands.mod.dao.model.workorder.vo;

import com.shands.mod.dao.model.res.clean.UserAndDeptRes;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class UserVo {

  @ApiModelProperty("部门名称")
  private String deptName;

  @ApiModelProperty("人员列表")
  private List<UserAndDeptRes> userAndDeptResList;

}
