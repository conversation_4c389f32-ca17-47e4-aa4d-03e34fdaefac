package com.shands.mod.dao.model.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/** <AUTHOR> */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel
public class PatrolGetPlanRes {
  /** 方案id */
  @ApiModelProperty("方案id")
  private Integer id;
  /** 方案sn */
  @ApiModelProperty("方案sn")
  private String sn;
  /** 方案名称 */
  @ApiModelProperty("方案名称")
  private String name;
  /** 站点list */
  @ApiModelProperty("站点list")
  private List<PlanSite> children;

  public List<PlanSite> getChildren() {
    if (null == children) {
      children = new ArrayList<>();
    }
    return children;
  }
}
