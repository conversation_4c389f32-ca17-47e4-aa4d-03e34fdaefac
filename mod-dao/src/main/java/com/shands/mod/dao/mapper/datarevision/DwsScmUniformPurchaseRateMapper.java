package com.shands.mod.dao.mapper.datarevision;

import com.shands.mod.dao.model.datarevision.bo.ManagementAnalysisBo;
import com.shands.mod.dao.model.datarevision.vo.ClassificationVo;
import com.shands.mod.dao.model.datarevision.vo.HomeDataDetailVo;
import com.shands.mod.dao.model.datarevision.vo.ScmHotelRankVo;
import com.shands.mod.dao.model.datarevision.vo.ScmRankVo;
import java.util.Date;
import java.util.List;
import com.shands.mod.dao.model.homerevision.RevenueDayInfoVo;
import com.shands.mod.dao.model.homerevision.RevenueHotelDataVo;
import org.apache.ibatis.annotations.Param;


public interface DwsScmUniformPurchaseRateMapper {

  List<String> homeCodes(@Param("codes") List<String> codes,@Param("dateTime") Date dateTime,@Param("type") String type);

  HomeDataDetailVo selectBlocDaySupplyChainMoney(@Param("maxDate") Date maxDate,
      @Param("yesterday") Date yesterday,
      @Param("lastWeekDay") Date lastWeekDay,
      @Param("codes") List<String> codes);

  HomeDataDetailVo selectBlocMonthSupplyChainMoney(@Param("maxDate") Date maxDate,
      @Param("lastMonthDay") String lastMonthDay,
      @Param("lastYearMonthDay") String lastYearMonthDay,
      @Param("codes") List<String> codes);


  HomeDataDetailVo selectBlocMonthUniformRecoveryRate(@Param("maxDate") Date maxDate,
      @Param("lastMonthDay") String lastMonthDay,
      @Param("lastQuarterDay") String lastQuarterDay,
      @Param("codes") List<String> codes);

  List<ScmRankVo> getDataRankAllByDept(@Param("bizDate") String bizDate,@Param("deptCode")List<String> deptCode);

  List<ScmRankVo> getDataRankMaterialByDept(@Param("bizDate") String bizDate,@Param("deptCode")List<String> deptCode);

  List<ScmRankVo> getDataRankAllByBrand(@Param("bizDate") String bizDate,@Param("brandCode")List<String> brandCode);

  List<ScmRankVo> getDataRankMaterialByBrand(@Param("bizDate") String bizDate,@Param("brandCode")List<String> brandCode);

  List<ScmHotelRankVo> getDataRankAllByHotel(@Param("bizDate") String bizDate,@Param("brandCode") String brandCode,@Param("deptCode")String deptCode,@Param("hotelCode") String hotelCode);

  List<ScmHotelRankVo> getDataRankMaterialByHotel(@Param("bizDate") String bizDate,@Param("brandCode") String brandCode,@Param("deptCode")String deptCode,@Param("hotelCode") String hotelCode);

  List<ScmRankVo> getDataRankAllByHotelTotal(@Param("bizDate") String bizDate,@Param("brandCode") String brandCode,@Param("deptCode")String deptCode);

  List<ScmRankVo> getDataRankMaterialByHotelTotal(@Param("bizDate") String bizDate,@Param("brandCode") String brandCode,@Param("deptCode")String deptCode);

  List<ClassificationVo> selectAnalysis(@Param("type") String type,@Param("managementAnalysisBo") ManagementAnalysisBo managementAnalysisBo);

  RevenueHotelDataVo selectHotelDayRevenues(@Param("dayTime") String dayTime,
      @Param("hotelCode") String hotelCode);

  RevenueHotelDataVo selectHotelMonthRevenues(@Param("dayTime") String dayTime,
      @Param("monthTime") String monthTime,
      @Param("timeType") String timeType,
      @Param("hotelCode") String hotelCode);

  RevenueHotelDataVo selectHotelYearRevenues(@Param("dayTime") String dayTime,
      @Param("monthTime") String monthTime,
      @Param("yearTime") String yearTime,
      @Param("timeType") String timeType,
      @Param("staYearTime") String staYearTime,
      @Param("hotelCode") String hotelCode);

  RevenueHotelDataVo selectHotelStaYearRevenues(@Param("staYearTime") String staYearTime,
      @Param("hotelCode") String hotelCode);

  RevenueHotelDataVo selectContrastMonthRevenues(
      @Param("time") String time,
      @Param("time2") String time2,
      @Param("hotelCode") String hotelCode,
      @Param("type") String type);

  RevenueHotelDataVo selectContrastYearRevenues(
      @Param("time") String time,
      @Param("time2") String time2,
      @Param("hotelCode") String hotelCode);

  List<RevenueDayInfoVo> selectDayRevenues(@Param("dayTime") String dayTime,
      @Param("staYearTime") String staYearTime,
      @Param("queryType") String queryType,
      @Param("dataGroup") String dataGroup,
      @Param("revenumType") String revenumType,
      @Param("dataEnum") String dataEnum,
      @Param("cooperationCode") String cooperationCode,
      @Param("codes") List<String> codes);

  List<RevenueDayInfoVo> selectDayChildsRevenues(@Param("dayTime") String dayTime,
      @Param("dataType") String dataType,
      @Param("staYearTime") String staYearTime,
      @Param("queryType") String queryType,
      @Param("revenumType") String revenumType,
      @Param("dataEnum") String dataEnum,
      @Param("cooperationCode") String cooperationCode,
      @Param("codes") List<String> codes);

  List<RevenueDayInfoVo> selectMonthRevenues(@Param("monthTime") String monthTime,
      @Param("staYearTime") String staYearTime,
      @Param("queryType") String queryType,
      @Param("dataGroup") String dataGroup,
      @Param("revenumType") String revenumType,
      @Param("dataEnum") String dataEnum,
      @Param("cooperationCode") String cooperationCode,
      @Param("type") String type,
      @Param("time2") String time2,
      @Param("codes") List<String> codes);

  List<RevenueDayInfoVo> selectMonthChildsRevenues(@Param("monthTime") String monthTime,
      @Param("dataType") String dataType,
      @Param("staYearTime") String staYearTime,
      @Param("queryType") String queryType,
      @Param("revenumType") String revenumType,
      @Param("dataEnum") String dataEnum,
      @Param("cooperationCode") String cooperationCode,
      @Param("type") String type,
      @Param("time2") String time2,
      @Param("codes") List<String> codes);

  List<RevenueDayInfoVo> selectYearRevenues(@Param("yearTime") String yearTime,
      @Param("staYearTime") String staYearTime,
      @Param("queryType") String queryType,
      @Param("dataGroup") String dataGroup,
      @Param("revenumType") String revenumType,
      @Param("dataEnum") String dataEnum,
      @Param("cooperationCode") String cooperationCode,
      @Param("time") String time,
      @Param("time2") String time2,
      @Param("codes") List<String> codes);

  List<RevenueDayInfoVo> selectYearChildsRevenues(@Param("yearTime") String yearTime,
      @Param("dataType") String dataType,
      @Param("staYearTime") String staYearTime,
      @Param("queryType") String queryType,
      @Param("revenumType") String revenumType,
      @Param("dataEnum") String dataEnum,
      @Param("cooperationCode") String cooperationCode,
      @Param("time") String time,
      @Param("time2") String time2,
      @Param("codes") List<String> codes);
}