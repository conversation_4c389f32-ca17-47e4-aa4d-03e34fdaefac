package com.shands.mod.dao.model.req.shopping;

import lombok.Data;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-06-01
 * @description 商品细节接收
 */
@Data
public class CommodityDto {

  /**
   * 菜品code
   */
  private String pluCode;
  /**
   * 菜品名称
   */
  private String pluName;
  /**
   * 菜品规格/做法  code
   */
  private String pluPracticeCode;
  /**
   * 菜品规格/做法  名称
   */
  private String pluPracticeName;
  /**
   * 购买数量
   */
  private Integer quantity;
  /**
   * 明细单价
   */
  private BigDecimal detailPrice;
  /**
   * 明细总价(明细单价*购买数量)
   */
  private BigDecimal totalPrice;
  /**
   * hs_shopping表的id
   */
  private Integer shoppingId;
  /**
   * 营业点code  hs_pos_outlet
   */
  private String outletCode;
  /**
   * 营业点名称
   */
  private String outletName;
  /**
   * 菜本code  hs_pos_note
   */
  private String noteCode;
  /**
   * 菜本名称
   */
  private String noteName;
}
