package com.shands.mod.dao.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 网评表(ModComment)实体类
 *
 * <AUTHOR>
 * @since 2022-03-28 10:47:54
 */
public class ModComment implements Serializable {

  private static final long serialVersionUID = -51220469745506895L;
  /**
   * 表主键
   */
  private Integer id;
  /**
   * 酒店编码
   */
  private String hotelCode;
  /**
   * 酒店名称
   */
  private String hotelName;
  /**
   * 事业部code
   */
  private String bizDepartmentCode;
  /**
   * 事业部名称
   */
  private String hotelBizDepartment;
  /**
   * 品牌code
   */
  private String brandCode;
  /**
   * 品牌名称
   */
  private String hotelBrand;
  /**
   * 营业日期
   */
  private Date bizDate;
  /**
   * 页面分
   */
  private Double totalSorce;
  /**
   * 点评数
   */
  private Integer commentTotal;
  /**
   * 本日新增点评数
   */
  private Integer currdayCommentN;
  /**
   * 渠道
   */
  private String otaType;
  /**
   * 每日新增网评分
   */
  private Double currdayScoreN;


  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public String getHotelCode() {
    return hotelCode;
  }

  public void setHotelCode(String hotelCode) {
    this.hotelCode = hotelCode;
  }

  public String getHotelName() {
    return hotelName;
  }

  public void setHotelName(String hotelName) {
    this.hotelName = hotelName;
  }

  public String getBizDepartmentCode() {
    return bizDepartmentCode;
  }

  public void setBizDepartmentCode(String bizDepartmentCode) {
    this.bizDepartmentCode = bizDepartmentCode;
  }

  public String getHotelBizDepartment() {
    return hotelBizDepartment;
  }

  public void setHotelBizDepartment(String hotelBizDepartment) {
    this.hotelBizDepartment = hotelBizDepartment;
  }

  public String getBrandCode() {
    return brandCode;
  }

  public void setBrandCode(String brandCode) {
    this.brandCode = brandCode;
  }

  public String getHotelBrand() {
    return hotelBrand;
  }

  public void setHotelBrand(String hotelBrand) {
    this.hotelBrand = hotelBrand;
  }

  public Date getBizDate() {
    return bizDate;
  }

  public void setBizDate(Date bizDate) {
    this.bizDate = bizDate;
  }

  public Double getTotalSorce() {
    return totalSorce;
  }

  public void setTotalSorce(Double totalSorce) {
    this.totalSorce = totalSorce;
  }

  public Integer getCommentTotal() {
    return commentTotal;
  }

  public void setCommentTotal(Integer commentTotal) {
    this.commentTotal = commentTotal;
  }

  public Integer getCurrdayCommentN() {
    return currdayCommentN;
  }

  public void setCurrdayCommentN(Integer currdayCommentN) {
    this.currdayCommentN = currdayCommentN;
  }

  public String getOtaType() {
    return otaType;
  }

  public void setOtaType(String otaType) {
    this.otaType = otaType;
  }

  public Double getCurrdayScoreN() {
    return currdayScoreN;
  }

  public void setCurrdayScoreN(Double currdayScoreN) {
    this.currdayScoreN = currdayScoreN;
  }

}

