package com.shands.mod.dao.model.proprietor;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 业主卡发放记录表(mod_proprietor_card_send_record)实体类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModProprietorCardSendRecord {

    /**
     * 自增主键id
     */
    private Long id;

    /**
     * 业主手机号
     */
    private String mobile;

    /**
     * 业主手机号区号
     */
    private String areaCode;

    /**
     * 卡id
     */
    private Integer cardId;

    /**
     * 发放结果（0:失败 1: 成功）
     */
    private Integer sendRes;

    /**
     * 创建时间（申请时间）
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 逻辑删除标志（0:未删除 1:删除）
     */
    private Integer delFlag;
}
