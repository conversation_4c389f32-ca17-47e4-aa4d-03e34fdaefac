package com.shands.mod.dao.mapper.remaindergroup;

import com.shands.mod.dao.model.ReminderGroup;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【reminder_group(工单提醒小组表)】的数据库操作Mapper
* @createDate 2023-08-07 17:52:41
* @Entity com.shands.mod.dao.model.ReminderGroup
*/
public interface ReminderGroupMapper {
  int insert(ReminderGroup reminderGroup);

  int insertBatch(@Param("list") List<ReminderGroup> reminderGroupList);

  int insertOrUpdateBatch(@Param("list") List<ReminderGroup> reminderGroupList);


  int deleteById(Long id);

  int deleteByIds(@Param("ids") List<Long> ids);

  int deleteByCompanyIdAndSource(@Param("companyId") int companyId, @Param("source") String source);
  int update(ReminderGroup reminderGroup);

  int updateByPrimaryKeySelective(ReminderGroup reminderGroup);
  ReminderGroup selectById(Long id);

  ReminderGroup selectByCompanyIdAndGroupName(@Param("companyId") Integer companyId, @Param("groupName") String groupName);

  List<ReminderGroup> selectByCompanyIdAndStatus(@Param("companyId") Integer companyId, @Param("status") Integer status);
  
  List<ReminderGroup> selectByCompanyIdAndSource(@Param("companyId") Integer companyId, @Param("source") String source);


  ReminderGroup uniqSelect(@Param("companyId") int companyId,@Param("groupName") String groupName);


}




