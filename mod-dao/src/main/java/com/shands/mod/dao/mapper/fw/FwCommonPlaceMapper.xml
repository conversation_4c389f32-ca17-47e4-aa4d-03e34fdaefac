<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.fw.FwCommonPlaceMapper">

    <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.fw.FwCommonPlace">
        <!--@Table fw_common_place-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="placeCode" column="place_code" jdbcType="VARCHAR"/>
        <result property="placeName" column="place_name" jdbcType="VARCHAR"/>
        <result property="lngGd" column="lng_gd" jdbcType="VARCHAR"/>
        <result property="latGd" column="lat_gd" jdbcType="VARCHAR"/>
        <result property="companyId" column="company_id" jdbcType="INTEGER"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          id, place_code, place_name, lng_gd, lat_gd, company_id, deleted, create_time, create_user, update_time, update_user
        from fw_common_place
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          id, place_code, place_name, lng_gd, lat_gd, company_id, deleted, create_time, create_user, update_time, update_user
        from fw_common_place
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select
          id, place_code, place_name, lng_gd, lat_gd, company_id, deleted, create_time, create_user, update_time, update_user
        from fw_common_place
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="placeCode != null and placeCode != ''">
                and place_code = #{placeCode}
            </if>
            <if test="placeName != null and placeName != ''">
                and place_name = #{placeName}
            </if>
            <if test="lngGd != null and lngGd != ''">
                and lng_gd = #{lngGd}
            </if>
            <if test="latGd != null and latGd != ''">
                and lat_gd = #{latGd}
            </if>
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into fw_common_place(place_code, place_name, lng_gd, lat_gd, company_id, deleted, create_time, create_user, update_time, update_user)
        values (#{placeCode}, #{placeName}, #{lngGd}, #{latGd}, #{companyId}, #{deleted}, #{createTime}, #{createUser}, #{updateTime}, #{updateUser})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update fw_common_place
        <set>
            <if test="placeCode != null and placeCode != ''">
                place_code = #{placeCode},
            </if>
            <if test="placeName != null and placeName != ''">
                place_name = #{placeName},
            </if>
            <if test="lngGd != null and lngGd != ''">
                lng_gd = #{lngGd},
            </if>
            <if test="latGd != null and latGd != ''">
                lat_gd = #{latGd},
            </if>
            <if test="companyId != null">
                company_id = #{companyId},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="createUser != null">
                create_user = #{createUser},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from fw_common_place where id = #{id}
    </delete>

</mapper>