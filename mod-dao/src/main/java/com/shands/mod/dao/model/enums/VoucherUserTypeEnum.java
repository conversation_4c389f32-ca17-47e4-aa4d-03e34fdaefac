package com.shands.mod.dao.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/1/23 17:31
 */
@Getter
@AllArgsConstructor
public enum VoucherUserTypeEnum {

  ONLINE(0, "线上使用"),

  OFFLINE(1, "线下使用"),

  ;

  private final Integer Code;

  private final String desc;

  public static VoucherUserTypeEnum findEnumByCode(Integer code) {
    for (VoucherUserTypeEnum type : VoucherUserTypeEnum.values()) {
      if (Objects.equals(type.getCode(), code)) {
        return type;
      }
    }
    return null;
  }
}
