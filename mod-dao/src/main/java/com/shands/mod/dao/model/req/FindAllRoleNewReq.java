package com.shands.mod.dao.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @createDate 2020/09/10 10:58 上午 <br>
 * @see com.shands.mod.dao.model.req <br>
 */
@ApiModel(value = "查询所有角色请求参数")
public class FindAllRoleNewReq {
  @ApiModelProperty(value = "角色名")
  private String roleName;
  @ApiModelProperty(value = "页码")
  private Integer page;
  @ApiModelProperty(value = "每页多少条")
  private Integer size;

  public String getRoleName() {
    return roleName;
  }

  public void setRoleName(String roleName) {
    this.roleName = roleName;
  }

  public Integer getPage() {
    return page;
  }

  public void setPage(Integer page) {
    this.page = page;
  }

  public Integer getSize() {
    return size;
  }

  public void setSize(Integer size) {
    this.size = size;
  }
}
