package com.shands.mod.dao.model.req.hs.consumer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @createDate 2021/02/22 下午5:48 <br>
 * @see com.shands.mod.websocket.model <br>
 */
@ApiModel("WebSocket请求参数")
public class WebSocketReq {

  @ApiModelProperty("公司id")
  private String companyId;

  @ApiModelProperty("用户id")
  private String userId;

  @ApiModelProperty("内容")
  private String message;

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public String getCompanyId() {
    return companyId;
  }

  public void setCompanyId(String companyId) {
    this.companyId = companyId;
  }

  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }
}
