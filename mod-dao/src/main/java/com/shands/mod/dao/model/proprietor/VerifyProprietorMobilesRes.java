package com.shands.mod.dao.model.proprietor;

import com.shands.mod.dao.model.MobileAndAreaCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 用于校验手机号是否属于业主的响应Res
 */
@Data
@ApiModel(description = "验证手机号是否属于业主的响应")
public class VerifyProprietorMobilesRes extends MobileAndAreaCode {

    @ApiModelProperty(value = "是否是业主（true表示是业主，false表示不是业主）")
    private Boolean isProprietor;
}
