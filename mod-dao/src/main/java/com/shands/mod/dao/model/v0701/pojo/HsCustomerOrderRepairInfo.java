package com.shands.mod.dao.model.v0701.pojo;

import java.util.Date;
import java.io.Serializable;

/**
 * 服务明细-维修服务(HsCustomerOrderRepairInfo)实体类
 *
 * <AUTHOR>
 * @since 2020-07-10 11:29:26
 */
public class HsCustomerOrderRepairInfo implements Serializable {
    private static final long serialVersionUID = 369752610809859385L;
    /**
    * 主键ID
    */
    private Integer id;
    /**
    * 订单编号
    */
    private Integer customerOrderId;
    /**
    * 服务内容
    */
    private String serviceContent;
    /**
    * 服务预约时间
    */
    private Date appointmentTime;
    /**
    * 服务图片URL
    */
    private String servicePic;
    /**
    * 版本号
    */
    private Integer version;
    /**
    * 删除标志 1：已删除 0：未删除
    */
    private Integer deleted;
    /**
    * 创建用户
    */
    private Integer createUser;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 修改用户
    */
    private Integer updateUser;
    /**
    * 修改时间
    */
    private Date updateTime;
    /**
    * 备注
    */
    private String remark;

  /**
   * 服务子项
   */
  private Integer expendId;

  public Integer getExpendId() {
    return expendId;
  }

  public void setExpendId(Integer expendId) {
    this.expendId = expendId;
  }

  public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCustomerOrderId() {
        return customerOrderId;
    }

    public void setCustomerOrderId(Integer customerOrderId) {
        this.customerOrderId = customerOrderId;
    }

    public String getServiceContent() {
        return serviceContent;
    }

    public void setServiceContent(String serviceContent) {
        this.serviceContent = serviceContent;
    }

    public Date getAppointmentTime() {
        return appointmentTime;
    }

    public void setAppointmentTime(Date appointmentTime) {
        this.appointmentTime = appointmentTime;
    }

    public String getServicePic() {
        return servicePic;
    }

    public void setServicePic(String servicePic) {
        this.servicePic = servicePic;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Integer getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Integer createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Integer updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}