<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.datarevision.AdsAppTradeRevenueDMapper">
  <resultMap type="com.shands.mod.dao.model.datarevision.po.AdsAppTradeRevenueD" id="AdsAppTradeRevenueDResultMap">
    <result property="id" column="id" jdbcType="INTEGER"/>
    <result property="hotelCode" column="hotel_code" jdbcType="VARCHAR"/>
    <result property="hotelName" column="hotel_name" jdbcType="VARCHAR"/>
    <result property="bizDepartmentCode" column="biz_department_code" jdbcType="VARCHAR"/>
    <result property="hotelBizDepartment" column="hotel_biz_department" jdbcType="VARCHAR"/>
    <result property="brandCode" column="biz_department_code" jdbcType="VARCHAR"/>
    <result property="hotelBrand" column="hotel_brand" jdbcType="VARCHAR"/>
    <result property="bizDate" column="biz_date" jdbcType="DATE"/>
    <result property="roomNum" column="room_num" jdbcType="INTEGER"/>
    <result property="memRoomsAmt" column="mem_rooms_amt" jdbcType="DOUBLE"/>
    <result property="memRoomNightsN" column="mem_room_nights_n" jdbcType="DOUBLE"/>
    <result property="roomNightsN" column="room_nights_n" jdbcType="DOUBLE"/>
  </resultMap>

  <sql id="baseColumn">
    id, hotel_code, hotel_name, biz_department_code, hotel_biz_department, biz_department_code, hotel_brand, biz_date, IFNULL(room_num, 0) as room_num, IFNULL(mem_rooms_amt, 0) as mem_rooms_amt, IFNULL(mem_room_nights_n, 0) as mem_room_nights_n, IFNULL(room_nights_n, 0) as room_nights_n
  </sql>

  <select id="complexSelect" resultMap="AdsAppTradeRevenueDResultMap">
    SELECT
    <include refid="baseColumn" />
    FROM ads_app_trade_revenue_d
    <where>
      <if test="startTime != null and startTime != ''">
        AND biz_date &gt;= #{startTime}
      </if>
      <if test="endTime != null and endTime != ''">
        AND biz_date &lt;= #{endTime}
      </if>
    </where>
  </select>


  <select id="selectRangeData" resultType="com.shands.mod.dao.model.statistics.dto.AdsAppTradeRevenueDDto">
    SELECT
    SUM(total_amt) as total_amt,
    SUM(total_room_amt) as total_room_amt,
    SUM(catering_amt) as catering_amt,
    SUM(other_amt) as other_amt,
    CONVERT((SUM(room_nights_n)/SUM(room_num)*100),DECIMAL(12,2)) as invest_occ,
    CONVERT((SUM(room_amt)/SUM(room_nights_n)),DECIMAL(12,2)) invest_adr,
    CONVERT((SUM(room_amt)/SUM(room_num)),DECIMAL(12,2)) invest_revpar,
    <!-- 间夜相关字段 -->
    SUM(bdw_room_nights_n) as bdw_room_nights_n,
    SUM(coope_room_nights_n) as coope_room_nights_n,
    SUM(offline_room_nights_n) as offline_room_nights_n,
    SUM(company_room_nights_n) as company_room_nights_n,
    SUM(card_room_nights_n) as card_room_nights_n,
    SUM(oneself_room_nights_n) as oneself_room_nights_n,
    SUM(ctrip_room_nights_n) as ctrip_room_nights_n,
    SUM(ptn_room_nights_n) as ptn_room_nights_n,
    SUM(taobao_room_nights_n) as taobao_room_nights_n,
    SUM(tiktok_room_nights_n) as tiktok_room_nights_n,
    SUM(tmc_room_nights_n) as tmc_room_nights_n,
    SUM(ids_room_nights_n) as ids_room_nights_n,
    SUM(tk_room_nights_n) as tk_room_nights_n,
    SUM(ts_room_nights_n) as ts_room_nights_n,
    SUM(qx_room_nights_n) as qx_room_nights_n,
    SUM(zx_room_nights_n) as zx_room_nights_n,
    SUM(wki_room_nights_n) as wki_room_nights_n,
    SUM(other_room_nights_n) as other_room_nights_n,
    <!-- APP消费会员数据 -->
    SUM(mem_app_room_nights_n) as mem_app_room_nights_n,
    SUM(mem_app_room_nights_m) as mem_app_room_nights_m,
    SUM(app_first_consume_mem_n) as app_first_consume_mem_n,
    SUM(app_repeat_consume_mem_n) as app_repeat_consume_mem_n,
    SUM(bf_app_room_nights_n) as bf_app_room_nights_n

    FROM ads_app_trade_revenue_d
    <where>
      <if test="startTime != null">
        AND biz_date &gt;= #{startTime}
      </if>
      <if test="endTime != null">
        AND biz_date &lt;= #{endTime}
      </if>
      <if test="hotelCode != null and hotelCode != ''">
        AND hotel_code = #{hotelCode}
      </if>
    </where>
  </select>


  <select id="selectRangeDataList" resultType="com.shands.mod.dao.model.statistics.dto.AdsAppTradeRevenueDDto">
    SELECT
      total_amt as total_amt,
      total_room_amt as total_room_amt,
      catering_amt as catering_amt,
      other_amt as other_amt,
      CONVERT((room_nights_n/room_num*100),DECIMAL(12,2)) as invest_occ,
      CONVERT((room_amt/room_nights_n),DECIMAL(12,2)) invest_adr,
      CONVERT((room_amt/room_num),DECIMAL(12,2)) invest_revpar,
      biz_date AS bizDate
    FROM ads_app_trade_revenue_d
    <where>
      <if test="startTime != null">
        AND biz_date &gt;= #{startTime}
      </if>
      <if test="endTime != null">
        AND biz_date &lt;= #{endTime}
      </if>
      <if test="hotelCode != null and hotelCode != ''">
        AND hotel_code = #{hotelCode}
      </if>
    </where>
  </select>

  <select id="queryNightAuditNum" resultMap="AdsAppTradeRevenueDResultMap">
    SELECT
    <include refid="baseColumn"/>
    FROM
      ads_app_trade_revenue_d
    WHERE
      biz_date = #{bizDate}
      AND hotel_code IN
      <foreach collection="hotelCodeList" item="hotelCode" open="(" separator="," close=")">
        #{hotelCode}
      </foreach>
      AND room_nights != 0
      AND room_nights IS NOT NULL
  </select>

</mapper>