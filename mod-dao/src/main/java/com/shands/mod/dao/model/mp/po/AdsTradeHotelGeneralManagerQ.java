package com.shands.mod.dao.model.mp.po;

import java.io.Serializable;
import java.util.Date;

/**
 * 总经理绩效评分(季度)(AdsTradeHotelGeneralManagerQ)实体类
 *
 * <AUTHOR>
 * @since 2023-02-06 10:20:11
 */
public class AdsTradeHotelGeneralManagerQ implements Serializable {
    private static final long serialVersionUID = -43466459101121899L;
    /**
     * 主键
     */
    private Object id;
    /**
     * 业务日期
     */
    private Date bizDate;
    /**
     * 业务日期(年季)
     */
    private String bizQuarter;
    /**
     * 事业部代码
     */
    private String departmentCode;
    /**
     * 事业部名称
     */
    private String departmentName;
    /**
     * 品牌代码
     */
    private String brandCode;
    /**
     * 品牌描述
     */
    private String brandName;
    /**
     * 管理方式code
     */
    private String manageCode;
    /**
     * 管理方式
     */
    private String manageMode;
    /**
     * 酒店代码
     */
    private String hotelCode;
    /**
     * 酒店名称
     */
    private String hotelName;
    /**
     * 酒店状态码:PREPARE=筹建、OPEN=在营
     */
    private String hotelStatusCode;
    /**
     * 酒店是否可控：0=否,1=是
     */
    private Integer isControl;
    /**
     * 酒店总经理ID
     */
    private Long hotelManagerId;
    /**
     * 酒店总经理姓名
     */
    private String hotelManagerName;
    /**
     * 交易额完成达标率(%)-当季累计
     */
    private String incomeRateQ;
    /**
     * 交易额预算完成率得分(100分)-当季累计
     */
    private Integer incomeScoreQ;
    /**
     * 网评意见量-当季累计
     */
    private Integer commentNQ;
    /**
     * C新增点评均分(5分)-当季累计
     */
    private Integer commentAvgScoreQ;
    /**
     * Z网评意见综合得分(100分)-当季累计
     */
    private String commentScoreQ;
    /**
     * D品牌物资采购达标率(%)-当季累计
     */
    private String procurementRateQ;
    /**
     * G品牌标准执行抽检达标率(%)
     */
    private String procurementCheckRate;
    /**
     * E品牌标准综合得分(100分)-当季累计
     */
    private String procurementScoreQ;
    /**
     * 百达星系会员综合得分(10分)-人工打分
     */
    private String bdxMemScore;
    /**
     * 消费会员数-当季累计
     */
    private Integer consumeMemNQ;
    /**
     * 新增消费会员数-当季累计
     */
    private Integer consumeMemIncNQ;
    /**
     * Z消费会员数量综合得分(100分)-当季累计
     */
    private String consumeMemScoreQ;
    /**
     * 工作标准(10分)-人工打分
     */
    private String workStandards;
    /**
     * 工作标准综合得分(10分)-人工打分
     */
    private String workStandardsScore;
    /**
     * M人才输送得分(10分)
     */
    private String talentScore;
    /**
     * O学习情况得分(5分)
     */
    private String learnScore;
    /**
     * P学习成长综合得分(15分)
     */
    private String learnUpScore;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;


    public Object getId() {
        return id;
    }

    public void setId(Object id) {
        this.id = id;
    }

    public Date getBizDate() {
        return bizDate;
    }

    public void setBizDate(Date bizDate) {
        this.bizDate = bizDate;
    }

    public String getBizQuarter() {
        return bizQuarter;
    }

    public void setBizQuarter(String bizQuarter) {
        this.bizQuarter = bizQuarter;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getManageCode() {
        return manageCode;
    }

    public void setManageCode(String manageCode) {
        this.manageCode = manageCode;
    }

    public String getManageMode() {
        return manageMode;
    }

    public void setManageMode(String manageMode) {
        this.manageMode = manageMode;
    }

    public String getHotelCode() {
        return hotelCode;
    }

    public void setHotelCode(String hotelCode) {
        this.hotelCode = hotelCode;
    }

    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    public String getHotelStatusCode() {
        return hotelStatusCode;
    }

    public void setHotelStatusCode(String hotelStatusCode) {
        this.hotelStatusCode = hotelStatusCode;
    }

    public Integer getIsControl() {
        return isControl;
    }

    public void setIsControl(Integer isControl) {
        this.isControl = isControl;
    }

    public Long getHotelManagerId() {
        return hotelManagerId;
    }

    public void setHotelManagerId(Long hotelManagerId) {
        this.hotelManagerId = hotelManagerId;
    }

    public String getHotelManagerName() {
        return hotelManagerName;
    }

    public void setHotelManagerName(String hotelManagerName) {
        this.hotelManagerName = hotelManagerName;
    }

    public String getIncomeRateQ() {
        return incomeRateQ;
    }

    public void setIncomeRateQ(String incomeRateQ) {
        this.incomeRateQ = incomeRateQ;
    }

    public Integer getIncomeScoreQ() {
        return incomeScoreQ;
    }

    public void setIncomeScoreQ(Integer incomeScoreQ) {
        this.incomeScoreQ = incomeScoreQ;
    }

    public Integer getCommentNQ() {
        return commentNQ;
    }

    public void setCommentNQ(Integer commentNQ) {
        this.commentNQ = commentNQ;
    }

    public Integer getCommentAvgScoreQ() {
        return commentAvgScoreQ;
    }

    public void setCommentAvgScoreQ(Integer commentAvgScoreQ) {
        this.commentAvgScoreQ = commentAvgScoreQ;
    }

    public String getCommentScoreQ() {
        return commentScoreQ;
    }

    public void setCommentScoreQ(String commentScoreQ) {
        this.commentScoreQ = commentScoreQ;
    }

    public String getProcurementRateQ() {
        return procurementRateQ;
    }

    public void setProcurementRateQ(String procurementRateQ) {
        this.procurementRateQ = procurementRateQ;
    }

    public String getProcurementCheckRate() {
        return procurementCheckRate;
    }

    public void setProcurementCheckRate(String procurementCheckRate) {
        this.procurementCheckRate = procurementCheckRate;
    }

    public String getProcurementScoreQ() {
        return procurementScoreQ;
    }

    public void setProcurementScoreQ(String procurementScoreQ) {
        this.procurementScoreQ = procurementScoreQ;
    }

    public String getBdxMemScore() {
        return bdxMemScore;
    }

    public void setBdxMemScore(String bdxMemScore) {
        this.bdxMemScore = bdxMemScore;
    }

    public Integer getConsumeMemNQ() {
        return consumeMemNQ;
    }

    public void setConsumeMemNQ(Integer consumeMemNQ) {
        this.consumeMemNQ = consumeMemNQ;
    }

    public Integer getConsumeMemIncNQ() {
        return consumeMemIncNQ;
    }

    public void setConsumeMemIncNQ(Integer consumeMemIncNQ) {
        this.consumeMemIncNQ = consumeMemIncNQ;
    }

    public String getConsumeMemScoreQ() {
        return consumeMemScoreQ;
    }

    public void setConsumeMemScoreQ(String consumeMemScoreQ) {
        this.consumeMemScoreQ = consumeMemScoreQ;
    }

    public String getWorkStandards() {
        return workStandards;
    }

    public void setWorkStandards(String workStandards) {
        this.workStandards = workStandards;
    }

    public String getWorkStandardsScore() {
        return workStandardsScore;
    }

    public void setWorkStandardsScore(String workStandardsScore) {
        this.workStandardsScore = workStandardsScore;
    }

    public String getTalentScore() {
        return talentScore;
    }

    public void setTalentScore(String talentScore) {
        this.talentScore = talentScore;
    }

    public String getLearnScore() {
        return learnScore;
    }

    public void setLearnScore(String learnScore) {
        this.learnScore = learnScore;
    }

    public String getLearnUpScore() {
        return learnUpScore;
    }

    public void setLearnUpScore(String learnUpScore) {
        this.learnUpScore = learnUpScore;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}

