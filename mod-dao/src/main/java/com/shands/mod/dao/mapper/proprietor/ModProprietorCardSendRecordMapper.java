package com.shands.mod.dao.mapper.proprietor;

import com.shands.mod.dao.model.proprietor.ModProprietorCardSendRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业主卡发放记录表(mod_proprietor_card_send_record)数据库访问层
 */
@Mapper
public interface ModProprietorCardSendRecordMapper {

    /**
     * 查询所有发放成功的记录
     *
     * @return 对象列表
     */
    List<ModProprietorCardSendRecord> selectAllSuccess();

    /**
     * 新增数据
     *
     * @param record 实例对象
     * @return 影响行数
     */
    int insert(ModProprietorCardSendRecord record);

    /**
     * 修改数据
     *
     * @param mobile 手机号
     * @return 影响行数
     */
    int deleteByMobile(@Param("mobile") String mobile);


}
