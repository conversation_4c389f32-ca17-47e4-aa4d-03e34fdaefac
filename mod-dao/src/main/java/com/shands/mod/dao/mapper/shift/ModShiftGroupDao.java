package com.shands.mod.dao.mapper.shift;

import com.shands.mod.dao.model.shift.ModShiftGroup;
import com.shands.mod.dao.model.shift.ModShiftInfo;
import com.shands.mod.dao.model.shift.QurMbServicesVo;
import org.apache.ibatis.annotations.Param;

import java.awt.print.Pageable;
import java.util.Date;
import java.util.List;

/**
 * 酒店班组(ModShiftGroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-17 10:14
 */
public interface ModShiftGroupDao {

  /**
   * 新增数据
   *
   * @param modShiftGroup 实例对象
   * @return 影响行数
   */
  int insert(ModShiftGroup modShiftGroup);



  /**
   * 查询当前时间属于哪个班次
   */
  ModShiftGroup queryExistByName(Integer hotelId,String name);


  String getLastCode();


  List<ModShiftGroup> queryByHotelId(Integer hotelId, Integer status);

  /**
   * 根据类型查找班组
   */
  ModShiftGroup queryByHotelIdAndShiftType(Integer hotelId,String shiftType);

  /**
   * 修改数据
   * @param modShiftGroup 实例对象
   * @return 影响行数
   */
  int update(ModShiftGroup modShiftGroup);


  /**
   * 通过主键删除数据
   *
   * @param id 主键
   * @return 影响行数
   */
  int deleteById(Integer id);

}

