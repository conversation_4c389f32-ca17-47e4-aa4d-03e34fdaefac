package com.shands.mod.dao.mapper.invoice;

import com.shands.mod.dao.model.invoice.ModInvoiceApplyCatering;
import com.shands.mod.dao.model.res.invoice.ApplyOpenInvoiceRes;
import com.shands.mod.dao.model.res.invoice.InvoiceApplyRecordNewRes;
import com.shands.mod.dao.model.res.invoice.InvoiceByApplyNoRes;
import com.shands.mod.dao.model.res.invoice.OrderInvoiceStatusRes;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ModInvoiceApplyCateringMapper {

  int deleteByPrimaryKey(Integer id);

  int insert(ModInvoiceApplyCatering record);

  int insertSelective(ModInvoiceApplyCatering record);

  ModInvoiceApplyCatering selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(ModInvoiceApplyCatering record);

  int updateByPrimaryKey(ModInvoiceApplyCatering record);

  /**
   * 查询订单有没有开票
   * @param orderId 订单id
   * @param catering 来源
   * @return
   */
  ApplyOpenInvoiceRes alreadyOpenInvoice(Integer orderId,String catering);

  int updateBySerialNoSelective(ModInvoiceApplyCatering record);

  ModInvoiceApplyCatering getBySerialNo(@Param("serialNo")String serialNo);

  int getByRefId(@Param("refId")Integer refId);

  OrderInvoiceStatusRes invoiceStatus(@Param("refId")Integer refId);

  InvoiceByApplyNoRes getInvoiceByApply(@Param("applyNumber")String applyNumber);

  /**
   * 2.15版本，查询申请发票记录
   */
  List<InvoiceApplyRecordNewRes> invoiceApplyList(@Param("customerId") Integer customerId);
}