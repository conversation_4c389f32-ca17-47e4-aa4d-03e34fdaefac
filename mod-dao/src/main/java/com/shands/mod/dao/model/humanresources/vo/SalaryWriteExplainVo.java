package com.shands.mod.dao.model.humanresources.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.OnceAbsoluteMerge;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/10/30
 **/
@Data
@ContentRowHeight(120)
@ColumnWidth(15)
@OnceAbsoluteMerge(firstRowIndex = 0, lastRowIndex = 0, firstColumnIndex = 1, lastColumnIndex = 7)
public class SalaryWriteExplainVo {

  @ExcelProperty("占位符")
  private String placeholder;

  @ExcelProperty("填写说明")
  private String writeExplain;
}
