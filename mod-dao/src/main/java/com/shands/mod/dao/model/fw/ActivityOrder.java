package com.shands.mod.dao.model.fw;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/** <AUTHOR> */
@ApiModel(value = "com-shands-mod-dao-model-fw-ActivityOrder")
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ActivityOrder {

  /** ID */
  @ApiModelProperty(value = "ID")
  private Integer id;

  /** 订单编号 */
  @ApiModelProperty(value = "订单编号")
  private String orderNo;

  /** 活动ID */
  @ApiModelProperty(value = "活动ID")
  private Integer activityId;

  /** 活动场次id */
  @ApiModelProperty(value = "活动场次id")
  private Integer activityShiftId;

  /** 联系人姓名 */
  @ApiModelProperty(value = "联系人姓名")
  private String contactName;

  /** 手机号码 */
  @ApiModelProperty(value = "手机号码")
  private String phone;

  /** 人数 */
  @ApiModelProperty(value = "人数")
  private Integer number;

  /** 单价 */
  @ApiModelProperty(value = "单价")
  private BigDecimal price;

  /** 总价 */
  @ApiModelProperty(value = "总价")
  private BigDecimal totalPrice;

  /** 企业ID */
  @ApiModelProperty(value = "企业ID")
  private Integer company;

  /** 核销状态 0.未核销 1.已核销 2.已生成订单 3.已支付 */
  @ApiModelProperty(value = "核销状态 0.未核销 1.已核销 2.已生成订单 3.已支付")
  private Byte status;

  @ApiModelProperty("用户退款原因")
  private String refundMessage;

  @ApiModelProperty("退款失败原因")
  private String refundFailMessage;

  /** 删除标志 */
  @ApiModelProperty(value = "删除标志")
  private Byte delete;

  /** 创建用户 */
  @ApiModelProperty(value = "创建用户")
  private Integer createUser;

  /** 创建时间 */
  @ApiModelProperty(value = "创建时间")
  private LocalDateTime createTime;

  /** 更新用户 */
  @ApiModelProperty(value = "更新用户")
  private Integer updateUser;

  /** 更新时间 */
  @ApiModelProperty(value = "更新时间")
  private LocalDateTime updateTime;

  private String couponCode;

  private String payType;

  private Integer payStatus;

  private Date payTime;

  private Integer cancelUser;

  private Date cancelTime;

  private Date refundApplyTime;

  private Date refundAccountTime;

  /***交易号*/
  private String transactionNo;
}
