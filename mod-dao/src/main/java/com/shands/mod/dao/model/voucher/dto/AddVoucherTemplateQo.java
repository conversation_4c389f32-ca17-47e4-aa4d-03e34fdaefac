package com.shands.mod.dao.model.voucher.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * @Description:
 * @Author: guixuehai
 * @Date: 2024/8/12 16:14
 */
@Data
public class AddVoucherTemplateQo {

  @ApiModelProperty("法宝模版名称")
  private String templateName;

  @ApiModelProperty("商品售价")
  private Integer salePrice;

  @ApiModelProperty("原价")
  private Integer originalPrice;

  @ApiModelProperty("商品结算价")
  private Integer settlementPrice;


  // 固定AGAINSTGOLDEN
  @ApiModelProperty("券类型 PRODUCT: 兑换券；AGAINSTGOLDEN：满减券 DISCOUNT：折扣类 ")
  private String ticketTypeEnum;

  @ApiModelProperty("每次生成的凭证数")
  private Integer createVoucherNum;
  @ApiModelProperty("抵金券类型：0抵金券 1折扣券")
  private Integer againstgoldenType;

  @ApiModelProperty("有效延期开始天数")
  private Integer delayDate;

  @ApiModelProperty("有效延期截至天数")
  private Integer delayToDate;

  /**
   * 商品描述
   */
  @ApiModelProperty("商品描述")
  private String description;

  /**
   * 使用规则
   */
  @ApiModelProperty("使用规则")
  private String useRule;


  @ApiModelProperty("优惠券门槛")
  private Integer useMinimumPrice;

  @ApiModelProperty("优惠金额")
  private Integer worthPrice;


  @ApiModelProperty("适用渠道 APP、小程序")
  private String channel;

  @ApiModelProperty("晚数 (不限制0，限制大于0)")
  private Integer nights;

  @ApiModelProperty("房间数 (不限制0，限制大于0)")
  private Integer roomNum;


  /**
   * 会员价 cweb012 bdx012
   */
  @ApiModelProperty("房价码")
  private String rateCode;

  /**
   * 一年
   */
  @ApiModelProperty("入住延期开始天数")
  private Integer delayComeDate;

  @ApiModelProperty("入住延期截至天数")
  private Integer delayOldToDate;


  @ApiModelProperty("有效期使用规则：周几可以使用，1-星期一，7-星期日，逗号分隔")
  private String validatyRule;

  @ApiModelProperty("入住有效期使用规则：周几可以使用，1-星期一，7-星期日，逗号分隔")
  private String reserveValidatyRule;


  @ApiModelProperty("-1-无限库存，可选值无限库存、有限库存。范围0-999999999。发券成功后，扣减库存。")
  private Integer totalStock;


  @ApiModelProperty("成本承担：0-平台承担 1-门店承担 2-其他")
  private Integer costUndertake;

  @ApiModelProperty("适用酒店")
  private List<String> whileListHotelCodes;

  // 固定ROOM
  @ApiModelProperty("适用场景 ROOM：客房券；RESTAURANT：餐饮类 RECREATION：康乐；LOUNGE：休息厅；SCRIPTKILL：剧本杀；MALL：商城")
  private Integer scene;


  @ApiModelProperty(value = "ICON")
  private String imageUrl;


  // 固定1普通
  @ApiModelProperty(value = "法宝使用类型 1:普通 2:次卡")
  private Integer resType;

  @ApiModelProperty(value = "所属品牌id")
  private String brandId;

  @ApiModelProperty(value = "所属品牌名称")
  private String brandName;

  // 固定0线上预订使用
  @ApiModelProperty(value = "券使用方式，0线上预订使用 1 线下扫码核销 0")
  private Integer useType;

  // 不限
  @ApiModelProperty(value = "是否限制本人使用 0-不限 1-限制")
  private Integer onlyOwnUse;

  //
  @ApiModelProperty(value = "是否为动态结算 0-否 1-是")
  private Integer ifDynamicSettlement;

  /**
   * 是否单店法宝 是
   */
  private Integer ifSingleHotel;


  @ApiModelProperty(value = "结算标识,默认为 0 ")
  private String costUndertakeIdentify = "0";


  @ApiModelProperty(value = "是否发放可用时光值 0-否 1-是")
  private Integer sendAvailableScore;


  // 固定 适用身份权益
  @ApiModelProperty("适用权益范围")
  private Integer applyInterestScope;

  /**
   * 固定传1
   */
  @ApiModelProperty("有效期类型 0-固定 1-动态天数 2-动态小时")
  private Integer validateType;

  private Integer ifInterest;

  private Integer privilegeFlag;


  private Integer type;
  @ApiModelProperty("所属活动id")
  private Integer etsActivityId;
  @ApiModelProperty("法宝分类id")
  private Long magicTypeId;
  @ApiModelProperty("备注")
  private String comment;


}
