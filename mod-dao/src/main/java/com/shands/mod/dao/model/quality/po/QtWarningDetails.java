package com.shands.mod.dao.model.quality.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.util.Date;
import java.io.Serializable;
/**
 * (QtWarningDetails)实体类
 *
 * <AUTHOR>
 * @since 2022-03-31 15:06:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QtWarningDetails implements Serializable {
    private static final long serialVersionUID = -34472991759484539L;
    /**
     * 主键id
     */
    private Integer id;
    /**
     * 预警时间
     */
    private Date warningTime;
    /**
     * 酒店id
     */
    private Integer hotelId;
    /**
     * 任务表id
     */
    private Integer taskId;
    /**
     * 检查表id
     */
    private Integer checkId;
    /**
     * 检查表类型
     */
    private String checkType;
    /**
     * 预警原因
     */
    private String warningReason;
    
    private String remark;
    
    private Date createTime;
    
    private Date updateTime;
    
    private Boolean ifDelete;
    
    private Integer createUser;
    
    private Integer updateUser;

}
