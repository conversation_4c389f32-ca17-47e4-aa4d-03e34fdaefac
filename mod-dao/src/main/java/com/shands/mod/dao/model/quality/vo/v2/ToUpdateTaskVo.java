package com.shands.mod.dao.model.quality.vo.v2;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("跳转修改(分配)整改任务详情响应参数")
public class ToUpdateTaskVo {


  @ApiModelProperty("标题")
  private String title;
  @ApiModelProperty("酒店名称")
  private  String hotelName;
  @ApiModelProperty("描述")
  private String describe;
  @ApiModelProperty("附件地址")
  private String url;
  @ApiModelProperty("资源类型")
  private String resourceType;

  @ApiModelProperty("部门")
  private String departmentName;

  @ApiModelProperty("维度")
  private String dimensionName;

  @ApiModelProperty("优先级")
  private String priority;

  @ApiModelProperty("任务开始时间")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date taskStartTime;

  @ApiModelProperty("任务开始时间")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date taskEndTime;

  @ApiModelProperty("整改人")
  private List<RectificationUserVo>  rectificationPeople;

  @ApiModelProperty("复查人")
  private List<RectificationUserVo> reviewPeople;


}
