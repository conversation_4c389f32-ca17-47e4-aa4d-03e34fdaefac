package com.shands.mod.dao.model.training.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;

/**
 * 课程学员表(ModCourseUser)实体类
 *
 * <AUTHOR>
 * @since 2022-08-17 15:09:48
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModCourseUser implements Serializable {
    private static final long serialVersionUID = 755100850231598704L;
    
    private Integer id;
    /**
     * 学员姓名
     */
    private String name;

    /**
     * 学员手机号
     */
    private String mobile;
    /**
     * 事业部code
     */
    private String divisionCode;
    /**
     * 事业部名称
     */
    private String divisionName;
    /**
     * 酒店id
     */
    private Integer hotelId;
    /**
     * 酒店名
     */
    private String hotelName;
    /**
     * 最近学习时间
     */
    private Date lastLearnTime;
    /**
     * 课程类型（单课、合集）
     */
    private String type;
    /**
     * 单课或合集id
     */
    private Integer courseId;
    /**
     * 用户id
     */
    private Integer userId;
}

