package com.shands.mod.dao.mapper.hs;

import com.shands.mod.dao.model.hs.HotelService;
import com.shands.mod.dao.model.res.ModServiceRes;
import com.shands.mod.dao.model.res.hs.hotel.AllocationStrategyRes;
import com.shands.mod.dao.model.res.hs.hotel.HotelServiceResForWeb;
import com.shands.mod.dao.model.res.hs.staff.ServerRes;
import com.shands.mod.dao.model.res.hs.workorder.ServiceBaseInfo;
import com.shands.mod.dao.model.v0701.vo.HotelServiceInfoVo;
import com.shands.mod.dao.model.v0701.vo.WorkCusInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HotelServiceMapper {

  int deleteByPrimaryKey(Integer id);

  int insertSelective(HotelService record);

  int insertBatch(@Param("entityList") List<HotelService> hotelServiceList);

  HotelService selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(HotelService record);

  List<HotelService> findByGroupAndCom(@Param("platformFlag") Integer platformFlag, @Param("companyId") Integer companyId);

  List<HotelService> findAll(
      @Param("companyId") Integer companyId);

  HotelService detail( @Param("id") Integer id,@Param("companyId") Integer companyId);

  List<HotelServiceResForWeb> getServiceList(@Param("companyId") Integer companyId);

  HotelService findService(
      @Param("companyId") Integer companyId, @Param("serviceType") String serviceType);


  HotelService findServiceByName(
      @Param("companyId") Integer companyId, @Param("serviceName") String serviceName);

  List<HotelServiceResForWeb> serviceForType(@Param("companyId") Integer companyId);

  Integer getId(@Param("serviceType") String serviceType, @Param("companyId") Integer companyId);

  Integer deleteByCompanyId(@Param("companyId") Integer companyId,@Param("modServiceId") Integer modServiceId);

  int deleteByServiceTypeList(@Param("companyId") Integer companyId,@Param("serviceTypeList") List<String> serviceTypeList);

  List<HotelService> allForCompany(@Param("companyId") Integer companyId,@Param("serviceType") String serviceType);

  List<HotelService> findAllService(@Param("serviceType") String serviceType);

  //所有集团的服务
  List<HotelService> findGroupService(@Param("serviceType") String serviceType);

  List<HotelService> allServiceByGroupId(@Param("companyId") Integer companyId);

  Integer updateCnName(@Param("modServiceId") Integer modServiceId,@Param("cnname") String cnname,@Param("updateUser") Integer updateUser);

  /**
   * 根据酒店编码查询酒店服务列表
   * @param companyId
   * @return
   */
  List<HotelServiceInfoVo> qurHotelServices(Integer companyId);

  /**
   * 根据服务编号查询受理部门 受理人信息
   * @param hotelServiceId
   * @return
   */
  WorkCusInfoVo qurWorkCusInfo(Integer hotelServiceId);

  /**
   * 根据服务编号查询受理部门 受理人信息
   * @param deptId 部门编号
   * @return
   */
  WorkCusInfoVo getWorkCusInfoByDeptId(Integer deptId);

  /**
   * 根据服务编码查询服务派单方式 1：自动派单 2：人工派单
   * @param hotelServiceId
   * @return
   */
  Integer qurArrangeType(Integer hotelServiceId);

  List<String> getNameByService(@Param("companyId") Integer companyId,@Param("serviceType") List<String> listString);

  List<ServerRes> getByCompany(@Param("companyId") Integer companyId);

  /**
   * 查询抄送人
   * @param companyId
   * @param serviceId
   * @return
   */
  String getDuplicatePeople(@Param("companyId") Integer companyId,@Param("serviceId") Integer serviceId);

  List<AllocationStrategyRes> getAllocationStrategy(@Param("companyId") Integer companyId);

  void editAllocationStrategy(@Param("allocationStrategy") Integer allocationStrategy, @Param("serviceType") String serviceType, @Param("companyId") Integer companyId);

  /**
   * 查询开通保洁/送物/维修服务的酒店id列表
   */
  List<Integer> getHotelIdListOfServiceWorkOrder();

  List<Integer> getAllHotelIdListOfServiceWorkOrder();

  /**
   * @param modServiceId 集团服务id
   * @return 已开通服务的酒店数量
   */
  int numberOfHotelsWithService(@Param("modServiceId") Integer modServiceId);

  int disableHotelService(@Param("modServiceId") Integer modServiceId, @Param("deleted") Integer deleted);

  List<ModServiceRes> getServiceExtendTypeByIdList(@Param("hotelServiceIdList") List<Integer> hotelServiceIdList);

  ServiceBaseInfo getServiceBaseInfoById(@Param("hotelServiceId") Integer hotelServiceId);



}
