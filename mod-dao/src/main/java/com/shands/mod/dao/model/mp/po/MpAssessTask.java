package com.shands.mod.dao.model.mp.po;

import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 工单配置字段详情表(MpAssessTask)实体类
 *
 * <AUTHOR>
 * @since 2022-11-02 18:14:05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class MpAssessTask implements Serializable {
    private static final long serialVersionUID = 684711683404201648L;
    
    private Integer id;
    /**
     * 版本号
版本号
     */
    private Integer version;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private Integer createUser;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 修改人
     */
    private Integer updateUser;
    /**
     * 名称
     */
    private String name;
    /**
     * 编号
     */
    private String taskCode;
    /**
     * 考核时间类型  month/quarter
     */
    private String timeType;
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 状态 noStart  running complete
     */
    private String assessStatus;
    /**
     * 指标
     */
    private String targetCode;
    /**
     * 占比
     */
    private Integer proportion;
    /**
     * 酒店品牌
     */
    private String brand;
    /**
     * 酒店状态
     */
    private String hotelStatus;
    /**
     * 管理方式
     */
    private String manage;

    private Integer status;

  private String targetRemark;

  private String targetRule;

  private Integer month;

  private String remark;

  private String batchCode;
}

