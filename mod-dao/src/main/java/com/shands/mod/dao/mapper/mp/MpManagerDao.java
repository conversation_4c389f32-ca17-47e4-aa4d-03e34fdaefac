package com.shands.mod.dao.mapper.mp;

import com.shands.mod.dao.model.mp.bo.ManageListBo;
import com.shands.mod.dao.model.mp.po.MpManager;
import com.shands.mod.dao.model.mp.vo.ManageHotelVo;
import com.shands.mod.dao.model.mp.vo.ManageListVo;
import com.shands.mod.dao.model.mp.vo.ManageVo;
import com.shands.mod.dao.model.newDataBoard.vo.HotelByGmVo;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 工单配置字段详情表(MpManager)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-02 19:01:50
 */
public interface MpManagerDao {

  /**
   * 通过ID查询单条数据
   *
   * @param id 主键
   * @return 实例对象
   */
  MpManager queryById(Integer id);

  /**
   * 查询指定行数据
   *
   * @return 对象列表
   */
  List<ManageVo> queryAll();

  /**
   * 统计总行数
   *
   * @param MpManager 查询条件
   * @return 总行数
   */
  long count(MpManager MpManager);

  /**
   * 新增数据
   *
   * @param MpManager 实例对象
   * @return 影响行数
   */
  int insert(MpManager MpManager);

  /**
   * 批量新增数据（MyBatis原生foreach方法）
   *
   * @param entities List<MpManager> 实例对象列表
   * @return 影响行数
   */
  int insertBatch(@Param("entities") List<MpManager> entities);

  /**
   * 批量新增或按主键更新数据（MyBatis原生foreach方法）
   *
   * @param entities List<MpManager> 实例对象列表
   * @return 影响行数
   * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
   */
  int insertOrUpdateBatch(@Param("entities") List<MpManager> entities);

  /**
   * 修改数据
   *
   * @param MpManager 实例对象
   * @return 影响行数
   */
  int update(MpManager MpManager);

  /**
   * 通过主键删除数据
   *
   * @param id 主键
   * @return 影响行数
   */
  int deleteById(Integer id);

  List<Integer> selectByManager(@Param("userId") Integer userId);

  List<ManageHotelVo> selectByHotel(@Param("userId") Integer userId);

  List<HotelByGmVo> selectHotelByGm(@Param("userId")Integer userId,@Param("divisionCode")String divisionCode);

  Date selectAllByUpdateTime(@Param("userId") Integer userId,@Param("hotelCode") String hotelCode);

  List<ManageListVo> selectManageList(@Param("manageListBo") ManageListBo manageListBo);

  /**
   * 根据companyId查询关联的总经理ucId
   *
   * @param companyId 酒店companyId
   * @return 关联的总经理列表
   */
  List<Integer> selectManagerUcIdListByCompanyId(@Param("companyId") Integer companyId);
}
