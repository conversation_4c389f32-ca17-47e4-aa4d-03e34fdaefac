package com.shands.mod.dao.model.req.hs.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @createDate 2020/10/12 10:01 上午 <br>
 * @see com.shands.mod.dao.model.req.hs.hotel <br>
 */
@ApiModel("查找入住单请求参数")
public class FindOrderWrapper {

  @ApiModelProperty("公司id")
  private Integer companyId;

  @ApiModelProperty("登陆手机号")
  private String loginMobile;

  @ApiModelProperty("二维码code")
  private String code;

  public Integer getCompanyId() {
    return companyId;
  }

  public void setCompanyId(Integer companyId) {
    this.companyId = companyId;
  }

  public String getLoginMobile() {
    return loginMobile;
  }

  public void setLoginMobile(String loginMobile) {
    this.loginMobile = loginMobile;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }
}
