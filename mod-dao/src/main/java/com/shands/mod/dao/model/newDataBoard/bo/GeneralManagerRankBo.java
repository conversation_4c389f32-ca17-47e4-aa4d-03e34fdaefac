package com.shands.mod.dao.model.newDataBoard.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("总经理排行榜请求参数")
public class GeneralManagerRankBo {
  @ApiModelProperty("事业部code")
  private String divisionCode;
  @ApiModelProperty("区分层级 10%:ten ;80% : eighty;最后10% : lastTen;全部 all")
  private String type;
  @ApiModelProperty("业务日期")
  private String bizDate;
  @ApiModelProperty("搜索字段")
  private String searchStr;
}
