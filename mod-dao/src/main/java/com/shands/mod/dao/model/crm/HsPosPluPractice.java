package com.shands.mod.dao.model.crm;

import java.math.BigDecimal;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * 菜项做法(HsPosPluPractice)实体类
 *
 * <AUTHOR>
 * @since 2021-05-18 11:30:12
 */
@Data
public class HsPosPluPractice implements Serializable {

  private static final long serialVersionUID = -40792429009480367L;
  /**
   * id 绿云pos系统
   */
  private Integer id;
  /**
   * 做法代码
   */
  private String code;
  /**
   * 做法描述
   */
  private String descript;
  /**
   * 英文名称
   */
  private String descriptEn;
  /**
   * 加价
   */
  private BigDecimal addPrice;
  /**
   * 菜项code
   */
  private String pluCode;
  /**
   * 所属公司id
   */
  private Integer companyId;
  /**
   * 集团id
   */
  private Integer groupId;
  /**
   * 删除标志
   */
  private Integer deleted;
  /**
   * 创建时间
   */
  private Date createTime;
  /**
   * 创建用户
   */
  private Integer createUser;
  /**
   * 更新用户
   */
  private Integer updateUser;
  /**
   * 更新时间
   */
  private Date updateTime;

}