package com.shands.mod.dao.model.hs;

import lombok.ToString;
import java.util.Date;

@ToString
public class Remind {
  private Integer id;

  private String serviceType;

  /** 单位秒 */
  private Integer amount;

  private Integer orderStatus;

  private Integer acceptDept;

  private Integer acceptUser;

  private Integer status;

  private Integer companyId;

  private Integer groupId;

  private Integer version;

  private Integer deleted;

  private Integer createUser;

  private Date createTime;

  private Integer updateUser;

  private Date updateTime;

  private Integer sendSms;

  private Integer call;

  private Integer processType;

  /**
   * 是否通知当前受理人
   */
  private Boolean notifyAcceptUser;

  /**
   * 是否通知工单创建人
   */
  private Boolean notifyCreateUser;

  /**
   * 通知小组id
   */
  private Long reminderGroupId;

  private String source;

  public Remind() {
  }

  public Integer getCall() {
    return call;
  }

  public void setCall(Integer call) {
    this.call = call;
  }

  public Integer getSendSms() {
    return sendSms;
  }

  public void setSendSms(Integer sendSms) {
    this.sendSms = sendSms;
  }

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public String getServiceType() {
    return serviceType;
  }

  public void setServiceType(String serviceType) {
    this.serviceType = serviceType == null ? null : serviceType.trim();
  }

  public Integer getAmount() {
    return amount;
  }

  public void setAmount(Integer amount) {
    this.amount = amount;
  }

  public Integer getOrderStatus() {
    return orderStatus;
  }

  public void setOrderStatus(Integer orderStatus) {
    this.orderStatus = orderStatus;
  }

  public Integer getAcceptDept() {
    return acceptDept;
  }

  public void setAcceptDept(Integer acceptDept) {
    this.acceptDept = acceptDept;
  }

  public Integer getAcceptUser() {
    return acceptUser;
  }

  public void setAcceptUser(Integer acceptUser) {
    this.acceptUser = acceptUser;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public Integer getCompanyId() {
    return companyId;
  }

  public void setCompanyId(Integer companyId) {
    this.companyId = companyId;
  }

  public Integer getGroupId() {
    return groupId;
  }

  public void setGroupId(Integer groupId) {
    this.groupId = groupId;
  }

  public Integer getVersion() {
    return version;
  }

  public void setVersion(Integer version) {
    this.version = version;
  }

  public Integer getDeleted() {
    return deleted;
  }

  public void setDeleted(Integer deleted) {
    this.deleted = deleted;
  }

  public Integer getCreateUser() {
    return createUser;
  }

  public void setCreateUser(Integer createUser) {
    this.createUser = createUser;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Integer getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(Integer updateUser) {
    this.updateUser = updateUser;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public Integer getProcessType() {
    return processType;
  }

  public void setProcessType(Integer processType) {
    this.processType = processType;
  }

  public Boolean getNotifyAcceptUser() {
    return notifyAcceptUser;
  }

  public void setNotifyAcceptUser(Boolean notifyAcceptUser) {
    this.notifyAcceptUser = notifyAcceptUser;
  }

  public Boolean getNotifyCreateUser() {
    return notifyCreateUser;
  }

  public void setNotifyCreateUser(Boolean notifyCreateUser) {
    this.notifyCreateUser = notifyCreateUser;
  }

  public Long getReminderGroupId() {
    return reminderGroupId;
  }

  public void setReminderGroupId(Long reminderGroupId) {
    this.reminderGroupId = reminderGroupId;
  }

  public String getSource() {
    return source;
  }

  public void setSource(String source) {
    this.source = source;
  }

}
