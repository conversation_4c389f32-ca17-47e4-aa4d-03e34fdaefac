package com.shands.mod.dao.mapper.hs;


import com.shands.mod.dao.model.hs.HsHotelServiceClassify;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【hs_hotel_service_classify(酒店服务分类)】的数据库操作Mapper
 * @createDate 2023-08-29 11:06:04
 * @Entity com.shands.mod.dao.model.hs.HsHotelServiceClassify
 */
public interface HsHotelServiceClassifyMapper {

  int insertSelective(HsHotelServiceClassify classify);

  int insertBatch(@Param("list") List<HsHotelServiceClassify> list);


  int updateByPrimaryKeySelective(HsHotelServiceClassify classify);

  int updateStatus(@Param("id") Long id, @Param("status") Integer status);

  int deleteById(Long id);

  HsHotelServiceClassify selectById(Long id);

  List<HsHotelServiceClassify> selectByCompanyId(@Param("companyId") Integer companyId);


  HsHotelServiceClassify selectByCompanyIdAndCode(@Param("companyId") Integer companyId,
      @Param("code") String code);

  HsHotelServiceClassify selectByCompanyIdAndName(@Param("companyId") Integer companyId,
      @Param("name") String name);


  List<HsHotelServiceClassify> select(@Param("companyId") Integer companyId,
      @Param("name") String name, @Param("status") Integer status);


}




