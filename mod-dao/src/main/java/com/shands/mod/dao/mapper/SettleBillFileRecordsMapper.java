package com.shands.mod.dao.mapper;

import com.shands.mod.dao.model.SettleBillFileRecords;
import com.shands.mod.dao.model.SettleBillFileRecordsExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface SettleBillFileRecordsMapper {
    long countByExample(SettleBillFileRecordsExample example);

    int deleteByExample(SettleBillFileRecordsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SettleBillFileRecords record);

    int insertSelective(SettleBillFileRecords record);

    List<SettleBillFileRecords> selectByExampleWithRowbounds(SettleBillFileRecordsExample example, RowBounds rowBounds);

    List<SettleBillFileRecords> selectByExample(SettleBillFileRecordsExample example);

    SettleBillFileRecords selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SettleBillFileRecords record, @Param("example") SettleBillFileRecordsExample example);

    int updateByExample(@Param("record") SettleBillFileRecords record, @Param("example") SettleBillFileRecordsExample example);

    int updateByPrimaryKeySelective(SettleBillFileRecords record);

    int updateByPrimaryKey(SettleBillFileRecords record);

    void batchInsert(@Param("items") List<SettleBillFileRecords> items);
}