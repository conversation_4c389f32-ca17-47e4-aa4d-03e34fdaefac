package com.shands.mod.dao.model.res;

import lombok.Data;

/**
 * @Description:
 * @Author: wj
 * @Date: 2024/12/20 17:01
 */
@Data
public class MyResultsItem {

  /**
   * 会员手机号（部分隐藏）
   */
  private String mobile;

  /**
   * 创建时间，格式为 YYYY-MM-DD HH:MM:SS
   */
  private String createTime;

  /**
   * 标签枚举，根据活动类型不同，标签的含义也不同：
   * - 注册会员：APP/小程序
   * - 预订结果：已绑定
   * - 售卡：漫步卡/畅游卡
   */
  private String label;

  /**
   * 更新时间，格式为 YYYY-MM-DD HH:MM:SS
   */
  private String updateTime;
}
