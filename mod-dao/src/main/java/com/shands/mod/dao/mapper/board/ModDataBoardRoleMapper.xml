<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shands.mod.dao.mapper.board.ModDataBoardRoleMapper" >
  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.board.ModDataBoardRole" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="data_id" property="dataId" jdbcType="INTEGER" />
    <result column="role_id" property="roleId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, data_id, role_id
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from mod_data_board_role
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from mod_data_board_role
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.shands.mod.dao.model.board.ModDataBoardRole" >
    insert into mod_data_board_role (id, data_id, role_id
      )
    values (#{id,jdbcType=INTEGER}, #{dataId,jdbcType=INTEGER}, #{roleId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.shands.mod.dao.model.board.ModDataBoardRole" >
    insert into mod_data_board_role
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="dataId != null" >
        data_id,
      </if>
      <if test="roleId != null" >
        role_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="dataId != null" >
        #{dataId,jdbcType=INTEGER},
      </if>
      <if test="roleId != null" >
        #{roleId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.board.ModDataBoardRole" >
    update mod_data_board_role
    <set >
      <if test="dataId != null" >
        data_id = #{dataId,jdbcType=INTEGER},
      </if>
      <if test="roleId != null" >
        role_id = #{roleId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.board.ModDataBoardRole" >
    update mod_data_board_role
    set data_id = #{dataId,jdbcType=INTEGER},
      role_id = #{roleId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!--根据角色id删除-->
  <delete id="deleteByRoleId">
    delete from mod_data_board_role where role_id = #{roleId}
  </delete>

  <!--批量添加-->
  <insert id="insertBatch">
    insert into mod_data_board_role (data_id,role_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.dataId},#{item.roleId})
    </foreach>
  </insert>
</mapper>