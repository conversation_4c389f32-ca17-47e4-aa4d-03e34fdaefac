package com.shands.mod.dao.model.workorder.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class NwNodeVo {

  @ApiModelProperty(value="业务节点Id")
  private Integer nodeId;

  @ApiModelProperty(value="业务名称")
  private String bussinessName;

  @ApiModelProperty(value="接受对象")
  private String acceptType;

  @ApiModelProperty(value="接受人")
  private String acceptUser;

  @ApiModelProperty(value = "代替指定人")
  private Integer replaceAcceptUser;

  @ApiModelProperty(value = "指定人id")
  private Integer acceptUserId;
}
