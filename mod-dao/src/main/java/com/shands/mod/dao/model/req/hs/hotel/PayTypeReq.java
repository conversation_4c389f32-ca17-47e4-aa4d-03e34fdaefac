package com.shands.mod.dao.model.req.hs.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("获得支付方式，请求参数")
public class PayTypeReq {

  @NotBlank(message ="手机号码不能为空")
  @ApiModelProperty("手机号码")
  private String mobile;

  @NotNull(message = "酒店id不能为空")
  @ApiModelProperty("酒店id")
  private Integer companyId;

  @NotBlank(message ="服务类型不能为空")
  @ApiModelProperty("服务类型")
  private String serviceType;

  @ApiModelProperty("查询线上支付，线下支付必传参数")
  private String payType;

  @ApiModelProperty("订单号")
  private Integer orderId;
}
