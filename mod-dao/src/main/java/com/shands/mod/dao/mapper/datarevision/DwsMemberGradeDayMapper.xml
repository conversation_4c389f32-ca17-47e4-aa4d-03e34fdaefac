<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.datarevision.DwsMemberGradeDayMapper">

    <resultMap type="com.shands.mod.dao.model.datarevision.po.DwsMemberGradeDay" id="DwsMemberGradeDayMap">
      <result property="id" column="id" jdbcType="INTEGER"/>
      <result property="bizDate" column="biz_date" jdbcType="TIMESTAMP"/>
      <result property="bdwEarthAmt" column="bdw_earth_amt" jdbcType="INTEGER"/>
      <result property="bdwMoonAmt" column="bdw_moon_amt" jdbcType="INTEGER"/>
      <result property="bdwMarsAmt" column="bdw_mars_amt" jdbcType="INTEGER"/>
      <result property="bdwNeptuneAmt" column="bdw_neptune_amt" jdbcType="INTEGER"/>
      <result property="bdwPatersonAmt" column="bdw_paterson_amt" jdbcType="INTEGER"/>
      <result property="sqBeAmt" column="sq_be_amt" jdbcType="INTEGER"/>
      <result property="sqBsAmt" column="sq_bs_amt" jdbcType="INTEGER"/>
      <result property="sqBgAmt" column="sq_bg_amt" jdbcType="INTEGER"/>
      <result property="sqBpAmt" column="sq_bp_amt" jdbcType="INTEGER"/>
      <result property="bdwAmt" column="bdw_amt" jdbcType="INTEGER"/>
      <result property="sqAmt" column="sq_amt" jdbcType="INTEGER"/>
    </resultMap>

  <!--查询单个-->
  <select id="queryById" resultMap="DwsMemberGradeDayMap">
    select
      id, biz_date, bdw_earth_amt, bdw_moon_amt, bdw_mars_amt, bdw_neptune_amt, bdw_paterson_amt, sq_be_amt, sq_bs_amt, sq_bg_amt, sq_bp_amt, bdw_amt, sq_amt
    from dws_member_grade_day
    where id = #{id}
  </select>

  <!--统计总行数-->
  <select id="count" resultType="java.lang.Long">
    select count(1)
    from dws_member_grade_day
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="bizDate != null">
        and biz_date = #{bizDate}
      </if>
      <if test="bdwEarthAmt != null">
        and bdw_earth_amt = #{bdwEarthAmt}
      </if>
      <if test="bdwMoonAmt != null">
        and bdw_moon_amt = #{bdwMoonAmt}
      </if>
      <if test="bdwMarsAmt != null">
        and bdw_mars_amt = #{bdwMarsAmt}
      </if>
      <if test="bdwNeptuneAmt != null">
        and bdw_neptune_amt = #{bdwNeptuneAmt}
      </if>
      <if test="bdwPatersonAmt != null">
        and bdw_paterson_amt = #{bdwPatersonAmt}
      </if>
      <if test="sqBeAmt != null">
        and sq_be_amt = #{sqBeAmt}
      </if>
      <if test="sqBsAmt != null">
        and sq_bs_amt = #{sqBsAmt}
      </if>
      <if test="sqBgAmt != null">
        and sq_bg_amt = #{sqBgAmt}
      </if>
      <if test="sqBpAmt != null">
        and sq_bp_amt = #{sqBpAmt}
      </if>
      <if test="bdwAmt != null">
        and bdw_amt = #{bdwAmt}
      </if>
      <if test="sqAmt != null">
        and sq_amt = #{sqAmt}
      </if>
    </where>
  </select>

  <!--新增所有列-->
  <insert id="insert" keyProperty="id" useGeneratedKeys="true">
    insert into dws_member_grade_day(biz_date, bdw_earth_amt, bdw_moon_amt, bdw_mars_amt, bdw_neptune_amt, bdw_paterson_amt, sq_be_amt, sq_bs_amt, sq_bg_amt, sq_bp_amt, bdw_amt, sq_amt)
    values (#{bizDate}, #{bdwEarthAmt}, #{bdwMoonAmt}, #{bdwMarsAmt}, #{bdwNeptuneAmt}, #{bdwPatersonAmt}, #{sqBeAmt}, #{sqBsAmt}, #{sqBgAmt}, #{sqBpAmt}, #{bdwAmt}, #{sqAmt})
  </insert>

  <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
    insert into dws_member_grade_day(biz_date, bdw_earth_amt, bdw_moon_amt, bdw_mars_amt, bdw_neptune_amt, bdw_paterson_amt, sq_be_amt, sq_bs_amt, sq_bg_amt, sq_bp_amt, bdw_amt, sq_amt)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.bizDate}, #{entity.bdwEarthAmt}, #{entity.bdwMoonAmt}, #{entity.bdwMarsAmt}, #{entity.bdwNeptuneAmt}, #{entity.bdwPatersonAmt}, #{entity.sqBeAmt}, #{entity.sqBsAmt}, #{entity.sqBgAmt}, #{entity.sqBpAmt}, #{entity.bdwAmt}, #{entity.sqAmt})
    </foreach>
  </insert>

  <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
    insert into dws_member_grade_day(biz_date, bdw_earth_amt, bdw_moon_amt, bdw_mars_amt, bdw_neptune_amt, bdw_paterson_amt, sq_be_amt, sq_bs_amt, sq_bg_amt, sq_bp_amt, bdw_amt, sq_amt)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.bizDate}, #{entity.bdwEarthAmt}, #{entity.bdwMoonAmt}, #{entity.bdwMarsAmt}, #{entity.bdwNeptuneAmt}, #{entity.bdwPatersonAmt}, #{entity.sqBeAmt}, #{entity.sqBsAmt}, #{entity.sqBgAmt}, #{entity.sqBpAmt}, #{entity.bdwAmt}, #{entity.sqAmt})
    </foreach>
    on duplicate key update
    biz_date = values(biz_date),
    bdw_earth_amt = values(bdw_earth_amt),
    bdw_moon_amt = values(bdw_moon_amt),
    bdw_mars_amt = values(bdw_mars_amt),
    bdw_neptune_amt = values(bdw_neptune_amt),
    bdw_paterson_amt = values(bdw_paterson_amt),
    sq_be_amt = values(sq_be_amt),
    sq_bs_amt = values(sq_bs_amt),
    sq_bg_amt = values(sq_bg_amt),
    sq_bp_amt = values(sq_bp_amt),
    bdw_amt = values(bdw_amt),
    sq_amt = values(sq_amt)
  </insert>

  <!--通过主键修改数据-->
  <update id="update">
    update dws_member_grade_day
    <set>
      <if test="bizDate != null">
        biz_date = #{bizDate},
      </if>
      <if test="bdwEarthAmt != null">
        bdw_earth_amt = #{bdwEarthAmt},
      </if>
      <if test="bdwMoonAmt != null">
        bdw_moon_amt = #{bdwMoonAmt},
      </if>
      <if test="bdwMarsAmt != null">
        bdw_mars_amt = #{bdwMarsAmt},
      </if>
      <if test="bdwNeptuneAmt != null">
        bdw_neptune_amt = #{bdwNeptuneAmt},
      </if>
      <if test="bdwPatersonAmt != null">
        bdw_paterson_amt = #{bdwPatersonAmt},
      </if>
      <if test="sqBeAmt != null">
        sq_be_amt = #{sqBeAmt},
      </if>
      <if test="sqBsAmt != null">
        sq_bs_amt = #{sqBsAmt},
      </if>
      <if test="sqBgAmt != null">
        sq_bg_amt = #{sqBgAmt},
      </if>
      <if test="sqBpAmt != null">
        sq_bp_amt = #{sqBpAmt},
      </if>
      <if test="bdwAmt != null">
        bdw_amt = #{bdwAmt},
      </if>
      <if test="sqAmt != null">
        sq_amt = #{sqAmt},
      </if>
    </set>
    where id = #{id}
  </update>

  <!--通过主键删除-->
  <delete id="deleteById">
    delete from dws_member_grade_day where id = #{id}
  </delete>

    <select id="selectMemberScaleOfBdw" resultType="com.shands.mod.dao.model.datarevision.vo.GeneralDrawingVo">
        SELECT
          LEFT(n.ereryMonth,7) time,
          ROUND(IFNULL(bdw_amt, 0) / 10000,2) AS number
        FROM
          dws_member_grade_day m
          RIGHT JOIN
            (SELECT
              MAX(h.biz_date) AS ereryMonth
            FROM
              dws_member_grade_day h
            WHERE DATE_FORMAT(h.biz_date,'%Y-%m') > DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 12 MONTH),'%Y-%m')
            GROUP BY LEFT (h.biz_date, 7)) n
            ON m.biz_date = n.ereryMonth
    </select>
  <select id="selectMemberGradeScale"
    resultType="com.shands.mod.dao.model.datarevision.vo.MemberGradeScaleVo">
    SELECT bdw_earth_amt    bdwEarthAmt,
           bdw_moon_amt     bdwMoonAmt,
           bdw_mars_amt     bdwMarsAmt,
           bdw_neptune_amt  bdwNeptuneAmt,
           bdw_paterson_amt bdwPatersonAmt,
           sq_be_amt        sqBeAmt,
           sq_bs_amt        sqBsAmt,
           sq_bg_amt        sqBgAmt,
           sq_bp_amt        sqBpAmt
    from dws_member_grade_day
    where biz_date = (SELECT MAX(biz_date) bizDate from dws_member_grade_day);
  </select>
</mapper>