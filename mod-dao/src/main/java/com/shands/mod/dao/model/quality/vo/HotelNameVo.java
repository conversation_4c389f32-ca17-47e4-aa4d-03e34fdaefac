package com.shands.mod.dao.model.quality.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/23
 **/
@ApiModel("酒店任务分析看板部分出参")
@Data
public class HotelNameVo implements Serializable {

  private static final long serialVersionUID = 1756678574165418464L;

  @ApiModelProperty("酒店id")
  private Integer hotelId;

  @ApiModelProperty("酒店名称")
  private String hotelName;

  @ApiModelProperty("检查表id")
  private Integer checkId;

  @ApiModelProperty("检查表标题")
  private String checkTitle;

  @ApiModelProperty("'任务id'")
  private Integer taskId;

  @ApiModelProperty("检查类型")
  private String checkType;

  @ApiModelProperty("考核方式")
  private String assessmentType;

  @ApiModelProperty("考核方式名")
  private String assessmentTypeName;

  @ApiModelProperty("考核次数")
  private Long assessmentTimes;

  @ApiModelProperty("考核开始时间")
  private Date assessmentStartTime;

  @ApiModelProperty("考核结束时间")
  private Date assessmentEndTime;
}