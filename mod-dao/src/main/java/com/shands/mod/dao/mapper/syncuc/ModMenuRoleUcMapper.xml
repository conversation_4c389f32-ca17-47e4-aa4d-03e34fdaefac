<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shands.mod.dao.mapper.syncuc.ModMenuRoleUcMapper" >
  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.syncuc.ModMenuRoleUc" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="role_id" property="roleId" jdbcType="INTEGER" />
    <result column="permission_id" property="permissionId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, role_id, permission_id
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from mod_menu_role_uc
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from mod_menu_role_uc
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.shands.mod.dao.model.syncuc.ModMenuRoleUc" >
    insert into mod_menu_role_uc (id, role_id, permission_id
      )
    values (#{id,jdbcType=INTEGER}, #{roleId,jdbcType=INTEGER}, #{permissionId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.shands.mod.dao.model.syncuc.ModMenuRoleUc" >
    insert into mod_menu_role_uc
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="roleId != null" >
        role_id,
      </if>
      <if test="permissionId != null" >
        permission_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="roleId != null" >
        #{roleId,jdbcType=INTEGER},
      </if>
      <if test="permissionId != null" >
        #{permissionId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.syncuc.ModMenuRoleUc" >
    update mod_menu_role_uc
    <set >
      <if test="roleId != null" >
        role_id = #{roleId,jdbcType=INTEGER},
      </if>
      <if test="permissionId != null" >
        permission_id = #{permissionId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.syncuc.ModMenuRoleUc" >
    update mod_menu_role_uc
    set role_id = #{roleId,jdbcType=INTEGER},
      permission_id = #{permissionId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!--删除掉所有有关roleId的数据-->
  <delete id="deleteByRole">
    delete from mod_menu_role_uc where role_id = #{roleId}
  </delete>


  <insert id="insertBatch">
    insert into mod_menu_role_uc (role_id, permission_id)
    values
    <foreach collection="list" item="item" separator=",">
    (#{item.roleId},#{item.permissionId})
    </foreach>
    </insert>

</mapper>