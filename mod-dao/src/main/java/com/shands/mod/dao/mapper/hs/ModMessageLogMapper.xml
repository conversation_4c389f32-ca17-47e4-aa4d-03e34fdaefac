<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.hs.ModMessageLogMapper">
  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.v0701.pojo.ModMessageLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="message_type" jdbcType="VARCHAR" property="messageType" />
    <result column="message_content" jdbcType="VARCHAR" property="messageContent" />
    <result column="message_title" jdbcType="VARCHAR" property="messageTitle" />
    <result column="extend_type" jdbcType="VARCHAR" property="extendType" />
    <result column="extend_no" jdbcType="VARCHAR" property="extendNo" />
    <result column="send_state" jdbcType="TINYINT" property="sendState" />
    <result column="read_state" jdbcType="TINYINT" property="readState" />
    <result column="recipient_type" jdbcType="VARCHAR" property="recipientType" />
    <result column="recipient_id" jdbcType="VARCHAR" property="recipientId" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="hotel_name" jdbcType="VARCHAR" property="hotelName" />
    <result column="work_id" jdbcType="INTEGER" property="workId"/>
    <result column="promote_state" jdbcType="INTEGER" property="promoteState"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, message_type, message_title, message_content, extend_type, extend_no, send_state, read_state, recipient_type,
    recipient_id, group_id, company_id, create_time, update_time, remark,work_id,promote_state
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mod_message_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mod_message_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.shands.mod.dao.model.v0701.pojo.ModMessageLog" useGeneratedKeys="true">
    insert into mod_message_log (message_type, message_title, message_content, extend_no, extend_type,
      send_state, read_state, recipient_type, 
      recipient_id, group_id, company_id, 
      create_time, update_time, remark,work_id
      )
    values (#{messageType,jdbcType=VARCHAR}, #{messageTitle,jdbcType=VARCHAR}, #{messageContent,jdbcType=VARCHAR}, #{extendNo,jdbcType=VARCHAR}, #{extendType,jdbcType=VARCHAR},
      #{sendState,jdbcType=TINYINT}, #{readState,jdbcType=TINYINT}, #{recipientType,jdbcType=VARCHAR},
      #{recipientId,jdbcType=VARCHAR}, #{groupId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR},
      #{workId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shands.mod.dao.model.v0701.pojo.ModMessageLog" useGeneratedKeys="true">
    insert into mod_message_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="messageType != null">
        message_type,
      </if>
      <if test="messageContent != null">
        message_content,
      </if>
      <if test="extendNo != null">
        extend_no,
      </if>
      <if test="sendState != null">
        send_state,
      </if>
      <if test="readState != null">
        read_state,
      </if>
      <if test="recipientType != null">
        recipient_type,
      </if>
      <if test="recipientId != null">
        recipient_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="messageType != null">
        #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="messageContent != null">
        #{messageContent,jdbcType=VARCHAR},
      </if>
      <if test="extendNo != null">
        #{extendNo,jdbcType=VARCHAR},
      </if>
      <if test="sendState != null">
        #{sendState,jdbcType=TINYINT},
      </if>
      <if test="readState != null">
        #{readState,jdbcType=TINYINT},
      </if>
      <if test="recipientType != null">
        #{recipientType,jdbcType=VARCHAR},
      </if>
      <if test="recipientId != null">
        #{recipientId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.v0701.pojo.ModMessageLog">
    update mod_message_log
    <set>
      <if test="messageType != null">
        message_type = #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="messageContent != null">
        message_content = #{messageContent,jdbcType=VARCHAR},
      </if>
      <if test="extendNo != null">
        extend_no = #{extendNo,jdbcType=VARCHAR},
      </if>
      <if test="sendState != null">
        send_state = #{sendState,jdbcType=TINYINT},
      </if>
      <if test="readState != null">
        read_state = #{readState,jdbcType=TINYINT},
      </if>
      <if test="recipientType != null">
        recipient_type = #{recipientType,jdbcType=VARCHAR},
      </if>
      <if test="recipientId != null">
        recipient_id = #{recipientId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectMessageCount" resultType="int">
    select count(id) from mod_message_log where recipient_id = #{userId,jdbcType=VARCHAR} and read_state = 0
  </select>

  <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.v0701.pojo.ModMessageLog">
    update mod_message_log
    set message_type = #{messageType,jdbcType=VARCHAR},
      message_content = #{messageContent,jdbcType=VARCHAR},
      extend_no = #{extendNo,jdbcType=VARCHAR},
      send_state = #{sendState,jdbcType=TINYINT},
      read_state = #{readState,jdbcType=TINYINT},
      recipient_type = #{recipientType,jdbcType=VARCHAR},
      recipient_id = #{recipientId,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <!--小铃铛-->
  <select id="messageForU" resultMap="BaseResultMap">
    select
      t.id,
      t.message_type,
      t.message_title,
      t.message_content,
      t.extend_type,
      t.extend_no,
      t.send_state,
      t.read_state,
      t.recipient_type,
      t.recipient_id,
      t.group_id,
      t.company_id,
      t.create_time,
      t.update_time,
      t.remark,
      a.hotel_name
    from mod_message_log t
    left join mod_hotel_info a on t.company_id=a.hotel_id
    where t.recipient_id = #{userId,jdbcType=VARCHAR} and t.message_type = #{messageType}
    and t.send_state=#{sendState}
    ORDER by t.create_time desc
  </select>

  <select id="queryMessageByStatu" resultMap="BaseResultMap">
    select
      t.id,
      t.message_type,
      t.message_title,
      t.message_content,
      t.extend_type,
      t.extend_no,
      t.send_state,
      t.read_state,
      t.recipient_type,
      t.recipient_id,
      t.group_id,
      t.company_id,
      t.create_time,
      t.update_time,
      t.remark,
      a.hotel_name
    from mod_message_log t left join mod_hotel_info a on t.company_id=a.hotel_id
    where t.recipient_id = #{recipientId} and t.message_type = #{messageType}
      and t.send_state=#{sendState}
      <if test="readState != null">
        and t.read_state = #{readState}
      </if>
      <if test="messageTitle != '' and messageTitle != null ">
        and t.message_title = #{messageTitle}
      </if>
      <if test="companyId != null">
        and t.company_id = #{companyId}
      </if>
    ORDER by t.create_time desc, t.id desc
    limit 2
  </select>

  <!--一键已读-->
  <update id="oneKeyRead">
    update
    mod_message_log
    set
    read_state=#{readState}
    where
    recipient_id = #{userId,jdbcType=VARCHAR} and read_state = 0
  </update>

  <update id="coverData" >
    update mod_message_log set work_id = cast(extend_no as signed)  where extend_no != '' and extend_type = '200'
  </update>
</mapper>