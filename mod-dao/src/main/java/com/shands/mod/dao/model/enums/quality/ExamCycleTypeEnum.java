package com.shands.mod.dao.model.enums.quality;

import java.util.ArrayList;
import java.util.List;

/**
 * 考核周期
 */
public enum ExamCycleTypeEnum {
  DAY("每日"),
  MONTH("每月"),
  WEEK("每周");
  private String description;

  ExamCycleTypeEnum(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public static List<ExamCycleTypeEnum> getList(){
    List<ExamCycleTypeEnum> demoList = new ArrayList<ExamCycleTypeEnum>();
    for(ExamCycleTypeEnum demo : ExamCycleTypeEnum.values()){
      demoList.add(demo);
    }
    return demoList;
  }

  public static String getValues(String key){
    ExamCycleTypeEnum[] values = ExamCycleTypeEnum.values();
    for (ExamCycleTypeEnum value : values) {
      if(value.name().equals(key)){
        return value.getDescription();
      }
    }
    return "";
  }
}
