package com.shands.mod.dao.model.datarevision.po;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 德胧生态营收日报
 * @Author: 贺礼波
 * @CreateDate: 2023-03-21
 */
@Data
public class AdsAppTradeRevenueD {

  /**
   * 主键id
   */
  private Integer id;

  /**
   * 酒店代码
   */
  private String hotelCode;

  /**
   * 酒店名称
   */
  private String hotelName;

  /**
   * 事业部代码
   */
  private String bizDepartmentCode;

  /**
   * 事业部名称
   */
  private String hotelBizDepartment;

  /**
   * 品牌代码
   */
  private String brandCode;

  /**
   * 品牌名称
   */
  private String hotelBrand;

  /**
   * 营业日期
   */
  private Date bizDate;

  /**
   * 物理房量
   */
  private Integer roomNum;

  /**
   * 会员房费收入(元)-该酒店发展的会员产生的
   */
  private BigDecimal memRoomsAmt;

  /**
   * 会员间夜-该酒店发展的会员产生的
   */
  private BigDecimal memRoomNightsN;

  /**
   * 过夜房
   */
  private BigDecimal roomNightsN;
}
