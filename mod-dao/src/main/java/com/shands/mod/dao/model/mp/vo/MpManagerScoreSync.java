package com.shands.mod.dao.model.mp.vo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * mp_manager_score
 *
 * <AUTHOR>
@Data
public class MpManagerScoreSync implements Serializable {

  /**
   * 主键
   */
  private Integer id;

  /**
   * 考核方案code
   */
  private String taskCode;

  /**
   * 考核方案名称
   */
  private String taskName;

  /**
   * 酒店code
   */
  private String hotelCode;

  /**
   * 酒店名称
   */
  private String hotelName;

  /**
   * 用户id（通宝uc_id）
   */
  private Integer managerId;

  /**
   * 用户名称
   */
  private String managerName;

  /**
   * 用户手机号
   */
  private String mobile;

  /**
   * 用户通宝id
   */
  private Integer ucId;

  /**
   * 酒店通宝id
   */
  private Integer ucCompanyId;

  /**
   * 酒店总考核分
   */
  private Double hotelScore;

  /**
   * 总经理综合考核分
   */
  private Double managerSocre;

  /**
   * 总经理该酒店累计得分
   */
  private Double managerCumSocre;

  /**
   * 总经理该酒店累计天数
   */
  private Integer managerCumDay;

  /**
   * 事业部代码
   */
  private String departmentCode;

  /**
   * 事业部名称
   */
  private String departmentName;

  /**
   * 品牌编码
   */
  private String brandCode;

  /**
   * 品牌名称
   */
  private String brandName;

  /**
   * 酒店状态 OPEN在营 PREPARE筹开
   */
  private String hotelStatus;

  /**
   * 管理方式编码
   */
  private String manageCode;

  /**
   * 管理方式名称
   */
  private String manageName;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 修改时间
   */
  private Date updateTime;

  /**
   * 任务开始时间
   */
  private Date startDate;

  /**
   * 任务结束时间
   */
  private Date endDate;

  private static final long serialVersionUID = 1L;
}