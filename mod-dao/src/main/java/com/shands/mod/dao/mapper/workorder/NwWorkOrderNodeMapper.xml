<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.workorder.NwWorkOrderNodeMapper">

    <resultMap type="com.shands.mod.dao.model.workorder.po.NwWorkOrderNode" id="NwWorkOrderNodeMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
        <result property="bussinessName" column="bussiness_name" jdbcType="VARCHAR"/>
        <result property="acceptType" column="accept_type" jdbcType="VARCHAR"/>
        <result property="acceptUser" column="accept_user" jdbcType="VARCHAR"/>
        <result property="templateId" column="template_id" jdbcType="INTEGER"/>
        <result property="replaceAcceptUser" column="replace_accept_user" jdbcType="INTEGER"/>
        <result property="acceptUserId" column="accept_user_id" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="NwWorkOrderNodeMap">
        select
          id, version, create_time, create_user, update_time, update_user, deleted, bussiness_name, accept_type, accept_user,template_id,replace_accept_user,accept_user_id
        from nw_work_order_node
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="NwWorkOrderNodeMap">
        select
          id, version, create_time, create_user, update_time, update_user, deleted, bussiness_name, accept_type, accept_user,template_id,replace_accept_user,accept_user_id
        from nw_work_order_node
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="bussinessName != null and bussinessName != ''">
                and bussiness_name = #{bussinessName}
            </if>
            <if test="acceptType != null and acceptType != ''">
                and accept_type = #{acceptType}
            </if>
            <if test="acceptUser != null and acceptUser != ''">
                and accept_user = #{acceptUser}
            </if>
            <if test="templateId != null">
              and template_id = #{templateId}
            </if>
          <if test="replaceAcceptUser != null">
            and replace_accept_user = #{replaceAcceptUser}
          </if>
          <if test="acceptUserId != null">
            and accept_user_id = #{acceptUserId}
          </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from nw_work_order_node
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="bussinessName != null and bussinessName != ''">
                and bussiness_name = #{bussinessName}
            </if>
            <if test="acceptType != null and acceptType != ''">
                and accept_type = #{acceptType}
            </if>
            <if test="acceptUser != null and acceptUser != ''">
                and accept_user = #{acceptUser}
            </if>
            <if test="templateId != null">
              and template_id = #{templateId}
            </if>
          <if test="replaceAcceptUser != null">
            and replace_accept_user = #{replaceAcceptUser}
          </if>
          <if test="acceptUserId != null">
            and accept_user_id = #{acceptUserId}
          </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nw_work_order_node(version, create_time, create_user, update_time, update_user, deleted, bussiness_name, accept_type, accept_user,template_id,replace_accept_user,accept_user_id)
        values (#{version}, #{createTime}, #{createUser}, #{updateTime}, #{updateUser}, #{deleted}, #{bussinessName}, #{acceptType}, #{acceptUser},#{templateId},#{replaceAcceptUser},#{acceptUserId})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into nw_work_order_node(version, create_time, create_user, update_time, update_user, deleted, bussiness_name, accept_type, accept_user,template_id,replace_accept_user,accept_user_id)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.version}, #{entity.createTime}, #{entity.createUser}, #{entity.updateTime}, #{entity.updateUser}, #{entity.deleted}, #{entity.bussinessName}, #{entity.acceptType}, #{entity.acceptUser},#{entity.templateId},#{entity.replaceAcceptUser},#{entity.acceptUserId})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into nw_work_order_node(version, create_time, create_user, update_time, update_user, deleted, bussiness_name, accept_type, accept_user,template_id,replace_accept_user,accept_user_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.version}, #{entity.createTime}, #{entity.createUser}, #{entity.updateTime}, #{entity.updateUser}, #{entity.deleted}, #{entity.bussinessName}, #{entity.acceptType}, #{entity.acceptUser},#{entity.templateId},#{entity.replaceAcceptUser},#{entity.acceptUserId})
        </foreach>
        on duplicate key update
        version = values(version),
        create_time = values(create_time),
        create_user = values(create_user),
        update_time = values(update_time),
        update_user = values(update_user),
        deleted = values(deleted),
        bussiness_name = values(bussiness_name),
        accept_type = values(accept_type),
        accept_user = values(accept_user),
        template_id = values (template_id)
      replace_accept_user = values (replace_accept_user)
      accept_user_id = values (accept_user_id)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nw_work_order_node
        <set>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="createUser != null">
                create_user = #{createUser},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="bussinessName != null and bussinessName != ''">
                bussiness_name = #{bussinessName},
            </if>
            <if test="acceptType != null and acceptType != ''">
                accept_type = #{acceptType},
            </if>
            <if test="acceptUser != null and acceptUser != ''">
                accept_user = #{acceptUser},
            </if>
            <if test="templateId != null">
              template_id = #{templateId},
            </if>
          <if test="replaceAcceptUser != null">
            and replace_accept_user = #{replaceAcceptUser}
          </if>
          <if test="acceptUserId != null">
            and accept_user_id = #{acceptUserId}
          </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        z from nw_work_order_node where id = #{id}
    </delete>

  <delete id="deleteByTemplateId">
    update nw_work_order_node
    set deleted = 1
        where template_id = #{templateId}
  </delete>

  <select id="selectByTemplateId" resultType="com.shands.mod.dao.model.workorder.vo.NwNodeVo">
    select id nodeId,bussiness_name bussinessName,accept_type acceptType,accept_user acceptUser,accept_user_id acceptUserId,
           replace_accept_user replaceAcceptUser
        from nw_work_order_node
    where template_id = #{templateId}
    and deleted = 0
  </select>
</mapper>

