<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.quality.QtRectificationUserMapper">

  <resultMap type="com.shands.mod.dao.model.quality.po.QtRectificationUser"
    id="QtRectificationUserMap">
    <result property="id" column="id" jdbcType="INTEGER"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    <result property="rectificationId" column="rectification_id" jdbcType="INTEGER"/>
    <result property="remark" column="remark" jdbcType="VARCHAR"/>
    <result property="createUser" column="create_user" jdbcType="INTEGER"/>
    <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
    <result property="ifDelete" column="if_delete" jdbcType="BOOLEAN"/>
    <result property="userId" column="user_id" jdbcType="INTEGER"/>
    <result property="type" column="type" jdbcType="VARCHAR"/>
  </resultMap>

  <!--查询单个-->
  <select id="queryById" resultMap="QtRectificationUserMap">
    select id,
           create_time,
           update_time,
           rectification_id,
           remark,
           create_user,
           update_user,
           if_delete,
           user_id,
           type
    from qt_rectification_user
    where id = #{id}
  </select>

  <!--统计总行数-->
  <select id="count" resultType="java.lang.Long">
    select count(1)
    from qt_rectification_user
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime}
      </if>
      <if test="rectificationId != null">
        and rectification_id = #{rectificationId}
      </if>
      <if test="remark != null and remark != ''">
        and remark = #{remark}
      </if>
      <if test="createUser != null">
        and create_user = #{createUser}
      </if>
      <if test="updateUser != null">
        and update_user = #{updateUser}
      </if>
      <if test="ifDelete != null">
        and if_delete = #{ifDelete}
      </if>
      <if test="userId != null">
        and user_id = #{userId}
      </if>
      <if test="type != null and type != ''">
        and type = #{type}
      </if>
    </where>
  </select>

  <!--新增所有列-->
  <insert id="insert" keyProperty="id" useGeneratedKeys="true">
    insert into qt_rectification_user(create_time, update_time, rectification_id, remark,
                                      create_user, update_user, if_delete, user_id, type)
    values (#{createTime}, #{updateTime}, #{rectificationId}, #{remark}, #{createUser},
            #{updateUser}, #{ifDelete}, #{userId}, #{type})
  </insert>

  <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
    insert into qt_rectification_user(create_time, update_time, rectification_id, remark,
    create_user, update_user, if_delete, user_id, type)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.createTime}, #{entity.updateTime}, #{entity.rectificationId}, #{entity.remark},
      #{entity.createUser}, #{entity.updateUser}, #{entity.ifDelete}, #{entity.userId},
      #{entity.type})
    </foreach>
  </insert>

  <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
    insert into qt_rectification_user(create_time, update_time, rectification_id, remark,
    create_user, update_user, if_delete, user_id, type)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.createTime}, #{entity.updateTime}, #{entity.rectificationId}, #{entity.remark},
      #{entity.createUser}, #{entity.updateUser}, #{entity.ifDelete}, #{entity.userId},
      #{entity.type})
    </foreach>
    on duplicate key update
    create_time = values(create_time),
    update_time = values(update_time),
    rectification_id = values(rectification_id),
    remark = values(remark),
    create_user = values(create_user),
    update_user = values(update_user),
    if_delete = values(if_delete),
    user_id = values(user_id),
    type = values(type)
  </insert>

  <!--通过主键修改数据-->
  <update id="update">
    update qt_rectification_user
    <set>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="rectificationId != null">
        rectification_id = #{rectificationId},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark},
      </if>
      <if test="createUser != null">
        create_user = #{createUser},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser},
      </if>
      <if test="ifDelete != null">
        if_delete = #{ifDelete},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="type != null and type != ''">
        type = #{type},
      </if>
    </set>
    where id = #{id}
  </update>

  <!--通过主键删除-->
  <delete id="deleteById">
    delete
    from qt_rectification_user
    where id = #{id}
  </delete>

  <select id="getNameByTaskId" resultType="java.lang.String">
    SELECT
      GROUP_CONCAT(DISTINCT mu.NAME) checkPerson
    FROM
      qt_rectification_user ru
      LEFT JOIN mod_user mu
        ON ru.user_id = mu.id
    WHERE ru.if_delete = 0
      AND ru.type = #{insPersonType}
      AND ru.rectification_id = #{taskId}
    GROUP BY
        ru.rectification_id,
        ru.type ;
  </select>

  <select id="getIdByTaskId" resultType="java.lang.String">
    SELECT
      GROUP_CONCAT(DISTINCT ru.user_id) checkPerson
    FROM
      qt_rectification_user ru
    WHERE ru.if_delete = 0
      AND ru.type = #{insPersonType}
      AND ru.rectification_id = #{taskId}
    GROUP BY
      ru.rectification_id,
      ru.type ;
  </select>

  <delete id="deleteByTaskId">
    delete
    from qt_rectification_user
    where rectification_id = #{taskId}
      and `type` = #{insPersonType}
  </delete>

  <select id="getRectificationUserName" resultType="java.lang.String">
    SELECT GROUP_CONCAT(mu.`NAME`), du.type
    from qt_rectification_user du
    LEFT JOIN mod_user mu ON du.user_id = mu.id
    <where>
      du.type = #{type}
      and du.if_delete = 0 and rectification_id = #{rectificationId}
      GROUP BY du.type;
    </where>

  </select>


  <select id="getRectificationUser"
    resultType="com.shands.mod.dao.model.quality.vo.v2.UserNameVo">
    SELECT GROUP_CONCAT(mu.`NAME`) name, du.type
    from qt_rectification_user du
           LEFT JOIN mod_user mu ON du.user_id = mu.id
    where du.if_delete = 0 and  rectification_id = #{rectificationId}
    GROUP BY du.type;
  </select>
  <select id="queryUserIdByTaskId"
    resultType="com.shands.mod.dao.model.quality.vo.v2.RectificationUserVo">
 select ru.user_id as userId,mu.`NAME` as userName
    from qt_rectification_user ru
    LEFT JOIN mod_user mu ON ru.user_id = mu.id
    where ru.if_delete = 0 and  ru.type = #{insPersonType}
      and ru.rectification_id = #{taskId}
  </select>

  <select id="queryUserIdByRectificationId" resultMap="QtRectificationUserMap">
    select
      *
    from qt_rectification_user
    where if_delete = 0 and  type =#{insPersonType} and rectification_id=#{taskId}
  </select>

  <select id="queryUserIdAndRectificationId" resultMap="QtRectificationUserMap">
    select
      *
    from qt_rectification_user
    where if_delete = 0  and rectification_id=#{taskId} and user_id = #{userId}
  </select>
</mapper>
