package com.shands.mod.dao.model.homerevision;

/**
 * <AUTHOR>
 * @date 2020/7/16
 * @desc 订单枚举类
*/
public enum StatisticsDataEnum {

  OPERATE_ANALYZE("OPERATE_ANALYZE","经营数据"),
  MEMBER_ANALYZE("MEMBER_ANALYZE","会员数据"),
  COMMENT_ANALYZE("COMMENT_ANALYZE","网评分析"),
  GW_ANALYZE("GW_ANALYZE","官网分析"),
  SERVICE_STATISTICS("SERVICE_STATISTICS","服务统计"),
  FACE_MEMBER("FACE_MEMBER","面对面会员发展"),
  STAFF_ACTIVATION("STAFF_ACTIVATION","员工激活率"),
  MEMBER_FINISH("MEMBER_FINISH","会员发展完成率"),
  GW_EXCITATION("GW_EXCITATION","官网激励产量"),
  PROJECT_ANALYZE("PROJECT_ANALYZE","项目分析"),
  ENTERPRISE_MEMBER("ENTERPRISE_MEMBER","企业个人会员"),
  NEW_CONSUMPTION_MEMBER("NEW_CONSUMPTION_MEMBER","首消会员数"),
  CONSUMPTION_MEMBER("CONSUMPTION_MEMBER","消费会员"),
  ROOM_RIGHT("ROOM_RIGHT","酒店发展会员间夜贡献"),
  ROOM_REVENUE("ROOM_REVENUE", "酒店发展会员营收"),
  MEMBER_REVENUE("MEMBER_REVENUE", "发展会员营收")
  ;

  private String moduleCode;

  private String moduleName;

  StatisticsDataEnum(String moduleCode, String moduleName) {
    this.moduleCode = moduleCode;
    this.moduleName = moduleName;
  }

  public String getModuleCode() {
    return moduleCode;
  }

  public void setModuleCode(String moduleCode) {
    this.moduleCode = moduleCode;
  }

  public String getModuleName() {
    return moduleName;
  }

  public void setModuleName(String moduleName) {
    this.moduleName = moduleName;
  }

  public static String getModuleCode(String moduleName) {
    for (StatisticsDataEnum item : StatisticsDataEnum.values()) {
      if (item.moduleName.equals(moduleName)) {
        return item.moduleCode;
      }
    }
    return null;
  }

  public static String getModuleName(String moduleCode) {
    for (StatisticsDataEnum item : StatisticsDataEnum.values()) {
      if (item.moduleCode.equals(moduleCode)) {
        return item.moduleName;
      }
    }
    return null;
  }

}
