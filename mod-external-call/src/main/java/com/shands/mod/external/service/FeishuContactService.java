package com.shands.mod.external.service;

import com.lark.oapi.service.contact.v3.model.User;
import com.lark.oapi.service.contact.v3.model.UserContactInfo;
import java.util.List;

/**
 * 飞书通讯录api
 *
 * <AUTHOR>
 */
public interface FeishuContactService {

  /**
   * 飞书添加或修改用户
   * @param appId 飞书应用id
   * @param user 飞书用户
   */
  void addOrModifyUser(String appId, User user,List<String> feiShuAppIds);

  /**
   * 飞书添加用户
   *
   * @param appId 飞书应用id
   * @param user  飞书用户
   * @return
   */
  User addUser(String appId, User user);

  /**
   * 飞书删除用户
   * @param appId 飞书应用id
   * @param userId 飞书用户id
   */
  void deleteUser(String appId, String userId);


  /**
   * 飞书删除或修改用户
   * @param appId 飞书应用id
   * @param userId 飞书用户id
   * @param feiShuDepartmentId 飞书部门id
   */
  void deleteOrModifyUser(String appId, String userId, String feiShuDepartmentId,List<String> needDeleteFeiShuAppIds);

  /**
   * 根据user_id获取飞书用户信息
   * @param appId 飞书应用id
   * @param userId 飞书用户id
   */
  User getUserInfoByUserId(String appId, String userId);

  List<UserContactInfo> batchGetUserId(String appId, List<String> mobiles);
}
