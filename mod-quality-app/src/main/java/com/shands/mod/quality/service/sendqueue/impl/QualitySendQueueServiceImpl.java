package com.shands.mod.quality.service.sendqueue.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shands.mod.config.RocketMqConfig;
import com.shands.mod.dao.model.quality.bo.v2.SendRectificationTaskBo;
import com.shands.mod.dao.model.quality.po.QtCheckTask;
import com.shands.mod.dao.model.quality.vo.v2.ModUserHotelVo;
import com.shands.mod.quality.service.sendqueue.QualitySendQueueService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import com.shands.mod.rocketmq.RocketMqProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description:
 * @Author: Java菜鸟
 * @CreateDate: 2022/4/20 15:32
 */
@Service
@Slf4j
public class QualitySendQueueServiceImpl implements QualitySendQueueService {

  @Resource
  private RocketMqProducer rocketMqProducer;

  /**
   * 发送检查人队列
   */
  @Override
  public void sendCheckQueue(SendRectificationTaskBo sendRectificationTaskBo){

    try {
      Map<String, Object> sendMap = new HashMap<>();
      sendMap.put("sendRectificationTask", JSONObject.toJSONString(sendRectificationTaskBo));
      //加入队列
      rocketMqProducer.syncSendMessage(RocketMqConfig.QUALITY_DIRECT_MESSAGE_RECTIFICATION_TOPIC,
          JSONObject.toJSONString(sendMap));
    } catch (Exception e) {
      log.error("sendCheckQueue_消息推送失败_ " + JSON.toJSONString(sendRectificationTaskBo), e);
    }
  }

  @Override
  public void sendRectificationProgressTask(List<ModUserHotelVo> user,long sendMs, QtCheckTask checkTask) {
    try {
      Map<String, Object> sendMap = new HashMap<>();
      sendMap.put("userList", JSONObject.toJSONString(user));
      sendMap.put("checkTask", checkTask);
      //加入队列
      rocketMqProducer.syncSendDelayMessage(RocketMqConfig.QUALITY_RECTIFICATION_PROGRESS_PUSH_TOPIC,
          JSONObject.toJSONString(sendMap), sendMs);
    } catch (Exception e) {
      log.error("sendRectificationProgressTask_消息推送失败_ " + JSON.toJSONString(user), e);
    }
  }
}
