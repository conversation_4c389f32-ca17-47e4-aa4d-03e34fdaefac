package com.shands.mod.quality.service.v2.role.impl;

import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.mapper.syncuc.ModRoleUcDao;
import com.shands.mod.dao.mapper.syncuc.ModUserDao;
import com.shands.mod.dao.model.quality.vo.v2.ModRoleVo;
import com.shands.mod.dao.model.quality.vo.v2.ModUserHotelVo;
import com.shands.mod.dao.model.syncuc.ModRoleUc;
import com.shands.mod.external.model.dto.UcAuthenticationDto;
import com.shands.mod.external.service.UcAuthenticationService;
import com.shands.mod.quality.service.v2.role.IQualityRoleService;
import com.shands.mod.util.BaseThreadLocalHelper;
import com.shands.uc.base.vo.ResultVO;
import com.shands.uc.model.auth.UserLoginRes;
import com.shands.uc.model.req.v3.PublicLoginReq;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 整改任务相关接口实现类
 */

@Slf4j
@Service
public class QualityRoleServiceImpl implements IQualityRoleService {

  @Resource
  private ModRoleUcDao modRoleUcDao;

  @Resource
  private ModUserDao modUserDao;

  @Autowired
  private UcAuthenticationService ucAuthenticationService;

  @Value("${uc.sercret:380583f66a4c462ba62ca690c231be9f}")
  private String ucSercret;

  @Value("${uc.appId:mod3}")
  private String ucAppId;

  @Value("${uc.domain:http://test-shandsuc3.shands.com}")
  private String domain;

  @Value("${uc.ucUserName:admin}")
  private String ucUserName;

  @Value("${uc.ucPassword:123456}")
  private String ucPassword;

  @Override
  public List<ModUserHotelVo> getUserInfo(String roleCode) {
    List<ModUserHotelVo> users = new ArrayList<>();
    HashMap<String, String> head = new HashMap<>();
    head.put("uc-token", BaseThreadLocalHelper.getToken());
    try {
      ModRoleUc modRoleUc = modRoleUcDao.queryByCode(roleCode);
      if (modRoleUc != null) {

        UcAuthenticationDto ucAuthenticationDto = new UcAuthenticationDto();
        ucAuthenticationDto.setAppId(ucAppId);
        ucAuthenticationDto.setSecret(ucSercret);
        ucAuthenticationDto.setDomain(domain);

        PublicLoginReq publicLoginReq = new PublicLoginReq();
        publicLoginReq.setAppId(ucAppId);
        publicLoginReq.setUsername(ucUserName);
        publicLoginReq.setPassword(ucPassword);

        //调用通宝接口 换取token信息
        UserLoginRes userLoginRes = ucAuthenticationService.
            loginByUserName(ucAuthenticationDto, publicLoginReq);

        if (userLoginRes == null ||
            !org.springframework.util.StringUtils.hasText(userLoginRes.getToken())) {
          throw new RuntimeException("uc接口鉴权信息获取失败");
        }

        //设置token请求头
        ucAuthenticationDto.setToken(userLoginRes.getToken());

        ResultVO resultVO = ucAuthenticationService.roleUserList(ucAuthenticationDto,modRoleUc.getUcId(), -1,1);
        log.info(resultVO.getResult().toString());
        JSONObject jsonObject = JSONObject.parseObject(resultVO.getResult().toString());
        // 获取到key为shoppingCartItemList的值
        String roles = jsonObject.getString("records");
        List<ModRoleVo> modRoleVoList = JSONObject.parseArray(roles,
            ModRoleVo.class);
        List<Integer> modIds = modRoleVoList.stream().map(ModRoleVo::getUserId)
            .collect(Collectors.toList());
        users = modUserDao.getListByUcids(modIds);
      }
      return users;
    } catch (Exception e) {
      return users;
    }
  }
}
