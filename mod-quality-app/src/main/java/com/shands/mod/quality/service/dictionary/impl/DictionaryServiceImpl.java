package com.shands.mod.quality.service.dictionary.impl;

import com.alibaba.fastjson.JSON;
import com.shands.mod.dao.mapper.quality.QtCheckDetailsMapper;
import com.shands.mod.dao.mapper.quality.QtCheckDictionaryMapper;
import com.shands.mod.dao.mapper.quality.QtCheckTaskMapper;
import com.shands.mod.dao.model.enums.quality.CheckItemsTypeEnum;
import com.shands.mod.dao.model.enums.quality.CheckTypeEnum;
import com.shands.mod.dao.model.quality.bo.DictionaryBo;
import com.shands.mod.dao.model.quality.po.QtCheckTask;
import com.shands.mod.dao.model.quality.vo.DictionaryVo;
import com.shands.mod.dao.model.quality.vo.v2.DictionaryV2Vo;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.quality.service.dictionary.DictionaryService;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description:
 * @Author: Java菜鸟
 * @CreateDate: 2022/3/18 10:07
 */
@Slf4j
@Service
public class DictionaryServiceImpl implements DictionaryService {

  @Resource
  private QtCheckDictionaryMapper dictionaryMapper;
  @Resource
  private QtCheckTaskMapper checkTaskMapper;
  @Resource
  private QtCheckDetailsMapper checkDetailsMapper;


  @Override
  public List<DictionaryVo> dictionaryList(DictionaryBo dictionaryBo) {
    if (CheckItemsTypeEnum.CHECK_DEPT.name().equals(dictionaryBo.getDictionaryCode()) ||
        CheckItemsTypeEnum.CHECK_DIMENSION.name().equals(dictionaryBo.getDictionaryCode())) {
      List<DictionaryVo> dictionaryVos = null;
      try {
        if (dictionaryBo.getTaskId() != null) {
          //taskId 不等于null 查询明细表部门
          QtCheckTask checkTask = checkTaskMapper.queryById(dictionaryBo.getTaskId());
          if (CheckTypeEnum.BLOC_INSPECTION.name().equals(checkTask.getCheckType())) {
            dictionaryVos = checkDetailsMapper.selectByDept(
                checkTask.getCheckType(), checkTask.getCheckId());
          }
        } else {
          dictionaryVos = dictionaryMapper.dictionaryList(dictionaryBo);
        }
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        throw new ServiceException("查询异常");
      }
      log.info("dictionaryList_响应结果_{}", JSON.toJSONString(dictionaryVos));
      return dictionaryVos;
    }
    return null;
  }

  @Override
  public List<DictionaryV2Vo> qtDictionaryList(String type) {
    List<DictionaryV2Vo> dictionaryV2VoList = dictionaryMapper.qtDictionaryList(type);
    return dictionaryV2VoList;
  }
}
