package com.shands.mod.comment.controller;

import com.shands.mod.comment.req.CommentChannelNowReq;
import com.shands.mod.comment.req.CommentChannelReq;
import com.shands.mod.comment.req.CommentRankNowReq;
import com.shands.mod.comment.req.CommentRankReq;
import com.shands.mod.comment.req.CommentReportRankReq;
import com.shands.mod.comment.req.CommentReportTrendReq;
import com.shands.mod.comment.req.CommentTrendNewScoreReq;
import com.shands.mod.comment.res.CommentIndexRes;
import com.shands.mod.comment.res.CommentIndexVo;
import com.shands.mod.comment.service.IModCommentReportService;
import com.shands.mod.comment.service.IModCommentService;
import com.shands.mod.comment.util.DateUtil;
import com.shands.mod.comment.util.ThreadLocalHelper;
import com.shands.mod.comment.vo.IndexTimeVO;
import com.shands.mod.comment.vo.IndexVO;
import com.shands.mod.controller.BaseController;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.enums.DescEnum;
import com.shands.mod.dao.model.enums.OwnershipEnum;
import com.shands.mod.dao.model.homerevision.HomeDataEnum;
import com.shands.mod.dao.model.req.comment.CommentReq;
import com.shands.mod.dao.model.res.comment.CommentRes;
import com.shands.mod.dao.model.res.gwincentive.OwnershipRes;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.model.wp.CommentChannelVo;
import com.shands.mod.dao.model.wp.CommentHotelRankNewVo;
import com.shands.mod.dao.model.wp.CommentRankNewVo;
import com.shands.mod.dao.model.wp.CommentRankVo;
import com.shands.mod.dao.model.wp.CommentReportNewScoreVo;
import com.shands.mod.dao.model.wp.CommentReportRankNewVo;
import com.shands.mod.dao.model.wp.CommentReportRankVo;
import com.shands.mod.dao.model.wp.CommentReportScoreNewVo;
import com.shands.mod.dao.model.wp.CommentReportScoreVo;
import com.shands.mod.dao.model.wp.CommentReportTrendVo;
import com.shands.mod.dao.model.wp.CommentRoomCommentVo;
import com.shands.mod.service.BaseHotelInfoCommonService;
import com.shands.mod.util.BaseThreadLocalHelper;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.ApiOperation;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.PropertySource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("/report")
public class ReportController extends BaseController {

  @Resource
  private ModHotelInfoDao modHotelInfoDao;

  @Autowired
  private IModCommentReportService iModCommentReportService;
  @Autowired
  private IModCommentService iModCommentService;
  @Autowired
  private BaseHotelInfoCommonService hotelInfoCommonService;

  @Value("${els.h5url:https://testx-m.kaiyuanhotels.com}")
  private String sxeUrl;
  @Override
  public boolean isPublic() {
    return true;
  }

  @PostMapping(
      value = "/index",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "index", notes = "首页", produces = "application/json")
  public ResultVO<Object> index() {
    IndexTimeVO indexTimeVO = new IndexTimeVO();
    LocalDate maxDate = iModCommentService.getMaxDate();
    indexTimeVO.setArrDate(maxDate);
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(ThreadLocalHelper.getCompanyId());
    // 集团用户需要统计酒店数量,酒店用户不需要
    if ("00001".equals(modHotelInfo.getHotelCode()) || "管理公司".equals(modHotelInfo.getTrade())) {
      int hotelCount = iModCommentService.getHotelCount(maxDate);
      indexTimeVO.setCount(hotelCount);
    }
    return ResultVO.success(indexTimeVO);
  }

  @PostMapping(
      value = "/indexData",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "indexData", notes = "首页数据", produces = "application/json")
  public ResultVO<Object> indexData(@Validated @RequestBody IndexTimeVO indexTimeVO) {
    LocalDate date = indexTimeVO.getArrDate();
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(ThreadLocalHelper.getCompanyId());
    CommentReq commentReq = new CommentReq();
    IndexVO indexVO = new IndexVO();
    List<CommentReportTrendVo> commentReportVos = new ArrayList<>();
    CommentIndexRes commentIndexRes = new CommentIndexRes();
//    modHotelInfo.setHotelCode("KYHZMD");
    // 集团用户
    if ("000001".equals(modHotelInfo.getHotelCode()) || "管理公司".equals(modHotelInfo.getTrade())) {
      if ("left".equals(indexTimeVO.getDesc())) {
        commentReq.setArrDate(date);
        commentReq.setTwoArrDate(date.minusDays(6));
        List<CommentRes> indexScore = iModCommentService.getSorceByChannel(commentReq);
        getIndexInfo(indexVO, indexScore);
        indexVO.setDateTime(
            commentReq.getTwoArrDate().getMonthValue() + "/" + commentReq.getTwoArrDate()
                .getDayOfMonth());
        commentReportVos = iModCommentService.reportTrendScore(date, indexTimeVO.getDateEnum());
      }
      if ("right".equals(indexTimeVO.getDesc())) {
        commentReq.setArrDate(date.minusDays(6));
        commentReq.setEndDate(date);
        commentReq.setTwoArrDate(date.minusDays(13));
        commentReq.setTwoEndDate(date.minusDays(7));
        List<CommentRes> indexScoreBefore = iModCommentService.getCommentByChannel(commentReq);
        getIndexInfo(indexVO, indexScoreBefore);
        commentReportVos = iModCommentService.reportTrendNewScore(date, indexTimeVO.getDateEnum());
      }
      commentIndexRes.setIndexVO(indexVO);
      commentIndexRes.setCommentReportTrendVoList(commentReportVos);
    } else {
      commentReq.setArrDate(date);
      commentReq.setHotelCode(modHotelInfo.getHotelCode());
      List<IndexVO> indexScoreHotel = iModCommentService.indexScoreHotel(commentReq);
      commentIndexRes.setIndexVOList(indexScoreHotel);
    }
    return ResultVO.success(commentIndexRes);
  }


  @PostMapping(
      value = "/reportScoreNow",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "reportScoreNow", notes = "页面分", produces = "application/json")
  public ResultVO<Object> reportScoreNow() {

    List<CommentReportScoreVo> commentReportScoreVos = iModCommentReportService.reportScoreNow();
    return ResultVO.success(commentReportScoreVos);
  }

  @PostMapping(
      value = "/reportScoreBefore",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "reportScoreBefore", notes = "近7天新增网评", produces = "application/json")
  public ResultVO<Object> reportScoreBefore() {

    List<CommentReportScoreNewVo> commentReportScoreNewVos = iModCommentReportService
        .reportScoreBefore();
    return ResultVO.success(commentReportScoreNewVos);
  }

  @PostMapping(
      value = "/reportTrendScore",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "reportTrendScore", notes = "页面分趋势图", produces = "application/json")
  public ResultVO<Object> reportTrendScore(@Valid @RequestBody CommentReportTrendReq req) {

    List<CommentReportTrendVo> commentReportTrendVos = iModCommentReportService
        .reportTrendScore(req);
    return ResultVO.success(commentReportTrendVos);
  }

  @PostMapping(
      value = "/reportTrendNewScore",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "reportTrendNewScore", notes = "新增网评趋势图", produces = "application/json")
  public ResultVO<Object> reportTrendNewScore(@Valid @RequestBody CommentReportTrendReq req) {

    List<CommentReportTrendVo> commentReportTrendVos = iModCommentReportService
        .reportTrendNewScore(req);
    return ResultVO.success(commentReportTrendVos);
  }

  @PostMapping(
      value = "/trendNewScore",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "trendNewScore", notes = "新增网评趋势图报表", produces = "application/json")
  public ResultVO<Object> trendNewScore(@Valid @RequestBody CommentTrendNewScoreReq req) {

    Map<String, CommentReportNewScoreVo> stringCommentReportNewScoreVoMap = iModCommentReportService
        .trendNewScore(req.getDeptCode(),null);
    return ResultVO.success(stringCommentReportNewScoreVoMap);
  }

  @PostMapping(
      value = "/reportCommentRank",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "reportCommentRank", notes = "数据报表网评排行榜页面分", produces = "application/json")
  public ResultVO<Object> reportCommentRank(@Valid @RequestBody CommentReportRankReq req) {

    List<CommentReportRankVo> commentReportRankVos = iModCommentReportService
        .reportCommentRank(req);
    return ResultVO.success(commentReportRankVos);
  }

  @PostMapping(
      value = "/reportCommentRankNew",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "reportCommentRankNew", notes = "数据报表网评排行榜新增", produces = "application/json")
  public ResultVO<Object> reportCommentRankNew(@Valid @RequestBody CommentReportRankReq req) {

    List<CommentReportRankNewVo> commentReportRankVos = iModCommentReportService
        .reportCommentRankNew(req);
    List<CommentReportRankNewVo> collect = null;
    if (req.getOrder().equals("top")) {
      collect = commentReportRankVos.stream()
          .sorted(Comparator.comparing(CommentReportRankNewVo::getRoomComment).reversed()
              .thenComparing(CommentReportRankNewVo::getScore, Comparator.reverseOrder())).collect(
              Collectors.toList());
    }else {
      collect = commentReportRankVos.stream()
          .sorted(Comparator.comparing(CommentReportRankNewVo::getRoomComment)
              .thenComparing(CommentReportRankNewVo::getScore)).collect(
              Collectors.toList());
    }
    List<CommentReportRankNewVo> res = new ArrayList<>();
    for (int i =0;i<(collect.size()>=10 ? 10 :collect.size());i++){
      res.add(collect.get(i));
    }
    return ResultVO.success(res);
  }

  @PostMapping(
      value = "/commentRank",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "commentRank", notes = "网评页面分排行榜", produces = "application/json")
  public ResultVO<Object> commentRank(@Valid @RequestBody CommentRankNowReq req) {

    List<CommentRankVo> commentRankVos = iModCommentReportService.commentRank(req);
    return ResultVO.success(commentRankVos);
  }

  @PostMapping(
      value = "/commentRankNew",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "commentRankNew", notes = "网评新增网评排行榜", produces = "application/json")
  public ResultVO<Object> commentRankNew(@Valid @RequestBody CommentRankReq req)
      throws ExecutionException, InterruptedException {
    List<CommentRankNewVo> res = new ArrayList<>();
    CommentRankNewVo commentRankNewVo1 = null;
    List<CommentRankNewVo> commentRankNewVos = iModCommentReportService.commentRankNew(req);
    if (!req.getType().equals("hotel")) {
      commentRankNewVo1 = commentRankNewVos.get(0);
      commentRankNewVos.remove(commentRankNewVo1);
    }
    for (CommentRankNewVo commentRankNewVo : commentRankNewVos) {
      List<CommentHotelRankNewVo> commentHotelRankNewVoList = commentRankNewVo
          .getCommentHotelRankNewVoList();
      if (commentHotelRankNewVoList != null && commentHotelRankNewVoList.size()>0) {
        commentHotelRankNewVoList = commentHotelRankNewVoList.stream().sorted(
            Comparator.comparing(CommentHotelRankNewVo::getRoomComment).reversed()
                .thenComparing(CommentHotelRankNewVo::getScore, Comparator.reverseOrder()))
            .collect(Collectors.toList());
        commentRankNewVo.setCommentHotelRankNewVoList(commentHotelRankNewVoList);
      }
    }
    commentRankNewVos = commentRankNewVos.stream().sorted(Comparator.comparing(CommentRankNewVo :: getRoomComment).reversed().thenComparing(CommentRankNewVo :: getScore,Comparator.reverseOrder())).collect(Collectors.toList());
    if (!req.getType().equals("hotel")) {
      commentRankNewVos.add(0, commentRankNewVo1);
    }
    return ResultVO.success(commentRankNewVos);
  }

  @PostMapping(
      value = "/commentChannelScore",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "commentChannelScore", notes = "渠道网评页面分", produces = "application/json")
  public ResultVO<Object> commentChannelScore(@Valid @RequestBody CommentChannelNowReq req) {

    List<CommentChannelVo> commentChannelVos = iModCommentReportService.commentChannelScore(req);
    return ResultVO.success(commentChannelVos);
  }


  @PostMapping(
      value = "/commentChannelNew",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "commentChannelNew", notes = "渠道网评新增", produces = "application/json")
  public ResultVO<Object> commentChannelNew(@Valid @RequestBody CommentChannelReq req) {

    List<CommentChannelVo> commentChannelVos = iModCommentReportService.commentChannelNew(req);
    return ResultVO.success(commentChannelVos);
  }


  @PostMapping(
      value = "/getOwnershipList",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "getOwnershipList", notes = "事业部列表", produces = "application/json")
  public ResultVO<Object> getOwnershipList() {

    List<OwnershipRes> ownershipList = OwnershipEnum.getOwnershipList();
    return ResultVO.success(ownershipList);
  }


  private void getIndexInfo(IndexVO indexVO, List<CommentRes> indexScore) {
    for (CommentRes commentRes : indexScore) {
      if ("百达星系".equals(commentRes.getChannel())) {
        indexVO.setBdx(commentRes.getMt());
        indexVO.setBdxhb(commentRes.getDesc());
      }
      if ("官网".equals(commentRes.getChannel())) {
        indexVO.setGw(commentRes.getMt());
        indexVO.setGwhb(commentRes.getDesc());
      }
      if ("携程".equals(commentRes.getChannel())) {
        indexVO.setXc(commentRes.getMt());
        indexVO.setXchb(commentRes.getDesc());
      }
      if ("美团".equals(commentRes.getChannel())) {
        indexVO.setMt(commentRes.getMt());
        indexVO.setMthb(commentRes.getDesc());
      }
    }
  }

  @PostMapping(
      value = "/indexDataV2",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "indexDataV2", notes = "首页数据", produces = "application/json")
  public ResultVO<Object> indexDataV2(@Validated @RequestBody IndexTimeVO indexTimeVO) {
    CommentIndexVo commentIndexVo = new CommentIndexVo();
    LocalDate maxDate = iModCommentService.getMaxDate();
    ModHotelInfo modHotelInfo = hotelInfoCommonService
        .findHotelInfo(Integer.valueOf(BaseThreadLocalHelper.getCompany()));
    CommentReq commentReq = new CommentReq();
    IndexVO indexVO = new IndexVO();
    List<CommentReportTrendVo> commentReportVos = new ArrayList<>();
    CommentIndexRes commentIndexRes = new CommentIndexRes();
    // 集团用户
    if ("000001".equals(modHotelInfo.getHotelCode()) || "管理公司".equals(modHotelInfo.getTrade())) {
      commentIndexVo.setModuleCode(HomeDataEnum.BLOC_COMMENT.getModuleCode());
      commentIndexVo.setModuleName(HomeDataEnum.BLOC_COMMENT.getModuleName());
      // 集团用户需要统计酒店数量,酒店用户不需要
      int hotelCount = iModCommentService.getHotelCount(maxDate);
      Date from = Date.from(maxDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
      commentIndexVo.setModuleDes(HomeDataEnum.BLOC_COMMENT.getModuleDesc().replace("{1}", DateUtil.dateToString(from,DateUtil.DATE_FORMAT_YYYY_MM_DD_N)).replace("{2}", String.valueOf(hotelCount)));
      commentIndexVo.setTemplateDes(DescEnum.BLOC_COMMENT.getDescription());
      commentIndexVo.setUrl(sxeUrl + "/sxe/netComment/memberStatis");
      if ("left".equals(indexTimeVO.getDesc())) {
        commentReq.setArrDate(maxDate);
        commentReq.setTwoArrDate(maxDate.minusDays(6));
        List<CommentRes> indexScore = iModCommentService.getSorceByChannel(commentReq);
        getIndexInfo(indexVO, indexScore);
        indexVO.setDateTime(
            commentReq.getTwoArrDate().getMonthValue() + "/" + commentReq.getTwoArrDate()
                .getDayOfMonth());
        commentReportVos = iModCommentService.reportTrendScore(maxDate, indexTimeVO.getDateEnum());
        //单房网评量
      }
      if ("right".equals(indexTimeVO.getDesc())) {
        commentReq.setArrDate(DateUtil.getStartOrEndDayOfMonth(LocalDate.now(),true));
        commentReq.setEndDate(LocalDate.now());
        commentReq.setTwoArrDate(DateUtil.getStartOrEndDayOfMonth(LocalDate.now().plusMonths(-1),true));
        commentReq.setTwoEndDate(DateUtil.getStartOrEndDayOfMonth(LocalDate.now().plusMonths(-1),false));
        List<CommentRes> indexScoreBefore = iModCommentService.getCommentByChannel(commentReq);
        getIndexInfo(indexVO, indexScoreBefore);
        commentReportVos = iModCommentService.reportTrendNewScore(maxDate, indexTimeVO.getDateEnum());
        CommentRoomCommentVo commentRoomCommentVo = iModCommentService.roomComment(commentReq.getArrDate(),commentReq.getEndDate());
        commentIndexRes.setCommentRoomCommentVo(commentRoomCommentVo);
      }
      commentIndexRes.setIndexVO(indexVO);
      commentIndexRes.setCommentReportTrendVoList(commentReportVos);
    } else {
      //酒店
      commentIndexVo.setModuleCode(HomeDataEnum.HOTEL_COMMENT.getModuleCode());
      commentIndexVo.setModuleName(HomeDataEnum.HOTEL_COMMENT.getModuleName());
      Date from = Date.from(maxDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
      commentIndexVo.setModuleDes(HomeDataEnum.HOTEL_COMMENT.getModuleDesc().replace("{1}", DateUtil.dateToString(from,DateUtil.DATE_FORMAT_YYYY_MM_DD_N)));
      commentIndexVo.setTemplateDes(DescEnum.HOTEL_COMMENT.getDescription());
      commentReq.setArrDate(maxDate);
      commentReq.setHotelCode(modHotelInfo.getHotelCode());
      List<IndexVO> indexScoreHotel = iModCommentService.indexScoreHotel(commentReq);

      Map<String, CommentReportNewScoreVo> stringCommentReportNewScoreVoMap = iModCommentReportService
          .trendNewScore(null, modHotelInfo.getHotelCode());
      commentIndexRes.setRoomReport(stringCommentReportNewScoreVoMap);
      commentIndexRes.setIndexVOList(indexScoreHotel);
    }
    commentIndexVo.setCommentIndexRes(commentIndexRes);
    return ResultVO.success(commentIndexVo);
  }
}
