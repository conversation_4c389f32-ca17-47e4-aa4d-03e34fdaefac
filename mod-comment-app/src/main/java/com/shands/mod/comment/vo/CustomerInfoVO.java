package com.shands.mod.comment.vo;

import com.shands.mod.dao.model.hs.Customer;
import com.shands.mod.util.BaseConstants;
import java.io.Serializable;
import java.util.Date;

/**
 * 客户信息
 *
 * <AUTHOR>
 */
public class CustomerInfoVO implements Serializable {
  private static final long serialVersionUID = -8749460536514856079L;
  private Integer id;
  private String name;
  private Integer sex;
  private String mobile;
  private Date birthday;
  private String idType;
  private String idNum;
  private String userType;
  private String token;
  private Integer status;

  private String shandsOpenid;

  private String gwOpenid;
  private String gwToken;

  /**解密后的明文**/
  private String decryptText;

  private String miniprogramOpenid;
  private String miniprogramSessionKey;
  private String unionid;

  private Integer countVoucher;

  public String getGwToken() {
    return gwToken;
  }

  public void setGwToken(String gwToken) {
    this.gwToken = gwToken;
  }

  public String getMiniprogramOpenid() {
    return miniprogramOpenid;
  }

  public void setMiniprogramOpenid(String miniprogramOpenid) {
    this.miniprogramOpenid = miniprogramOpenid;
  }

  public String getMiniprogramSessionKey() {
    return miniprogramSessionKey;
  }

  public void setMiniprogramSessionKey(String miniprogramSessionKey) {
    this.miniprogramSessionKey = miniprogramSessionKey;
  }

  public String getUnionid() {
    return unionid;
  }

  public void setUnionid(String unionid) {
    this.unionid = unionid;
  }

  public String getDecryptText() {
    return decryptText;
  }

  public void setDecryptText(String decryptText) {
    this.decryptText = decryptText;
  }

  public String getShandsOpenid() {
    return shandsOpenid;
  }

  public void setShandsOpenid(String shandsOpenid) {
    this.shandsOpenid = shandsOpenid;
  }

  public String getGwOpenid() {
    return gwOpenid;
  }

  public void setGwOpenid(String gwOpenid) {
    this.gwOpenid = gwOpenid;
  }

  public CustomerInfoVO() {}

  public CustomerInfoVO(Customer customer) {
    this(customer, null);
  }

  public CustomerInfoVO(Customer customer, String token) {
    if (customer != null) {
      this.setId(customer.getId());
      this.setName(customer.getName());
      this.setSex(customer.getSex());
      this.setMobile(customer.getMobile());
      this.setBirthday(customer.getBirthday());
      this.setUserType(customer.getUserType());
      this.setToken(token);
      this.setGwOpenid(customer.getGwOpenid());
      this.setShandsOpenid(customer.getShandsOpenid());
      this.setStatus(
          customer.getDeleted() != null
                  && customer.getDeleted().intValue() == BaseConstants.DATA_UNDELETED
              ? 1
              : 0);
      this.setMiniprogramOpenid(customer.getMiniprogramOpenid());
      this.setMiniprogramSessionKey(customer.getMiniprogramSessionKey());
      this.setUnionid(customer.getUnionid());
    }
  }

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public Integer getSex() {
    return sex;
  }

  public void setSex(Integer sex) {
    this.sex = sex;
  }

  public String getMobile() {
    return mobile;
  }

  public void setMobile(String mobile) {
    this.mobile = mobile;
  }

  public Date getBirthday() {
    return birthday;
  }

  public void setBirthday(Date birthday) {
    this.birthday = birthday;
  }

  public String getIdType() {
    return idType;
  }

  public void setIdType(String idType) {
    this.idType = idType;
  }

  public String getIdNum() {
    return idNum;
  }

  public void setIdNum(String idNum) {
    this.idNum = idNum;
  }

  public String getUserType() {
    return userType;
  }

  public void setUserType(String userType) {
    this.userType = userType;
  }

  public String getToken() {
    return token;
  }

  public void setToken(String token) {
    this.token = token;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  @Override
  public String toString() {
    return "CustomerInfoVO{" +
        "id=" + id +
        ", name='" + name + '\'' +
        ", sex=" + sex +
        ", mobile='" + mobile + '\'' +
        ", birthday=" + birthday +
        ", idType='" + idType + '\'' +
        ", idNum='" + idNum + '\'' +
        ", userType='" + userType + '\'' +
        ", token='" + token + '\'' +
        ", status=" + status +
        ", shandsOpenid='" + shandsOpenid + '\'' +
        ", gwOpenid='" + gwOpenid + '\'' +
        ", gwToken='" + gwToken + '\'' +
        ", decryptText='" + decryptText + '\'' +
        ", miniprogramOpenid='" + miniprogramOpenid + '\'' +
        ", miniprogramSessionKey='" + miniprogramSessionKey + '\'' +
        ", unionid='" + unionid + '\'' +
        ", countVoucher=" + countVoucher +
        '}';
  }

  public Integer getCountVoucher() {
    return countVoucher;
  }

  public void setCountVoucher(Integer countVoucher) {
    this.countVoucher = countVoucher;
  }
}
