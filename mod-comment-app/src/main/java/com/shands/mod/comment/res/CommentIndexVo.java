package com.shands.mod.comment.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/31 16:47
 */
@Data
public class CommentIndexVo {

  @ApiModelProperty(value = "模板类型")
  private String moduleCode;

  @ApiModelProperty(value = "模板名称")
  private String moduleName;

  @ApiModelProperty(value = "数据描述")
  private String moduleDes;

  @ApiModelProperty(value = "数据描述")
  private String templateDes;

  @ApiModelProperty(value = "跳转地址")
  private String url;

  @ApiModelProperty(value = "网评详情")
  private CommentIndexRes commentIndexRes;
}
