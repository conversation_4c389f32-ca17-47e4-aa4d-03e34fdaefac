package com.shands.mod.pay.service.wxpay.impl;

import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.mapper.PayWxOrderDao;
import com.shands.mod.dao.model.v0701.pojo.PayWxConfig;
import com.shands.mod.dao.model.v0701.pojo.PayWxOrder;
import com.shands.mod.pay.config.WxConfig;
import com.shands.mod.pay.config.WxPayCertConfig;
import com.shands.mod.pay.constant.WxPayConstants;
import com.shands.mod.pay.enums.SignTypeEnum;
import com.shands.mod.pay.handler.WxPayException;
import com.shands.mod.pay.model.dto.RefundorderChannelDto;
import com.shands.mod.pay.model.dto.RefundorderDto;
import com.shands.mod.pay.model.vo.RefundorderChannelVo;
import com.shands.mod.pay.model.vo.RefundorderVo;
import com.shands.mod.pay.service.channel.ChannelConfigService;
import com.shands.mod.pay.service.order.PayRefundService;
import com.shands.mod.pay.service.wxpay.WxRefundService;
import com.shands.mod.pay.util.SnowFlakeID;
import com.shands.mod.pay.util.WXPayRequest;
import com.shands.mod.pay.util.WXPayUtil;
import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.buf.HexUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * @ClassName WxRefundServiceImpl
 * @Description 微信支付-退款申请实现类
 * <AUTHOR>
 * @Date 2021/4/8 10:51
 * @Version 1.0
 */
@Service
@Slf4j
public class WxRefundServiceImpl implements WxRefundService {

  @Value("${wx.refund.notify}")
  private String notifyUrl;

  @Resource
  private PayWxOrderDao payWxOrderDao;

  private final ChannelConfigService channelConfigService;

  private final PayRefundService payRefundService;

  private final WxConfig wxConfig;

  public WxRefundServiceImpl(
      ChannelConfigService channelConfigService,
      PayRefundService payRefundService, WxConfig wxConfig) {
    this.channelConfigService = channelConfigService;
    this.payRefundService = payRefundService;
    this.wxConfig = wxConfig;
  }

  /**
   * <AUTHOR>
   * @Description 微信支付-退款申请
   * 通过应用请求参数 查询支付配置信息 组装推送申请对象参数
   * @Date 2021/4/8 11:26
   * @Param refundorderChannelDto: 渠道请求参数dto对象
   * @return: com.shands.mod.pay.model.vo.RefundorderChannelVo
   **/
  @Override
  public RefundorderChannelVo refundorderForChannel(RefundorderChannelDto refundorderChannelDto) {

    //根据渠道编码查询支付参数配置
    PayWxConfig payWxConfig = channelConfigService.qurPayWxConfig(refundorderChannelDto.getApply_channel());
    if(StringUtils.isEmpty(payWxConfig)){
      log.info("[微信支付][统一下单][下单失败][失败原因：支付参数配置信息查询为空]");
      throw new WxPayException("支付参数配置信息查询为空");
    }

    RefundorderDto refundorderDto = new RefundorderDto();

    SnowFlakeID snowFlakeID = new SnowFlakeID(1, 1);
    long outTradeNo = snowFlakeID.nextId();

    refundorderDto.setAppid(payWxConfig.getAppid());
    refundorderDto.setMch_id(payWxConfig.getMchId());
    refundorderDto.setSub_mch_id(payWxConfig.getSubMchId());
    refundorderDto.setSub_appid(payWxConfig.getSubAppid());

    refundorderDto.setNonce_str(WXPayUtil.generateNonceStr());
    refundorderDto.setSign_type(SignTypeEnum.MD5.getCode());
    refundorderDto.setNotify_url(notifyUrl);
    refundorderDto.setOut_refund_no(String.valueOf(outTradeNo));
    refundorderDto.setRefund_fee(String.valueOf(refundorderChannelDto.getRefund_fee()));
    refundorderDto.setRefund_desc(refundorderChannelDto.getRefund_desc());

    return this.refundorder(refundorderDto,refundorderChannelDto);
  }

  /**
   * <AUTHOR>
   * @Description 微信申请退款实现
   * @Date 2021/4/8 11:27
   * @Param refundorderDto: 支付请求对象
   * @Param refundorderChannelDto: 渠道请求对象
   * @return: null
   **/
  private RefundorderChannelVo refundorder(RefundorderDto refundorderDto, RefundorderChannelDto refundorderChannelDto){

    log.info("[微信支付][退款申请][接口请求数据：{}]", refundorderDto.toString());

    RefundorderVo refundorderVo = new RefundorderVo();
    RefundorderChannelVo channelVo = new RefundorderChannelVo();

    //根据应用方商户订单号，查询是否存在
    Map<String,String> qurParams = new HashMap<>();
    qurParams.put("applyChannel",refundorderChannelDto.getApply_channel());
    qurParams.put("applyTradeNo",refundorderChannelDto.getApply_trade_no());

    PayWxOrder payWxOrder = payWxOrderDao.selectOrderByApplyNo(qurParams);
    if(StringUtils.isEmpty(payWxOrder)){

      channelVo.setResult_code(WxPayConstants.WX_PAY_FAIL);
      channelVo.setResult_msg("支付订单查询为空");

      log.info("[微信支付][退款申请][申请失败：支付订单查询为空]");
      return channelVo;
    }

    //请求参数初始化
    refundorderDto.setTotal_fee(String.valueOf(payWxOrder.getTotalFee()));
    refundorderDto.setOut_trade_no(payWxOrder.getOutTradeNo());

    //获取sign秘钥
    Map<String,String> paramMap = JSONObject
        .parseObject(JSONObject.toJSONString(refundorderDto),Map.class);

    String reqBody;
    try {
      reqBody = WXPayUtil.generateSignedXml(paramMap,wxConfig.getMchKey());
    } catch (Exception e) {
      log.info("[微信支付][退款申请][sign参数转换失败] ", e);
      throw new WxPayException("sign参数转换失败");
    }

    //调用微信退款申请接口
    Map<String,String> respBodyMap;

    try {

      //退款申请证书
      byte[] ary = HexUtils.fromHexString(WxPayCertConfig.certStr);
      String resp = WXPayRequest.requestWithCert(WxPayConstants.REFUNDORDER_URL_SUFFIX, reqBody,false,refundorderDto.getMch_id(),new ByteArrayInputStream(ary));
      log.info("[微信支付][退款申请][接口返回数据 XML：{}]", resp);

      respBodyMap = WXPayUtil.xmlToMap(resp);
      log.info("[微信支付][退款申请][接口返回数据 MAP：{}]", respBodyMap);

    } catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new WxPayException(e.getMessage());
    }

    //判断接口返回状态
    if(!respBodyMap.containsKey("return_code") ||
        !WxPayConstants.WX_PAY_SUCCESS.equals(respBodyMap.get("return_code")) ||
        !WxPayConstants.WX_PAY_SUCCESS.equals(respBodyMap.get("result_code"))){
      log.info("[微信支付][退款申请失败][失败原因：{}]", respBodyMap.get("return_msg"));

      channelVo.setResult_code(WxPayConstants.WX_PAY_FAIL);
      channelVo.setResult_msg(respBodyMap.get("return_msg"));

      log.info("[微信支付][退款申请][下单失败][返回应用方数据：{}]", channelVo.toString());
      return channelVo;
    }

    //判断返回签名是否正确
    try {
      if(!WXPayUtil.isSignatureValid(respBodyMap,wxConfig.getMchKey())){
        throw new WxPayException("[微信支付][退款申请][返回SIGN验签失败]");
      }
    } catch (Exception e) {
      log.error("[微信支付][退款申请][返回SIGN验签失败] ", e);
      throw new WxPayException("sign参数转换失败，失败原因：{}", e.getMessage());
    }

    //返回数据结构转对象
    refundorderVo = JSONObject.parseObject(JSONObject.toJSONString(respBodyMap),RefundorderVo.class);

    log.info("[微信支付][退款申请][申请成功][返回数据：{}]", refundorderVo.toString());

    boolean saveFlag = payRefundService.savePayRefundInfo(refundorderChannelDto,refundorderDto,refundorderVo.getRefund_id());
    if(!saveFlag){
      throw new WxPayException("[微信支付][退款申请][数据落地失败]");
    }

    //同步返回数据
    channelVo.setResult_code(WxPayConstants.WX_PAY_SUCCESS);
    channelVo.setResult_msg(WxPayConstants.WX_PAY_SUCCESS_MSG);

    log.info("[微信支付][退款申请][申请成功][返回应用方数据：{}]", channelVo.toString());
    return channelVo;
  }
}
