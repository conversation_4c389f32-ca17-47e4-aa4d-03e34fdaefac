package com.shands.mod.pay.constant;

import org.apache.http.client.HttpClient;

/**
 * @ClassName WxPayConstants
 * @Description 微信支付常量配置类
 * <AUTHOR>
 * @Date 2021/3/30 12:21
 * @Version 1.0
 */
public class WxPayConstants {

  /** 微信支付请求地址 */
  public static final String DOMAIN_API = "api.mch.weixin.qq.com";

  /** 微信支付统一下单地址 */
  public static final String UNIFIEDORDER_URL_SUFFIX = "/pay/unifiedorder";

  /** 微信支付退款申请地址 */
  public static final String REFUNDORDER_URL_SUFFIX = "/secapi/pay/refund";

  /** 微信支付接口调用返回状态 - 成功 */
  public static final String WX_PAY_SUCCESS = "SUCCESS";

  /** 微信支付下单成功 - 成功 */
  public static final String WX_PAY_SUCCESS_MSG = "OK";

  /** 退款状态 - 成功 */
  public static final String REFUND_SUCCESS = "SUCCESS";

  /** 退款状态 - 关闭 */
  public static final String REFUND_CLOSE = "REFUNDCLOSE";

  /** 退款状态 - 异常 */
  public static final String REFUND_CHANGE = "CHANGE";

  /** 微信支付接口调用返回状态 - 失败 */
  public static final String WX_PAY_FAIL = "FAIL";

  /** 接口请求签名字段 sign */
  public static final String FIELD_SIGN = "sign";

  /** 货币标识 */
  public static final String FEE_TYPE = "CNY";

  /** CONNECT_TIMEOUT 设置连接超时时间，单位毫秒。*/
  public static final int CONNECT_TIMEOUT = 6000;

  /** SOCKET_TIMEOUT 请求获取数据的超时时间(即响应时间)，单位毫秒 */
  public static final int SOCKET_TIMEOUT = 8000;

  public static final String WXPAYSDK_VERSION = "WXPaySDK/3.0.9";

  public static final String USER_AGENT = WXPAYSDK_VERSION +
      " (" + System.getProperty("os.arch") + " " + System.getProperty("os.name") + " " + System.getProperty("os.version") +
      ") Java/" + System.getProperty("java.version") + " HttpClient/" + HttpClient.class.getPackage().getImplementationVersion();


}
