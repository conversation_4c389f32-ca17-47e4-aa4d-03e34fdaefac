package com.shands.mod.pay.filter;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/** 过滤器，用于截取RequestBody */
public class HttpServletRequestFilter implements Filter {

  @Override
  public void init(FilterConfig filterConfig) throws ServletException {}

  @Override
  public void doFilter(
      ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
      throws IOException, ServletException {
    // 防止流读取一次后就没有了, 所以需要将流继续写出去
    HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
    ServletRequest requestWrapper = new BodyReaderHttpServletRequestWrapper(httpServletRequest);
    filterChain.doFilter(requestWrapper, servletResponse);
  }

  @Override
  public void destroy() {}
}
