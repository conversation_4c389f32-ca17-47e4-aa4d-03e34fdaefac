package com.shands.mod.pay.remote.config;

import feign.Request;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Retryer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * <AUTHOR>
 * @date 2021/7/1
 * @desc feigin配置超时时间和请求头转发
*/
@Configuration
public class PayFeignConfigure implements RequestInterceptor {

  /**
   * 链接超时时间
   */
  private static final int CONNECT_TIME_OUT_MILLIS = 30000;
  /**
   * 返回超时时间
   */
  private static final int READ_TIMEOUT_MILLIS = 30000;

  @Bean
  public Request.Options options() {
    return new Request.Options(CONNECT_TIME_OUT_MILLIS, READ_TIMEOUT_MILLIS);
  }

  @Bean
  public Retryer feignRetryer() {
    return new Retryer.Default();
  }

  /**
   * 用来直接转发请求头信息到其他服务
   * @param template
   */
  @Override
  public void apply(RequestTemplate template) {
    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
        .getRequestAttributes();
    if (attributes == null) {
      return;
    }
    HttpServletRequest request = attributes.getRequest();
    Enumeration<String> headerNames = request.getHeaderNames();
    if (headerNames != null) {
      while (headerNames.hasMoreElements()) {
        String name = headerNames.nextElement();
        String values = request.getHeader(name);
        template.header(name, values);
      }
    }
  }
}
