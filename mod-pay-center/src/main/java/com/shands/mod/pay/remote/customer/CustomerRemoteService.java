package com.shands.mod.pay.remote.customer;

import com.shands.mod.pay.model.dto.ChannelNotifyRefundDto;
import com.shands.mod.pay.model.dto.ChannelNotifyUnifiedDto;
import com.shands.mod.pay.model.vo.ChannelNotifyVo;
import com.shands.mod.pay.remote.config.PayFeignConfigure;
import com.shands.mod.pay.remote.customer.fallback.CustomerRemoteServiceFallBack;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "mod-customer-app",fallback = CustomerRemoteServiceFallBack.class,configuration = PayFeignConfigure.class)
public interface CustomerRemoteService {

  /**
   *活动支付回调
   * @param channelNotifyUnifiedDto
   * @return
   */
  @PostMapping("/activity/updateStatus/unifiedNotify")
  ChannelNotifyVo wxPayNotify(@RequestBody ChannelNotifyUnifiedDto channelNotifyUnifiedDto);

  /**
   *活动退款回调
   * @param channelNotifyRefundDto
   * @return
   */
  @PostMapping("/activity/updateStatus/refundNotify")
  ChannelNotifyVo refundNotify(@RequestBody ChannelNotifyRefundDto channelNotifyRefundDto);

}
