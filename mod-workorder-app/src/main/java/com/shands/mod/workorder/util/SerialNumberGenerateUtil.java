package com.shands.mod.workorder.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <Description> 编号生成<br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @createDate 2020/12/07 10:25 上午 <br>
 * @see com.shands.mod.customer.util <br>
 */
public class SerialNumberGenerateUtil {

  /**
   * 根据时间+随机数生成18位编号
   * @return
   */
  public static String generate(){
    DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");//设置日期格式
    String newsNo = LocalDateTime.now().format(fmt);

    return newsNo + getRandomNumber();
  }

  private static int getRandomNumber() {
    return (int) ((Math.random() * 100) % 10);
  }

 /* public static void main(String[] args) {
    System.out.println(generate());
  }*/




}
