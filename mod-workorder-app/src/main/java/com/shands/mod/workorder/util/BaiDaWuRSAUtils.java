package com.shands.mod.workorder.util;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;
import javax.crypto.Cipher;

/**
 * <AUTHOR>
 * @since 2021-12-27 09:23
 */
public class BaiDaWuRSAUtils {
  /**
   * RSA最大加密明文大小
   */
  private static final int MAX_ENCRYPT_BLOCK = 245;

  /**
   * RSA最大解密密文大小
   */
  private static final int MAX_DECRYPT_BLOCK = 256;

  /**
   * 加密&签名类型
   */
  private static final String RSA_INSTANCE = "RSA";
  private static final String CIPHER_INSTANCE = "RSA/ECB/OAEPWITHSHA-256ANDMGF1PADDING";
  private static final String SIGN_INSTANCE = "SHA256WithRSA";

  /**
   * 获取密钥对
   *
   * @return 密钥对
   */
  public static KeyPair getKeyPair() throws Exception {
    KeyPairGenerator generator = KeyPairGenerator.getInstance(RSA_INSTANCE);
    generator.initialize(2048);
    return generator.generateKeyPair();
  }

  /**
   * 获取私钥
   *
   * @param privateKey 私钥字符串
   * @return 私钥
   */
  public static PrivateKey getPrivateKey(String privateKey) throws Exception {
    KeyFactory keyFactory = KeyFactory.getInstance(RSA_INSTANCE);
    byte[] decodedKey = Base64.getDecoder().decode(privateKey.getBytes());
    PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);
    return keyFactory.generatePrivate(keySpec);
  }

  /**
   * 获取公钥
   *
   * @param publicKey 公钥字符串
   * @return 公钥
   */
  public static PublicKey getPublicKey(String publicKey) throws Exception {
    KeyFactory keyFactory = KeyFactory.getInstance(RSA_INSTANCE);
    byte[] decodedKey = Base64.getDecoder().decode(publicKey.getBytes());
    X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decodedKey);
    return keyFactory.generatePublic(keySpec);
  }

  /**
   * RSA加密
   *
   * @param data      待加密数据
   * @param publicKey 公钥
   * @return 密文
   */
  public static String encrypt(String data, PublicKey publicKey) throws Exception {
    Cipher cipher = Cipher.getInstance(CIPHER_INSTANCE);
    cipher.init(Cipher.ENCRYPT_MODE, publicKey);
    int inputLen = data.getBytes().length;
    ByteArrayOutputStream out = new ByteArrayOutputStream();
    int offset = 0;
    byte[] cache;
    int i = 0;
    // 对数据分段加密
    while (inputLen - offset > 0) {
      if (inputLen - offset > MAX_ENCRYPT_BLOCK) {
        cache = cipher.doFinal(data.getBytes(), offset, MAX_ENCRYPT_BLOCK);
      } else {
        cache = cipher.doFinal(data.getBytes(), offset, inputLen - offset);
      }
      out.write(cache, 0, cache.length);
      i++;
      offset = i * MAX_ENCRYPT_BLOCK;
    }
    byte[] encryptedData = out.toByteArray();
    out.close();
    // 获取加密内容使用base64进行编码,并以UTF-8为标准转化成字符串
    // 加密后的字符串
    return Base64.getEncoder().encodeToString(encryptedData);
  }

  /**
   * RSA解密
   *
   * @param data       待解密数据
   * @param privateKey 私钥
   * @return 解密后的内容
   */
  public static String decrypt(String data, PrivateKey privateKey) throws Exception {
    Cipher cipher = Cipher.getInstance(CIPHER_INSTANCE);
    cipher.init(Cipher.DECRYPT_MODE, privateKey);
    byte[] dataBytes = Base64.getDecoder().decode(data);
    int inputLen = dataBytes.length;
    ByteArrayOutputStream out = new ByteArrayOutputStream();
    int offset = 0;
    byte[] cache;
    int i = 0;
    // 对数据分段解密
    while (inputLen - offset > 0) {
      if (inputLen - offset > MAX_DECRYPT_BLOCK) {
        cache = cipher.doFinal(dataBytes, offset, MAX_DECRYPT_BLOCK);
      } else {
        cache = cipher.doFinal(dataBytes, offset, inputLen - offset);
      }
      out.write(cache, 0, cache.length);
      i++;
      offset = i * MAX_DECRYPT_BLOCK;
    }
    out.close();
    // 解密后的内容
    return out.toString(String.valueOf(StandardCharsets.UTF_8));
  }

  /**
   * 签名
   *
   * @param data       待签名数据
   * @param privateKey 私钥
   * @return 签名
   */
  public static String sign(String data, PrivateKey privateKey) throws Exception {
    byte[] keyBytes = privateKey.getEncoded();
    PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
    KeyFactory keyFactory = KeyFactory.getInstance(RSA_INSTANCE);
    PrivateKey key = keyFactory.generatePrivate(keySpec);
    Signature signature = Signature.getInstance(SIGN_INSTANCE);
    signature.initSign(key);
    signature.update(data.getBytes());
    return Base64.getEncoder().encodeToString(signature.sign());
  }

  /**
   * 验签
   *
   * @param srcData   原始字符串
   * @param publicKey 公钥
   * @param sign      签名
   * @return 是否验签通过
   */
  public static boolean verify(String srcData, PublicKey publicKey, String sign) throws Exception {
    byte[] keyBytes = publicKey.getEncoded();
    X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
    KeyFactory keyFactory = KeyFactory.getInstance(RSA_INSTANCE);
    PublicKey key = keyFactory.generatePublic(keySpec);
    Signature signature = Signature.getInstance(SIGN_INSTANCE);
    signature.initVerify(key);
    signature.update(srcData.getBytes());
    return signature.verify(Base64.getDecoder().decode(sign.getBytes()));
  }

  /**
   * 申请密钥对、签名、验签、加密、解密全过程
   */
  /*public static void main(String[] args) {

    try {
      // 生成密钥对
      KeyPair keyPair = getKeyPair();
      String privateKey = new String(Base64.getEncoder().encode(keyPair.getPrivate().getEncoded()));
      String publicKey = new String(Base64.getEncoder().encode(keyPair.getPublic().getEncoded()));
      System.out.println("私钥:" + privateKey);
      System.out.println("公钥:" + publicKey);
      System.out.println("-----------------");

      // 发送报文的数据需要按ASCII字码从小到大排序
      Map<String, Object> requestMap = new TreeMap<>();
      requestMap.put("appId", "000001");
      requestMap.put("appName", "TEST1");
      requestMap.put("appSecret", "XMSQA6CTObOvvGUed6PWUWo");
      requestMap.put("businessId", "1111111111");
      requestMap.put("randomCode", 9999);
      requestMap.put("timestamp", new Date());
      requestMap.put("version", "v1.0.0");

      // 业务请求参数data
      TreeMap<String, Object> data = new TreeMap<>();
      data.put("param1", "第一个参数");
      data.put("param2", "第二个参数");
      data.put("param3", "第三个参数");
      data.put("param4", "第四个参数");

      requestMap.put("data", data);
      String requestStr = JSONObject.toJSONString(requestMap, SerializerFeature.MapSortField);

      // RSA签名
      System.out.println("待签名数据:" + requestStr);
      String sign = sign(requestStr, getPrivateKey(privateKey));
      System.out.println("签名:" + sign);
      System.out.println("-----------------");

      requestMap.put("sign", sign);

      // 接收数据
      String requestString = JSONObject.toJSONString(requestMap, SerializerFeature.MapSortField);
      System.out.println("接收数据（已签名）:" + requestString);
      String requestSign = (String) requestMap.get("sign");
      System.out.println("接收签名:" + requestSign);
      requestMap.remove("sign");
      // RSA验签
      String verifyJson = JSONObject.toJSONString(requestMap, SerializerFeature.MapSortField);
      System.out.println("待验签数据:" + verifyJson);
      boolean result = verify(verifyJson, getPublicKey(publicKey), requestSign);
      System.out.println("验签结果:" + result);
      System.out.println("-----------------");

      // 响应数据
      TreeMap<String, Object> responseMap = new TreeMap<>();
      responseMap.put("code", "10000");
      responseMap.put("msg", "The request is success!");
      responseMap.put("timestamp", new Date());

      // RSA加密
      String responseJson = JSONObject.toJSONString(responseMap, SerializerFeature.MapSortField);
      System.out.println("加密前响应数据:" + responseJson);
      String encryptData = encrypt(responseJson, getPublicKey(publicKey));
      System.out.println("加密后响应数据:" + encryptData);

      // RSA解密
      String decryptData = decrypt(encryptData, getPrivateKey(privateKey));
      System.out.println("解密后响应数据:" + decryptData);

    } catch (Exception e) {
      e.printStackTrace();
    }
  }*/
}
