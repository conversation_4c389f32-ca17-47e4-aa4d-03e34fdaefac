package com.shands.mod.workorder.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.model.workorder.bo.NwWorkOrderTemplateAdd;
import com.shands.mod.dao.model.workorder.bo.NwWorkOrderTemplateQuery;
import com.shands.mod.dao.model.workorder.vo.NwWorkOrderTemplateVo;
import com.shands.mod.util.StringReplaceUtils;
import com.shands.mod.vo.ResultVO;
import com.shands.mod.workorder.service.INwWorkOrderTemplateService;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 */
@RestController
@RequestMapping("/nwWorkOrderTemplate")
@Slf4j
public class NwWorkOrderTemplateController {

  @Autowired
  private INwWorkOrderTemplateService nwWorkOrderTemplateService;

  @PostMapping("/add")
  @ApiOperation(value = "add", notes = "新增模板", produces = "application/json")
  public ResultVO<Boolean> add(@RequestBody @Valid NwWorkOrderTemplateAdd nwWorkOrderTemplateAdd) {
    boolean add = nwWorkOrderTemplateService.add(nwWorkOrderTemplateAdd);
    if (add){
      return ResultVO.success();
    }
    return ResultVO.failed();
  }

  @PostMapping("/update")
  @ApiOperation(value = "update", notes = "修改模板", produces = "application/json")
  public ResultVO<Boolean> update(@RequestBody @Valid NwWorkOrderTemplateAdd nwWorkOrderTemplateAdd) {
    try {
      nwWorkOrderTemplateService.update(nwWorkOrderTemplateAdd);
    }catch (Exception e){
      return ResultVO.failed(e.getMessage());
    }
    return ResultVO.success();

  }

  @GetMapping("/delete")
  @ApiOperation(value = "delete", notes = "删除模板", produces = "application/json")
  public ResultVO<Boolean> delete(Integer templateId) {
    boolean delete = nwWorkOrderTemplateService.delete(templateId);
    if (delete){
      return ResultVO.success();
    }
    return ResultVO.failed();
  }

  @GetMapping("/updateStatus")
  @ApiOperation(value = "updateStatus", notes = "模板状态修改", produces = "application/json")
  public ResultVO<Boolean> updateStatus(@NotNull(message = "模板id不能为空") @RequestParam Integer templateId) {
    try {
      nwWorkOrderTemplateService.updateStatus(templateId);
    }catch (Exception e){
      return ResultVO.failed(e.getMessage());
    }
    return ResultVO.success();
  }

  @GetMapping("/queryPage")
  @ApiOperation(value = "queryPage", notes = "模板分页列表", produces = "application/json")
  public ResultVO<PageInfo<List<NwWorkOrderTemplateVo>>> queryPage(NwWorkOrderTemplateQuery nwWorkOrderTemplateQuery ,Integer pageSize,Integer pageNo){
    List<NwWorkOrderTemplateVo> nwWorkOrderTemplateVos = nwWorkOrderTemplateService
        .queryPage(nwWorkOrderTemplateQuery, pageSize, pageNo);


    if (CollectionUtil.isNotEmpty(nwWorkOrderTemplateVos)) {
      for (NwWorkOrderTemplateVo nwWorkOrderTemplateVo : nwWorkOrderTemplateVos) {
        if (CollectionUtil.isNotEmpty(nwWorkOrderTemplateVo.getModHotelInfoList())) {
          for (ModHotelInfo modHotelInfo : nwWorkOrderTemplateVo.getModHotelInfoList()) {
            modHotelInfo.setApiUrl(StringReplaceUtils
                .replaceNotNull(modHotelInfo.getApiUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

            modHotelInfo.setPosUrl(StringReplaceUtils
                .replaceNotNull(modHotelInfo.getPosUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

            modHotelInfo.setPLogoUrl(StringReplaceUtils
                .replaceNotNull(modHotelInfo.getPLogoUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

            modHotelInfo.setPDescUrl(StringReplaceUtils
                .replaceNotNull(modHotelInfo.getPDescUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

            modHotelInfo.setPQrCodeUrl(StringReplaceUtils
                .replaceNotNull(modHotelInfo.getPQrCodeUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

            modHotelInfo.setPBannerUrl(StringReplaceUtils
                .replaceNotNull(modHotelInfo.getPBannerUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));
          }
        }
      }
    }




    PageInfo pageInfo = new PageInfo<>(nwWorkOrderTemplateVos);
    return ResultVO.success(pageInfo);
  }

  @GetMapping("/list")
  @ApiOperation(value = "list", notes = "模板列表", produces = "application/json")
  public ResultVO<List<NwWorkOrderTemplateVo>> list(Integer hotelId){
    List<NwWorkOrderTemplateVo> nwWorkOrderTemplateVos = nwWorkOrderTemplateService
        .list(hotelId);


    if (CollectionUtil.isNotEmpty(nwWorkOrderTemplateVos)) {
      for (NwWorkOrderTemplateVo nwWorkOrderTemplateVo : nwWorkOrderTemplateVos) {
        if (CollectionUtil.isNotEmpty(nwWorkOrderTemplateVo.getModHotelInfoList())) {
          for (ModHotelInfo modHotelInfo : nwWorkOrderTemplateVo.getModHotelInfoList()) {
            modHotelInfo.setApiUrl(StringReplaceUtils
                .replaceNotNull(modHotelInfo.getApiUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

            modHotelInfo.setPosUrl(StringReplaceUtils
                .replaceNotNull(modHotelInfo.getPosUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

            modHotelInfo.setPLogoUrl(StringReplaceUtils
                .replaceNotNull(modHotelInfo.getPLogoUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

            modHotelInfo.setPDescUrl(StringReplaceUtils
                .replaceNotNull(modHotelInfo.getPDescUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

            modHotelInfo.setPQrCodeUrl(StringReplaceUtils
                .replaceNotNull(modHotelInfo.getPQrCodeUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

            modHotelInfo.setPBannerUrl(StringReplaceUtils
                .replaceNotNull(modHotelInfo.getPBannerUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

          }
        }
      }
    }


    return ResultVO.success(nwWorkOrderTemplateVos);
  }

  @GetMapping("/selectById")
  @ApiOperation(value = "selectById", notes = "模板详情", produces = "application/json")
  public ResultVO<NwWorkOrderTemplateVo> selectById(@NotNull(message = "模板id不能为空") @RequestParam Integer templateId){
    NwWorkOrderTemplateVo nwWorkOrderTemplateVo = nwWorkOrderTemplateService.selectById(templateId);

    List<ModHotelInfo> modHotelInfoList = nwWorkOrderTemplateVo.getModHotelInfoList();
    if (CollectionUtil.isNotEmpty(modHotelInfoList)) {
      for (ModHotelInfo modHotelInfo : modHotelInfoList) {
        modHotelInfo.setApiUrl(StringReplaceUtils
            .replaceNotNull(modHotelInfo.getApiUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

        modHotelInfo.setPosUrl(StringReplaceUtils
            .replaceNotNull(modHotelInfo.getPosUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

        modHotelInfo.setPLogoUrl(StringReplaceUtils
            .replaceNotNull(modHotelInfo.getPLogoUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

        modHotelInfo.setPDescUrl(StringReplaceUtils
            .replaceNotNull(modHotelInfo.getPDescUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

        modHotelInfo.setPQrCodeUrl(StringReplaceUtils
            .replaceNotNull(modHotelInfo.getPQrCodeUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

        modHotelInfo.setPBannerUrl(StringReplaceUtils
            .replaceNotNull(modHotelInfo.getPBannerUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));

      }
    }

    return ResultVO.success(nwWorkOrderTemplateVo);
  }


}
